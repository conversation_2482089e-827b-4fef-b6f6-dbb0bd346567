import { useRouter } from "next/navigation";
import { useCallback } from "react";

export const createInspectoraUrl = (
	asin: string,
	country: string,
	section: "filter" | "result" = "filter"
): string => {
	const baseUrl = "/aep-solutions/platforms";
	const params = new URLSearchParams({
		tab: "inspect-ora",
		section,
		asin: asin.trim(),
		country: country.toLowerCase().trim(),
	});

	return `${baseUrl}?${params.toString()}`;
};

export const useInspectoraNavigation = () => {
	const router = useRouter();

	const navigateToInspectora = useCallback(
		(
			asin: string,
			country: string,
			section: "filter" | "result" = "filter"
		) => {
			const url = createInspectoraUrl(asin, country, section);
			router.push(url);
		},
		[router]
	);

	return { navigateToInspectora, createInspectoraUrl };
};

export const validateAsin = (asin: string): boolean => {
	const asinRegex = /^B[0-9A-Z]{9}$/i;
	return asinRegex.test(asin.trim());
};

export const validateCountry = (country: string): boolean => {
	const validCountries = [
		"us",
		"uk",
		"gb",
		"de",
		"fr",
		"it",
		"es",
		"ca",
		"jp",
	];
	return validCountries.includes(country.toLowerCase().trim());
};

interface InspectoraButtonProps {
	asin: string;
	country: string;
	section?: "filter" | "result";
	children: React.ReactNode;
	className?: string;
	disabled?: boolean;
}

export const InspectoraButton: React.FC<InspectoraButtonProps> = ({
	asin,
	country,
	section = "filter",
	children,
	className = "",
	disabled = false,
}) => {
	const { navigateToInspectora } = useInspectoraNavigation();

	const handleClick = () => {
		if (!disabled && asin && country) {
			navigateToInspectora(asin, country, section);
		}
	};

	return (
		<button
			onClick={handleClick}
			disabled={disabled || !asin || !country}
			className={className}
			type="button">
			{children}
		</button>
	);
};

import Link from "next/link";

interface InspectoraLinkProps {
	asin: string;
	country: string;
	section?: "filter" | "result";
	children: React.ReactNode;
	className?: string;
}

export const InspectoraLink: React.FC<InspectoraLinkProps> = ({
	asin,
	country,
	section = "filter",
	children,
	className = "",
}) => {
	const url = createInspectoraUrl(asin, country, section);

	return (
		<Link href={url} className={className}>
			{children}
		</Link>
	);
};
