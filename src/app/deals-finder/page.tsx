import { Suspense } from "react";
import { Footer } from "@/components/layouts/footer";
import { CTASection } from "./(sections)/cta";
import { FeaturesSection } from "./(sections)/features";
import { Header } from "./(sections)/header";
import { HeroSection } from "./(sections)/hero";
import { HowItWorksSection } from "./(sections)/how-it-work";
import { PricingSection } from "./(sections)/pricing";

// Loading screen component
const PageLoader = () => (
	<div className="flex items-center justify-center min-h-screen bg-slate-900">
		<div className="text-center">
			<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
			<p className="text-slate-400">Loading Deals Finder...</p>
		</div>
	</div>
);

// Main content component
const PageContent = () => (
	<div className="">
		<Header />
		<main>
			<HeroSection />
			<FeaturesSection />
			<HowItWorksSection />
			<PricingSection />
			<CTASection />
		</main>
		<Footer />
	</div>
);

export default function AIDeaFinderPage() {
	return (
		<Suspense fallback={<PageLoader />}>
			<PageContent />
		</Suspense>
	);
}
