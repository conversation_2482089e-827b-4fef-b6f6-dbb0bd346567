"use client";

import React from "react";
import { <PERSON><PERSON>, Wallet, Search, Star, Zap, Gift } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { CreateAccountButton } from "@/components/create-free-account-btn";

interface AIFeature {
	icon: React.ReactNode;
	title: string;
	description: string;
}

const features: AIFeature[] = [
	{
		icon: <Bot className="w-12 h-12 text-green-500" />,
		title: "Your Personal AI Deal Hunter",
		description:
			"Unleash your own smart AI assistant. Train it to meticulously review deals you find, or send it to discover even better or similar opportunities tailored just for you.",
	},
	{
		icon: <Wallet className="w-12 h-12 text-green-500" />,
		title: "Flexible Pay-As-You-Go",
		description:
			"Forget rigid monthly subscriptions. You're in control – only pay for the features and power you actually use, when you use them.",
	},
	{
		icon: <Search className="w-12 h-12 text-green-500" />,
		title: "AI-Powered Product Research",
		description:
			"Make purchasing decisions with confidence. Instruct your AI agents to conduct thorough product investigations, arming you with deep insights for smarter buys.",
	},
	{
		icon: <Star className="w-12 h-12 text-green-500" />,
		title: "Intuitive AI Deal Scoring",
		description:
			"Instantly see a deal's potential. Our easy-to-understand, personalised AI scoring system gives you quick, clear assessments, intelligently weighted towards your unique business priorities.",
	},
	{
		icon: <Zap className="w-12 h-12 text-green-500" />,
		title: "All The Tools, Smarter Price",
		description:
			"Get all the essential features you'd expect from leading calculators, but without the hefty price tag. Enjoy full functionality, paying only for what you need.",
	},
	{
		icon: <Gift className="w-12 h-12 text-green-500" />,
		title: "Unlock More Value: Sell With Us",
		description:
			"Feature your products on our marketplace and we'll double your token allocation. Sell with no upfront fees, just a straightforward, flat 10% commission on successful sales.",
	},
];

export const FeaturesSection: React.FC = () => {
	return (
		<section className="py-24 bg-slate-900" id="features">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				{/* Section Header */}
				<div className="text-center mb-16">
					<div className="w-24 h-1 bg-gradient-to-r from-green-500 to-transparent mx-auto mb-8" />
					<h2 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
						Why Choose The{" "}
						<span className="text-green-500">AI Deal Finder</span>?
					</h2>
					<p className="text-xl text-slate-400 max-w-3xl mx-auto">
						Powerful AI-driven features designed to maximise your
						Clickbuy selling success
					</p>
				</div>

				{/* Features Grid */}
				<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
					{features.map((feature, index) => (
						<Card
							key={index}
							className="p-8 text-center group hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 relative overflow-hidden bg-gradient-to-br from-slate-800 to-slate-700 border border-slate-700 rounded-2xl">
							<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

							<CardContent className="space-y-6 p-0">
								<div className="flex justify-center">
									{feature.icon}
								</div>
								<h3 className="text-xl font-semibold text-slate-50">
									{feature.title}
								</h3>
								<p className="text-slate-400 leading-relaxed">
									{feature.description}
								</p>
							</CardContent>
						</Card>
					))}
				</div>

				{/* CTA Section */}
				<div className="text-center">
					<CreateAccountButton size="lg" className="text-lg px-8" />
				</div>
			</div>
		</section>
	);
};
