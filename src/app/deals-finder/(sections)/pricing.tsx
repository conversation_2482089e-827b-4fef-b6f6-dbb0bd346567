"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Check } from "lucide-react";
import { CreateAccountButton } from "@/components/create-free-account-btn";
import { useAuth } from "@/hooks/use-auth";
import { useRouter } from "next/navigation";

interface PricingTier {
	name: string;
	price: string;
	period: string;
	checkOraRuns: string;
	inspectOraRuns: string;
	isPopular?: boolean;
}

const pricingTiers: PricingTier[] = [
	{
		name: "Light",
		price: "£0.99",
		period: "/one-time",
		checkOraRuns: "50",
		inspectOraRuns: "68",
	},
	{
		name: "Basic",
		price: "£2.99",
		period: "/one-time",
		checkOraRuns: "500",
		inspectOraRuns: "682",
	},
	{
		name: "Standard",
		price: "£9.99",
		period: "/one-time",
		checkOraRuns: "5,000",
		inspectOraRuns: "6,818",
		isPopular: true,
	},
	{
		name: "Premium",
		price: "£14.99",
		period: "/one-time",
		checkOraRuns: "10,000",
		inspectOraRuns: "13,636",
	},
	{
		name: "Ultimate",
		price: "£22.99",
		period: "/one-time",
		checkOraRuns: "20,000",
		inspectOraRuns: "27,273",
	},
];

export const PricingSection: React.FC = () => {
	const { isAuthenticated, isLoadingProfile } = useAuth();
	const router = useRouter();

	const handleGetStarted = () => {
		if (isAuthenticated) {
			router.push("/aep-solutions/platforms?tab=billing&section=pricing");
		} else {
			router.push("/?auth=signup");
		}
	};

	return (
		<section className="py-24 bg-slate-900" id="pricing">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				{/* Section Header */}
				<div className="text-center mb-16">
					<div className="w-24 h-1 bg-gradient-to-r from-green-500 to-transparent mx-auto mb-8" />
					<h2 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
						Token Based Pay As You Go Pricing
					</h2>
					<p className="text-xl text-slate-400 max-w-4xl mx-auto">
						Purchase a credit bundle once. Spend your credits on
						runs for any AI agent. The more you buy, the better the
						value.
					</p>
				</div>

				{/* Pricing Grid */}
				<div className="grid md:grid-cols-2 lg:grid-cols-5 gap-6 mb-12">
					{pricingTiers.map((tier, index) => (
						<Card
							key={index}
							className={`p-6 text-center relative transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 bg-gradient-to-br from-slate-800 to-slate-700 border rounded-2xl ${
								tier.isPopular
									? "border-green-500 shadow-lg shadow-green-500/20"
									: "border-slate-700"
							}`}>
							{tier.isPopular && (
								<div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
									<span className="bg-green-500 text-slate-50 px-3 py-1 rounded-full text-sm font-semibold">
										Popular
									</span>
								</div>
							)}

							<h3 className="text-xl font-semibold text-slate-50 mb-4">
								{tier.name}
							</h3>

							<div className="mb-6">
								<span className="text-3xl font-bold text-green-500">
									{tier.price}
								</span>
								<span className="text-slate-400 text-sm">
									{tier.period}
								</span>
							</div>

							<ul className="space-y-3 mb-6 text-sm">
								<li className="flex items-center justify-center gap-2">
									<Check className="w-4 h-4 text-green-500 flex-shrink-0" />
									<span className="text-slate-300">
										Up to{" "}
										<strong>{tier.checkOraRuns}</strong>{" "}
										CheckOra Runs
									</span>
								</li>
								<li className="text-slate-400 text-xs">OR</li>
								<li className="flex items-center justify-center gap-2">
									<Check className="w-4 h-4 text-green-500 flex-shrink-0" />
									<span className="text-slate-300">
										Up to{" "}
										<strong>{tier.inspectOraRuns}</strong>{" "}
										InspectOra Runs
									</span>
								</li>
								<li className="text-slate-400 text-xs italic mt-2">
									Mix & match as needed.
								</li>
							</ul>

							<Button
								className={`w-full transition-all duration-300 ${
									tier.isPopular
										? "bg-green-500 text-slate-50 hover:bg-green-600 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30"
										: "bg-transparent border border-green-500 text-green-500 hover:bg-green-500 hover:text-slate-50"
								}`}
								onClick={handleGetStarted}
								disabled={isLoadingProfile}>
								{isLoadingProfile
									? "Loading..."
									: "Get Started"}
							</Button>
						</Card>
					))}
				</div>

				{/* Pricing Notice */}
				{/* <div className="max-w-4xl mx-auto mb-12">
					<div className="flex items-start gap-3 p-4 bg-slate-800 border border-slate-700 rounded-lg">
						<Info className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
						<p className="text-slate-300 text-sm">
							<strong>Please Note:</strong> Credits are valid for
							a 30-day period of time. After a 30-day period, the
							credits will expire and new credits will need to be
							purchased.
						</p>
					</div>
				</div> */}

				{/* CTA Section */}
				<div className="text-center">
					<CreateAccountButton size="lg" className="text-lg px-8" />
				</div>
			</div>
		</section>
	);
};
