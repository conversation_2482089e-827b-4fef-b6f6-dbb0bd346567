"use client";

import React from "react";
import { CreateAccountButton } from "@/components/create-free-account-btn";

export const CTASection: React.FC = () => {
	return (
		<section
			id="cta"
			className="py-24 bg-gradient-to-br from-slate-800 to-slate-900 my-16 relative overflow-hidden w-full">
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_30%,rgba(34,197,94,0.15)_0%,transparent_70%),radial-gradient(circle_at_70%_70%,rgba(34,197,94,0.1)_0%,transparent_70%)] pointer-events-none" />

			<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
				<h2 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
					Ready to Find Smarter Deals?
				</h2>

				<h3 className="text-xl lg:text-2xl text-green-500 font-semibold mb-8 max-w-4xl mx-auto">
					Download our AI Deal Finder extension and start discovering
					profitable opportunities today.
				</h3>

				<div className="mb-4">
					<CreateAccountButton
						size="lg"
						className="text-lg px-12 py-6"
					/>
				</div>

				<p className="text-slate-400 mb-4">
					Free trial - No credit card details needed.
				</p>

				<p className="text-lg font-medium text-slate-50">
					Start your AI-powered deal hunting journey in just 30
					seconds.
				</p>
			</div>
		</section>
	);
};
