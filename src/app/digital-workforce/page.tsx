import { Suspense } from "react";
import { AIWorkforceHeroSection } from "./(sections)/hero";
import { AIAdvantageSection } from "./(sections)/advantage";
import { WorkforceAssemblySection } from "./(sections)/workforce";
import { AgentPhilosophySection } from "./(sections)/philosophy";
import { AIVisionSection } from "./(sections)/vision";
import { Header } from "./(sections)/header";
import { Footer } from "@/components/layouts/footer";

// Loading screen component
const PageLoader = () => (
	<div className="flex items-center justify-center min-h-screen bg-slate-900">
		<div className="text-center">
			<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
			<p className="text-slate-400">Loading AI Workforce...</p>
		</div>
	</div>
);

// Main content component
const PageContent = () => (
	<div className="">
		<Header />
		<main>
			<AIWorkforceHeroSection />
			<AIAdvantageSection />
			<WorkforceAssemblySection />
			<AgentPhilosophySection />
			<AIVisionSection />
		</main>
		<Footer />
	</div>
);

export default function AIWorkforcePage() {
	return (
		<Suspense fallback={<PageLoader />}>
			<PageContent />
		</Suspense>
	);
}
