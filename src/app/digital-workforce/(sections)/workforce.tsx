"use client";

import React from "react";
import { CreateAccountButton } from "@/components/create-free-account-btn";

interface AssemblyStep {
	number: number;
	title: string;
	description: string;
}

const steps: AssemblyStep[] = [
	{
		number: 1,
		title: "Hire Existing Agents",
		description:
			"Browse our marketplace of pre-built agents trained for specific, repetitive e-commerce tasks and deploy them instantly.",
	},
	{
		number: 2,
		title: "Apply Your Logic",
		description:
			"Customise any agent with your own unique business guidelines, preferences, and Standard Operating Procedures (SOPs) to make them truly yours.",
	},
	{
		number: 3,
		title: "Request a New Agent",
		description:
			"If an agent doesn't exist, request it. We'll work with you and the community to build it at no development cost to you, providing a simple cost-per-task price for its operation.",
	},
];

export const WorkforceAssemblySection: React.FC = () => {
	return (
		<section
			className="py-24 bg-slate-800 my-16 relative overflow-hidden"
			id="assembly">
			<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent" />

			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="text-center mb-16">
					<h2 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
						How to Assemble Your{" "}
						<span className="text-green-500">Workforce</span>
					</h2>
					<p className="text-xl text-slate-400">
						It&apos;s a simple, collaborative process to build your
						team of AI agents.
					</p>
				</div>

				<div className="max-w-4xl mx-auto space-y-8">
					{steps.map((step, index) => (
						<div
							key={index}
							className="flex items-center gap-8 p-6 bg-gradient-to-br from-slate-700 to-slate-600 border border-slate-600 rounded-xl transition-all duration-300 hover:border-green-500 group">
							<div className="flex-shrink-0">
								<div className="w-12 h-12 bg-green-500 text-slate-50 rounded-full flex items-center justify-center text-xl font-bold group-hover:scale-110 transition-transform duration-300">
									{step.number}
								</div>
							</div>
							<div>
								<h3 className="text-xl font-semibold text-slate-50 mb-2">
									{step.title}
								</h3>
								<p className="text-slate-400">
									{step.description}
								</p>
							</div>
						</div>
					))}
				</div>

				<div className="text-center mt-16">
					<CreateAccountButton size="lg" className="text-lg px-8" />
				</div>
			</div>
		</section>
	);
};
