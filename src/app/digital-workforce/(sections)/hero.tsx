"use client";

import React from "react";
import Image from "next/image";
import { Card } from "@/components/ui/card";
import { CreateAccountButton } from "@/components/create-free-account-btn";

export const AIWorkforceHeroSection: React.FC = () => {
	return (
		<section className="min-h-screen flex items-center pt-16 bg-gradient-to-br from-slate-900 to-slate-800 relative overflow-hidden">
			{/* Background Effects */}
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(34,197,94,0.1)_0%,transparent_50%),radial-gradient(circle_at_75%_75%,rgba(34,197,94,0.05)_0%,transparent_50%)] pointer-events-none" />

			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="grid lg:grid-cols-2 gap-12 items-center">
					{/* Hero Text */}
					<div className="animate-[fadeInUp_0.8s_ease-out]">
						<h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight mb-6 mt-12 md:mt-0">
							<span className="bg-gradient-to-r from-slate-50 to-green-500 bg-clip-text text-transparent">
								Build Your AI Workforce
							</span>
						</h1>

						{/* Added descriptive text */}
						<p className="text-xl text-slate-400 mb-8 leading-relaxed">
							Stop relying on outdated software and manual tasks.
							Hire autonomous AI agents to run your business
							processes with superhuman speed and accuracy.
						</p>

						{/* CTA Button */}
						<div className="flex sm:flex-row gap-4 mb-8">
							<CreateAccountButton
								size="lg"
								className="text-lg px-8"
							/>
						</div>
					</div>

					{/* Hero Visual - Extension Image */}
					<div className="animate-[fadeInUp_0.8s_ease-out] [animation-delay:0.2s]">
						<Card className="p-6 relative overflow-hidden bg-gradient-to-br from-slate-800 to-slate-700 border border-slate-700 rounded-2xl shadow-2xl">
							<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent" />

							{/* Extension Screenshot Container */}
							<div className="relative bg-slate-900 rounded-lg p-4 border border-slate-600">
								<Image
									src="/ai-agent.png"
									alt="Screenshot of Clickbuy Profit Calculator extension"
									width={365}
									height={350}
									className="w-full h-auto rounded-lg"
									priority
								/>
							</div>
						</Card>
					</div>
				</div>
			</div>
		</section>
	);
};
