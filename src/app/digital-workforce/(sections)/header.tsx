"use client";

import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Menu, X, Users, Home } from "lucide-react";
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { CreateAccountButton } from "@/components/create-free-account-btn";

export function Header() {
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
	const [isScrolled, setIsScrolled] = useState(false);

	// Handle scroll effect
	useEffect(() => {
		const handleScroll = () => {
			setIsScrolled(window.scrollY > 0);
		};

		window.addEventListener("scroll", handleScroll);
		return () => window.removeEventListener("scroll", handleScroll);
	}, []);

	// Handle body scroll lock when mobile menu is open
	useEffect(() => {
		if (isMobileMenuOpen) {
			document.body.style.overflow = "hidden";
		} else {
			document.body.style.overflow = "unset";
		}

		return () => {
			document.body.style.overflow = "unset";
		};
	}, [isMobileMenuOpen]);

	// Close mobile menu on escape key
	useEffect(() => {
		const handleEscape = (e: KeyboardEvent) => {
			if (e.key === "Escape" && isMobileMenuOpen) {
				setIsMobileMenuOpen(false);
			}
		};

		document.addEventListener("keydown", handleEscape);
		return () => document.removeEventListener("keydown", handleEscape);
	}, [isMobileMenuOpen]);

	const handleNavClick = () => {
		setIsMobileMenuOpen(false);
	};

	const scrollToTop = () => {
		window.scrollTo({ top: 0, behavior: "smooth" });
	};

	const scrollToSection = (sectionId: string) => {
		const element = document.getElementById(sectionId);
		if (element) {
			element.scrollIntoView({ behavior: "smooth" });
		}
		setIsMobileMenuOpen(false);
	};

	// Navigation items for AI Workforce page
	const navigationItems = [
		{ name: "AI Advantages", id: "advantages" },
		{ name: "How to Assemble", id: "assembly" },
		{ name: "Our Philosophy", id: "philosophy" },
		{ name: "Our Vision", id: "vision" },
	];

	return (
		<>
			<header
				className={cn(
					"fixed top-0 w-full z-50 transition-all duration-300",
					isScrolled
						? "bg-slate-900/95 backdrop-blur-md border-b border-slate-700"
						: "bg-transparent"
				)}>
				<div className="max-w-7xl mx-auto pt-6 pb-4">
					<div className="flex items-center justify-between h-16">
						{/* Logo - scrolls to top */}
						<button
							onClick={scrollToTop}
							className="flex items-center space-x-3 transition-transform hover:scale-105 z-50 relative">
							<Image
								src="/logo.png"
								alt="Screenshot of Clickbuy Profit Calculator extension"
								width={40}
								height={40}
								className="w-full h-20"
								priority
							/>
							<div className="-ml-11">
								<h1 className="text-xl font-semibold text-slate-50">
									ClickBuy
								</h1>
								<span className="text-sm font-medium text-green-500">
									AI Workforce
								</span>
							</div>
						</button>

						{/* Desktop Navigation */}
						<div className="hidden md:flex items-center space-x-8">
							{navigationItems.map((item) => (
								<button
									key={item.name}
									onClick={() => scrollToSection(item.id)}
									className="text-slate-50 hover:text-green-500 transition-colors duration-200 font-medium relative group">
									{item.name}
									<span className="absolute inset-x-0 -bottom-1 h-0.5 bg-green-500 scale-x-0 group-hover:scale-x-100 transition-transform duration-200" />
								</button>
							))}
						</div>

						{/* Desktop Buttons */}
						<div className="hidden md:flex items-center space-x-3">
							<CreateAccountButton
								size="sm"
								className="cursor-pointer"
							/>
							<Link href="/">
								<Button
									size="sm"
									variant="outline"
									className="border-slate-700 bg-transparent text-slate-50 hover:border-green-500 hover:text-green-500 cursor-pointer">
									<Home className="h-4 w-4 mr-2" />
									Home
								</Button>
							</Link>
						</div>

						{/* Mobile Menu Button */}
						<button
							onClick={() =>
								setIsMobileMenuOpen(!isMobileMenuOpen)
							}
							className="md:hidden p-2 rounded-lg transition-colors text-slate-50 hover:bg-slate-800 z-50 relative"
							aria-label="Toggle navigation menu">
							{isMobileMenuOpen ? (
								<X size={24} />
							) : (
								<Menu size={24} />
							)}
						</button>
					</div>
				</div>

				{/* Mobile Sidebar Overlay */}
				{isMobileMenuOpen && (
					<div
						className="fixed inset-0 bg-black/50 z-40 md:hidden"
						onClick={() => setIsMobileMenuOpen(false)}
					/>
				)}

				{/* Mobile Sidebar */}
				<div
					className={`
          fixed top-0 right-0 h-full w-[85vw] max-w-sm bg-slate-900 border-l border-slate-700 z-50 md:hidden
          transform transition-transform duration-300 ease-in-out
          ${isMobileMenuOpen ? "translate-x-0" : "translate-x-full"}
        `}>
					<div className="flex flex-col h-full">
						{/* Header with close button */}
						<div className="flex items-center justify-between p-6 border-b border-slate-700">
							<div className="flex items-center space-x-2">
								<Users className="w-6 h-6 text-green-500" />
								<span className="text-lg font-semibold text-slate-50">
									AI Workforce
								</span>
							</div>
							<button
								onClick={() => setIsMobileMenuOpen(false)}
								className="p-2 rounded-lg transition-colors text-slate-400 hover:bg-slate-800 hover:text-slate-50"
								aria-label="Close navigation menu">
								<X size={20} />
							</button>
						</div>

						{/* Navigation Menu */}
						<div className="flex-1 px-6 py-6">
							<nav className="flex flex-col space-y-1">
								{navigationItems.map((item) => (
									<button
										key={item.name}
										onClick={() => scrollToSection(item.id)}
										className="flex items-center px-4 py-3 text-slate-400 hover:text-slate-50 hover:bg-slate-800 rounded-lg transition-all text-base font-medium w-full text-left">
										{item.name}
									</button>
								))}
							</nav>
						</div>

						{/* Mobile Buttons */}
						<div className="p-6 border-t border-slate-700 space-y-3">
							<CreateAccountButton size="lg" fullWidth />
							<Link href="/" onClick={handleNavClick}>
								<Button
									size="lg"
									variant="outline"
									className="w-full border-slate-700 bg-transparent text-slate-50 hover:border-green-500 hover:text-green-500">
									<Home className="h-4 w-4 mr-2" />
									Back to Home
								</Button>
							</Link>
						</div>
					</div>
				</div>
			</header>
		</>
	);
}
