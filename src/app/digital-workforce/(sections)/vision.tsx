"use client";

import React from "react";
import { CreateAccountButton } from "@/components/create-free-account-btn";

export const AIVisionSection: React.FC = () => {
	return (
		<section
			id="vision"
			className="py-24 bg-gradient-to-br from-slate-800 to-slate-900 my-16 relative overflow-hidden w-full">
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_30%,rgba(34,197,94,0.15)_0%,transparent_70%),radial-gradient(circle_at_70%_70%,rgba(34,197,94,0.1)_0%,transparent_70%)] pointer-events-none" />

			<div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
				<div className="w-24 h-1 bg-gradient-to-r from-green-500 to-transparent mx-auto mb-8" />

				<h2 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
					Our Vision for AEP:{" "}
					<span className="text-green-500">The AI-Only Business</span>
				</h2>

				<p className="text-lg lg:text-xl text-slate-300 leading-relaxed max-w-4xl mx-auto mb-12">
					Our ultimate vision is to guide your business on the journey
					from a legacy model to an AI-First operation, and
					eventually, to a fully autonomous AI-Only enterprise.
					We&apos;re building a future where the AI not only works{" "}
					<em className="text-green-500">in</em> your business but{" "}
					<em className="text-green-500">on</em> your
					business—automating complex orchestration and strategic
					functions. This transition will elevate your role from a
					hands-on CEO to a true shareholder, overseeing a profitable,
					self-running company.
				</p>

				{/* CTA Section */}
				<div className="text-center">
					<CreateAccountButton size="lg" className="text-lg px-8" />
				</div>
			</div>
		</section>
	);
};
