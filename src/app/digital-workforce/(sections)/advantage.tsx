"use client";

import React from "react";
import { Infinity, Network, PoundSterling } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { CreateAccountButton } from "@/components/create-free-account-btn";

interface AIAdvantage {
	icon: React.ReactNode;
	title: string;
	description: string;
}

const advantages: AIAdvantage[] = [
	{
		icon: <Infinity className="w-12 h-12 text-green-500" />,
		title: "Infinite Scaling",
		description:
			"Instantly deploy hundreds of agents to execute tasks simultaneously. Your operational capacity is no longer limited by headcount or time zones.",
	},
	{
		icon: <Network className="w-12 h-12 text-green-500" />,
		title: "Exponential Intelligence",
		description:
			"AI capabilities are seeing a 400% increase year-on-year. Your workforce gets smarter and more efficient automatically, keeping you ahead of the curve.",
	},
	{
		icon: <PoundSterling className="w-12 h-12 text-green-500" />,
		title: "Dramatic Cost Reduction",
		description:
			"Slash overhead by replacing expensive software subscriptions and manual labour. Benefit from AI token costs that are decreasing by nearly 100% annually.",
	},
];

export const AIAdvantageSection: React.FC = () => {
	return (
		<section className="py-24 bg-slate-900" id="advantages">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				{/* Section Header */}
				<div className="text-center mb-16">
					<div className="w-24 h-1 bg-gradient-to-r from-green-500 to-transparent mx-auto mb-8" />
					<h2 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
						The <span className="text-green-500">AI-First</span>{" "}
						Advantage
					</h2>
					<p className="text-xl text-slate-400 max-w-3xl mx-auto">
						Transition from a traditional business to an AI-powered
						operation and unlock unprecedented growth.
					</p>
				</div>

				{/* Advantages Grid */}
				<div className="grid md:grid-cols-3 gap-8 mb-16">
					{advantages.map((advantage, index) => (
						<Card
							key={index}
							className="p-8 text-center group hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 relative overflow-hidden bg-gradient-to-br from-slate-800 to-slate-700 border border-slate-700 rounded-2xl">
							<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

							<CardContent className="space-y-6 p-0">
								<div className="flex justify-center">
									{advantage.icon}
								</div>
								<h3 className="text-xl font-semibold text-slate-50">
									{advantage.title}
								</h3>
								<p className="text-slate-400 leading-relaxed">
									{advantage.description}
								</p>
							</CardContent>
						</Card>
					))}
				</div>

				{/* CTA Section */}
				<div className="text-center">
					<CreateAccountButton size="lg" className="text-lg px-8" />
				</div>
			</div>
		</section>
	);
};
