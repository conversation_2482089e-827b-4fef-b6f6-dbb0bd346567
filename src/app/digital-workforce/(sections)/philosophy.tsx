"use client";

import React from "react";
import { UserCog, Workflow } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { CreateAccountButton } from "@/components/create-free-account-btn";

interface PhilosophyConcept {
	icon: React.ReactNode;
	title: string;
	description: string;
}

const concepts: PhilosophyConcept[] = [
	{
		icon: <UserCog className="w-12 h-12 text-green-500" />,
		title: "NVA: Narrow Vertical Agent",
		description:
			"Each agent is a specialist, what we call a Narrow Vertical Agent (NVA). It's designed to perform one specific, repetitive task with exceptional accuracy and efficiency, just like the perfect employee for a single, focused role.",
	},
	{
		icon: <Workflow className="w-12 h-12 text-green-500" />,
		title: "Orchestration: Agents Working in Concert",
		description:
			"Complex workflows are automated by chaining multiple NVAs together in a process we call Orchestration. You manage the high-level strategy, and the agents execute the ground-level tasks in perfect sync, enabling you to 10x, 20x, or even 100x your output.",
	},
];

export const AgentPhilosophySection: React.FC = () => {
	return (
		<section className="py-24 bg-slate-900" id="philosophy">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				{/* Section Header */}
				<div className="text-center mb-16">
					<div className="w-24 h-1 bg-gradient-to-r from-green-500 to-transparent mx-auto mb-8" />
					<h2 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
						Our Agent Philosophy
					</h2>
					<p className="text-xl text-slate-400 max-w-3xl mx-auto">
						We build specialised agents that work together to create
						a powerful, automated system.
					</p>
				</div>

				{/* Philosophy Grid */}
				<div className="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto mb-16">
					{concepts.map((concept, index) => (
						<Card
							key={index}
							className="p-8 text-center group hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 relative overflow-hidden bg-gradient-to-br from-slate-800 to-slate-700 border border-slate-700 rounded-2xl">
							<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

							<CardContent className="space-y-6 p-0">
								<div className="flex justify-center">
									{concept.icon}
								</div>
								<h3 className="text-xl font-semibold text-slate-50">
									{concept.title}
								</h3>
								<p className="text-slate-400 leading-relaxed">
									{concept.description}
								</p>
							</CardContent>
						</Card>
					))}
				</div>

				{/* CTA Section */}
				<div className="text-center">
					<CreateAccountButton size="lg" className="text-lg px-8" />
				</div>
			</div>
		</section>
	);
};
