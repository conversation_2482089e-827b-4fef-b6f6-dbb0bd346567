import { Suspense } from "react";
import { HeroSection } from "@/components/sections/hero";
import { AEPDefinitionSection } from "@/components/sections/aep-definition";
import { BenefitsSection } from "@/components/sections/benefit";
import { FeaturesSection } from "@/components/sections/features";
import { CTASection } from "@/components/sections/cta";
import { Header } from "@/components/sections/header";
import { Footer } from "@/components/layouts/footer";

// Loading screen component
const PageLoader = () => (
	<div className="flex items-center justify-center min-h-screen bg-slate-900">
		<div className="text-center">
			<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
			<p className="text-slate-400">Loading ClickBuy...</p>
		</div>
	</div>
);

// Main content component
const PageContent = () => (
	<div className="">
		<Header />
		<main>
			<HeroSection />
			<AEPDefinitionSection />
			<BenefitsSection />
			<FeaturesSection />
			<CTASection />
		</main>
		<Footer />
	</div>
);

export default function HomePage() {
	return (
		<Suspense fallback={<PageLoader />}>
			<PageContent />
		</Suspense>
	);
}
