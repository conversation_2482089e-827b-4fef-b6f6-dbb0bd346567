"use client";

import React from "react";
import Image from "next/image";
import { CheckCircle } from "lucide-react";
import { motion } from "framer-motion";
import { CreateAccountButton } from "@/components/create-free-account-btn";

const techLogos = [
	{ src: "/logos/azure.svg", alt: "Microsoft Azure" },
	{ src: "/logos/openai.svg", alt: "OpenAI" },
	{ src: "/logos/odoo.svg", alt: "Odoo" },
	{ src: "/logos/apple-intelligence.png", alt: "Apple Intelligence" },
	{ src: "/logos/claude.svg", alt: "Claude" },
];

const features = [
	"Microsoft-backed innovation",
	"Early access to advanced AI tools",
	"Smarter automation. Bigger reach.",
];

export function AIAdvantageSection() {
	return (
		<section
			className="py-24 bg-slate-800 my-16 relative overflow-hidden"
			id="ai-advantage">
			<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent" />
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="text-center mb-16">
					<h2 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
						Backed by Microsoft.{" "}
						<span className="text-green-500">
							Built for Smarter AI Selling.
						</span>
					</h2>

					<p className="text-xl text-slate-400 max-w-3xl mx-auto leading-relaxed">
						We&apos;re proud to be a{" "}
						<strong className="text-slate-300">
							Microsoft AI Startup Partner
						</strong>
						, funded and supported as we build the world&apos;s
						first fully automated{" "}
						<strong className="text-green-500">
							Agentic E-Commerce Platform (AEP)
						</strong>
						.
					</p>
				</div>

				<div className="max-w-4xl mx-auto space-y-8 mb-16">
					{features.map((text, index) => (
						<div
							key={index}
							className="flex items-center gap-8 p-6 bg-gradient-to-br from-slate-700 to-slate-600 border border-slate-600 rounded-xl transition-all duration-300 hover:border-green-500 group">
							<div className="flex-shrink-0">
								<CheckCircle className="w-12 h-12 text-green-500 group-hover:scale-110 transition-transform duration-300" />
							</div>
							<div>
								<span className="text-xl font-semibold text-slate-50 leading-relaxed">
									{text}
								</span>
							</div>
						</div>
					))}
				</div>

				{/* Technology Partners Section */}
				<div className="text-center mb-16">
					<h3 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
						Platform{" "}
						<span className="text-green-500">
							Technology Partners
						</span>
					</h3>

					<p className="text-lg text-slate-400 max-w-3xl mx-auto mb-12 leading-relaxed">
						Our platform promotes your products across both
						traditional search engines and emerging AI assistants
						like OpenAI, Apple Intelligence, and Claude — so your
						products show up where customers are searching now.
					</p>

					{/* Tech Logos */}
					<div className="flex flex-wrap items-center justify-center gap-8 lg:gap-12">
						{techLogos.map(({ src, alt }, index) => (
							<motion.div
								key={alt}
								className="relative group"
								initial={{
									scale: 1,
									filter: "grayscale(100%)",
								}}
								animate={{
									scale: [1, 1.1, 1],
									filter: [
										"grayscale(100%)",
										"grayscale(0%)",
										"grayscale(100%)",
									],
								}}
								transition={{
									duration: 3,
									repeat: Infinity,
									delay: index * 0.3,
									ease: "easeInOut",
								}}>
								<div className="p-4 rounded-lg bg-slate-800/50 border border-slate-700/50 hover:border-green-500/50 transition-all duration-300">
									<Image
										src={src}
										alt={alt}
										width={100}
										height={40}
										className="object-contain opacity-70 group-hover:opacity-100 transition-opacity duration-300"
									/>
								</div>
							</motion.div>
						))}
					</div>
				</div>

				<div className="text-center">
					<CreateAccountButton size="lg" className="text-lg px-8" />
				</div>
			</div>
		</section>
	);
}
