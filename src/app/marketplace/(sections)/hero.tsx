"use client";

import React from "react";
import Image from "next/image";
import { Card } from "@/components/ui/card";
import { CreateAccountButton } from "@/components/create-free-account-btn";

const HeroImageSection = () => {
	return (
		<div className="relative w-full flex items-center justify-center mb-30 md:mb-0">
			{/* Device Mockups Container */}
			<div className="relative flex items-center justify-center w-fit">
				{/* Desktop Device */}
				<Card className="relative z-0 w-80 -mr-12 -mt-16 md:w-[28rem] lg:w-[32rem] lg:-mr-42 overflow-hidden bg-gradient-to-br from-slate-800 to-slate-700 border border-slate-700 rounded-2xl shadow-2xl">
					<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent" />
					<Image
						src="/image1.png"
						alt="Desktop marketplace view"
						width={800}
						height={600}
						className="object-cover w-full h-auto"
					/>
				</Card>

				{/* Mobile Device Positioned Over Desktop */}
				<Card className="absolute z-10 w-32 md:w-48 lg:w-56 overflow-hidden bg-gradient-to-br from-slate-800 to-slate-700 border border-slate-700 rounded-2xl shadow-2xl top-10 left-[-3rem] sm:left-[-4rem] md:left-[-5rem] lg:left-[-8rem] lg:mt-5">
					<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent" />
					<Image
						src="/mobile1.png"
						alt="Mobile marketplace view"
						width={300}
						height={600}
						className="object-cover w-full h-auto"
					/>
				</Card>
			</div>
		</div>
	);
};

export const HeroSection: React.FC = () => {
	return (
		<section className="min-h-screen flex items-center pt-16 bg-gradient-to-br from-slate-900 to-slate-800 relative overflow-hidden">
			{/* Background Effects */}
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(34,197,94,0.1)_0%,transparent_50%),radial-gradient(circle_at_75%_75%,rgba(34,197,94,0.05)_0%,transparent_50%)] pointer-events-none" />

			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="grid lg:grid-cols-2 gap-12 items-center">
					{/* Hero Text */}
					<div className="animate-[fadeInUp_0.8s_ease-out]">
						{/* Tag
						<div className="inline-flex items-center px-4 py-2 rounded-full bg-slate-800/50 border border-slate-700/50 text-green-500 text-sm font-medium mb-6">
							ClickBuy Deals
						</div> */}

						<h1 className="text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight mb-6 mt-12 md:mt-0">
							<span className="bg-gradient-to-r from-slate-50 to-green-500 bg-clip-text text-transparent">
								We only win when you do
							</span>
							<br />
							<span className="text-slate-300">
								— no sales, no fees.
							</span>
						</h1>

						<p className="text-xl text-slate-400 mb-8 leading-relaxed">
							Sell your Amazon and eBay stock to millions of UK
							customers. List your products on ClickBuy Deals in
							under 5 minutes with zero upfront listing fees —
							fast, easy, and risk-free.
						</p>

						{/* CTA Button */}
						<div className="flex sm:flex-row gap-4 mb-8">
							<CreateAccountButton
								size="lg"
								className="text-lg px-8"
							/>
						</div>

						{/* Subtext */}
						<p className="text-lg text-slate-400 leading-relaxed">
							No upfront fees, no monthly subscriptions. Only pay
							when you sell with our flat 10% commission.
						</p>
					</div>

					{/* Hero Visual - Device Mockups */}
					<div className="animate-[fadeInUp_0.8s_ease-out] [animation-delay:0.2s] my-10">
						<HeroImageSection />
					</div>
				</div>
			</div>
		</section>
	);
};
