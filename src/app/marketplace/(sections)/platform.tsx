"use client";

import React from "react";
import { CheckCircle } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { CreateAccountButton } from "@/components/create-free-account-btn";

interface Feature {
	title: string;
	description: string;
}

const features: Feature[] = [
	{
		title: "No Upfront Fees",
		description:
			"Start selling without any initial costs. No membership, listing, or subscription fees. We only get paid when you do.",
	},
	{
		title: "Flat 10% Commission",
		description:
			"Simple, transparent pricing. A single, low commission rate across all categories. No complicated calculations.",
	},
	{
		title: "Get Listed Fast",
		description:
			"Connect your existing Amazon or eBay accounts and start listing products in under 5 minutes.",
	},
	{
		title: "AI-Powered Promotion",
		description:
			"Reach customers through traditional search & a strategic focus on emerging AI personal assistant platforms.",
	},
];

export function PlatformSection() {
	return (
		<section className="py-24 bg-slate-900" id="why-clickbuy">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				{/* Section Header */}
				<div className="text-center mb-16">
					<div className="w-24 h-1 bg-gradient-to-r from-green-500 to-transparent mx-auto mb-8" />
					<h2 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
						Sell Your Products on Our Platform –{" "}
						<span className="text-green-500">Here&apos;s Why</span>
					</h2>
				</div>

				{/* Features Grid */}
				<div className="grid md:grid-cols-2 gap-8 mb-16">
					{features.map((feature, index) => (
						<Card
							key={index}
							className="p-8 text-center group hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 relative overflow-hidden bg-gradient-to-br from-slate-800 to-slate-700 border border-slate-700 rounded-2xl">
							<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

							<CardContent className="space-y-6 p-0">
								<div className="flex justify-center">
									<CheckCircle className="w-12 h-12 text-green-500" />
								</div>
								<h3 className="text-xl font-semibold text-slate-50">
									{feature.title}
								</h3>
								<p className="text-slate-400 leading-relaxed">
									{feature.description}
								</p>
							</CardContent>
						</Card>
					))}
				</div>

				{/* CTA Section */}
				<div className="text-center">
					<CreateAccountButton size="lg" className="text-lg px-8" />
				</div>
			</div>
		</section>
	);
}
