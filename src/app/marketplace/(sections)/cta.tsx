"use client";

import React from "react";
import { CreateAccountButton } from "@/components/create-free-account-btn";

export const CTASection = () => {
	return (
		<section
			id="contact"
			className="py-24 bg-gradient-to-br from-slate-800 to-slate-900 my-16 relative overflow-hidden w-full">
			<div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_30%,rgba(34,197,94,0.15)_0%,transparent_70%),radial-gradient(circle_at_70%_70%,rgba(34,197,94,0.1)_0%,transparent_70%)] pointer-events-none" />

			<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
				<h2 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
					Have Questions? We&apos;re Here to Help.
				</h2>

				<h3 className="text-xl lg:text-2xl text-green-500 font-semibold mb-8 max-w-4xl mx-auto">
					If you have any questions about selling on Clickbuy Deals or
					need assistance, our dedicated team is ready to support you.
				</h3>

				<div className="flex flex-col md:flex-row items-center justify-center gap-6 mb-4">
					<CreateAccountButton
						size="lg"
						className="text-lg px-12 py-6"
					/>
					{/* <Link href="mailto:<EMAIL>">
						<Button
							size="lg"
							variant="outline"
							className="text-lg px-12 py-6 border-slate-700 bg-transparent text-slate-50 hover:border-green-500 hover:text-green-500 cursor-pointer">
							Email Support
						</Button>
					</Link> */}
					{/* <Link
						href="#"
						className="text-green-500 underline hover:text-green-400 transition-colors text-lg font-medium"></Link> */}
				</div>

				<p className="text-slate-400 mb-4">
					Get expert guidance for your selling journey.
				</p>

				<p className="text-lg font-medium text-slate-50">
					We&apos;re here to help you succeed on our platform.
				</p>
			</div>
		</section>
	);
};
