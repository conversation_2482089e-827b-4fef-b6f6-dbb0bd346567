"use client";

import React from "react";
import { Card } from "@/components/ui/card";
import { CheckCircle, Info } from "lucide-react";
import { CreateAccountButton } from "@/components/create-free-account-btn";

const ListItem = ({ children }: { children: React.ReactNode }) => (
	<li className="flex items-start gap-2">
		<CheckCircle className="text-green-500 mt-1 w-5 h-5" />
		<span className="text-slate-300">{children}</span>
	</li>
);

export const PricingSection = ({
	sectionClass = "",
}: {
	sectionClass?: string;
}) => (
	<section id="pricing" className={`py-24 bg-slate-900 ${sectionClass}`}>
		<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			{/* Section Header */}
			<div className="text-center mb-16">
				<div className="w-24 h-1 bg-gradient-to-r from-green-500 to-transparent mx-auto mb-8" />
				<h2 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
					A Marketplace Built for{" "}
					<span className="text-green-500">Fair Selling</span>
				</h2>
				<p className="text-xl text-slate-400 max-w-4xl mx-auto">
					We&apos;re committed to protecting both sellers and buyers.
					Unlike other platforms, our strict 30-day refund policy only
					applies to valid, justifiable reasons:
				</p>
			</div>

			{/* Refund Policy List */}
			<ul className="grid gap-4 md:grid-cols-3 text-left max-w-5xl mx-auto text-lg mb-16">
				<ListItem>
					Product clearly defective (manufacturer guarantee applies).
				</ListItem>
				<ListItem>Product not genuine or not as described.</ListItem>
				<ListItem>Product returned in original condition.</ListItem>
			</ul>

			{/* Features Grid */}
			<div className="grid gap-8 md:grid-cols-2 max-w-4xl mx-auto mb-16">
				<Card className="p-6 text-center bg-gradient-to-br from-slate-800 to-slate-700 border border-slate-700 rounded-2xl shadow-2xl hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">
					<h3 className="text-2xl font-semibold mb-4 text-slate-50">
						No Contracts, No Upselling, Just Sales
					</h3>
					<p className="text-slate-400 leading-relaxed">
						You&apos;re in full control. Join or leave at any time
						by publishing or unpublishing your offers. The lowest
						priced option is shown first, always — no premium
						placements, no upsells.
					</p>
				</Card>

				<Card className="p-6 text-center bg-gradient-to-br from-slate-800 to-slate-700 border border-slate-700 rounded-2xl shadow-2xl hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">
					<h3 className="text-2xl font-semibold mb-4 text-slate-50">
						Efficient & Timely Payments
					</h3>
					<p className="text-slate-400 leading-relaxed">
						Receive your earnings promptly. We process payments 10
						days after each completed sale to ensure delivery has
						been confirmed. Funds are sent directly to your bank
						account.
					</p>
				</Card>
			</div>

			{/* Pricing Highlight */}
			<Card className="bg-gradient-to-br from-slate-800 to-slate-700 border border-green-500 rounded-2xl p-8 max-w-5xl mx-auto shadow-2xl mb-16">
				<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent" />

				<h3 className="text-3xl font-bold mb-4 text-center text-slate-50">
					Our Transparent & Fair Pricing
				</h3>
				<p className="text-center text-lg mb-6 text-slate-400">
					We believe AI should reduce your costs — not inflate them.
					That&apos;s why we offer the fairest pricing model in the
					UK.
				</p>
				<div className="text-center text-5xl font-extrabold mb-2 text-green-500">
					10%
				</div>
				<p className="text-center text-xl font-medium mb-4 text-slate-300">
					Flat Commission
				</p>
				<p className="text-center mb-6 text-slate-400">
					You only pay if you sell. No hidden fees, no fine print:
				</p>
				<ul className="grid md:grid-cols-2 gap-3 text-lg max-w-2xl mx-auto text-left mb-8">
					<ListItem>No Membership Fees</ListItem>
					<ListItem>No Signup Fees</ListItem>
					<ListItem>No Listing Fees</ListItem>
					<ListItem>No Fees if You Don&apos;t Succeed</ListItem>
				</ul>
				<p className="text-center text-lg text-slate-300 font-medium">
					Clean. Simple. Honest. Just 10% — that&apos;s it.
				</p>
			</Card>

			{/* Pricing Notice */}
			<div className="max-w-4xl mx-auto mb-12">
				<div className="flex items-start gap-3 p-4 bg-slate-800 border border-slate-700 rounded-lg">
					<Info className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
					<p className="text-slate-300 text-sm">
						<strong>Please Note:</strong> All pricing is transparent
						with no hidden fees. We only succeed when you succeed.
					</p>
				</div>
			</div>

			{/* CTA Section */}
			<div className="text-center">
				<CreateAccountButton size="lg" className="text-lg px-8" />
			</div>
		</div>
	</section>
);

export default PricingSection;
