"use client";

import React from "react";
import { CheckCircle } from "lucide-react";
import { CreateAccountButton } from "@/components/create-free-account-btn";

const Step = ({ children }: { children: React.ReactNode }) => (
	<li className="flex items-start gap-3">
		<CheckCircle className="text-green-500 mt-1 w-5 h-5" />
		<span className="text-slate-300">{children}</span>
	</li>
);

export const StartSellingCTA = () => {
	return (
		<section id="cta" className="py-24 bg-slate-900">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="max-w-5xl mx-auto">
					{/* Section Header */}
					<div className="text-center mb-16">
						<div className="w-24 h-1 bg-gradient-to-r from-green-500 to-transparent mx-auto mb-8" />
						<h2 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
							How to Sign Up and{" "}
							<span className="text-green-500">
								Start Selling
							</span>
						</h2>
						<p className="text-xl text-slate-400 max-w-4xl mx-auto">
							Follow these simple steps to get your products
							listed on Clickbuy Deals and reach millions of UK
							customers:
						</p>
					</div>

					{/* Steps */}
					<ul className="space-y-4 text-lg mb-16 max-w-4xl mx-auto">
						<Step>
							Click on the Create Free Account button below to
							access the portal.
						</Step>
						<Step>
							Create your secure username and password for your
							seller account.
						</Step>
						<Step>
							Easily link your existing Amazon or eBay seller
							account to the Clickbuy Deals platform.
						</Step>
						<Step>
							Review and agree to the marketplace terms and
							conditions.
						</Step>
						<Step>
							Use the import function to bring your existing
							inventory data into the platform.
						</Step>
						<Step>
							Select specific inventory items to list and set your
							desired price. Press Publish.
						</Step>
						<Step>
							If prompted, confirm your agreement to the terms and
							conditions.
						</Step>
						<Step>
							Provide your bank account details to receive
							payments after your first sale.
						</Step>
					</ul>

					<div className="text-center">
						<CreateAccountButton
							size="lg"
							className="text-lg px-8"
						/>
					</div>
				</div>
			</div>
		</section>
	);
};
