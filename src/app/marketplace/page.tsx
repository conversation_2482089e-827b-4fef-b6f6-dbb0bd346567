import { Suspense } from "react";
import { CTASection } from "./(sections)/cta";
import { Header } from "./(sections)/header";
import { HeroSection } from "./(sections)/hero";
import { PlatformSection } from "./(sections)/platform";
import { AIAdvantageSection } from "./(sections)/ai-advantage";
import { PricingSection } from "./(sections)/pricing";
import { StartSellingCTA } from "./(sections)/start-selling";
import { Footer } from "@/components/layouts/footer";

// Loading screen component
const PageLoader = () => (
	<div className="flex items-center justify-center min-h-screen bg-slate-900">
		<div className="text-center">
			<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
			<p className="text-slate-400">Loading AI Deal Finder...</p>
		</div>
	</div>
);

// Main content component
const PageContent = () => (
	<div className="">
		<Header />
		<main>
			<HeroSection />
			<PlatformSection />
			<AIAdvantageSection />
			<PricingSection />
			<StartSellingCTA />
			<CTASection />
		</main>
		<Footer />
	</div>
);

export default function AIDeaFinderPage() {
	return (
		<Suspense fallback={<PageLoader />}>
			<PageContent />
		</Suspense>
	);
}
