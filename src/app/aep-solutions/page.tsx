"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";

import SolutionsHubPageContent from "./(components)/aep-page-contents";

export default function ProtectedSolutionsPage() {
	const { isAuthenticated, isLoadingProfile } = useAuth();
	const router = useRouter();

	useEffect(() => {
		if (!isLoadingProfile && !isAuthenticated) {
			router.replace("/");
		}
	}, [isAuthenticated, isLoadingProfile, router]);

	if (isLoadingProfile || !isAuthenticated) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-slate-900">
				<Loader2 className="w-8 h-8 text-green-500 animate-spin" />
			</div>
		);
	}

	return <SolutionsHubPageContent />;
}
