"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";
import dynamic from "next/dynamic";

const LoadingScreen = () => (
	<div className="flex h-screen items-center justify-center bg-background">
		<div className="text-center">
			<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
			<p className="text-foreground">Loading Deals Finder...</p>
		</div>
	</div>
);

const Platforms = dynamic(() => import("../(components)/platforms"), {
	ssr: false,
	loading: () => <LoadingScreen />,
});

export default function ProtectedPlatformsPage() {
	const { isAuthenticated, isLoadingProfile } = useAuth();
	const router = useRouter();

	useEffect(() => {
		if (!isLoadingProfile && !isAuthenticated) {
			router.replace("/");
		}
	}, [isAuthenticated, isLoadingProfile, router]);

	if (isLoadingProfile || !isAuthenticated) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-slate-900">
				<Loader2 className="w-8 h-8 text-green-500 animate-spin" />
			</div>
		);
	}

	return <Platforms />;
}
