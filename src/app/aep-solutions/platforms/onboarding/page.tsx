"use client";

import { useEffect } from "react";
import OnboardingComponent from "../../(components)/onboarding-component";

export default function OnboardingPage() {
	useEffect(() => {
		// Check if already onboarded
		const isOnboarded =
			localStorage.getItem("onboardingComplete") === "true";
		if (isOnboarded) {
			window.location.href = "/aep-solutions/platforms";
		}
	}, []);

	return <OnboardingComponent />;
}
