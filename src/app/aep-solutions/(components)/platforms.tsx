/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useTheme } from "@/hooks/useTheme";
import { useAuth } from "@/hooks/use-auth";
import { useTokenBalance } from "@/hooks/use-token-data";
import { extensionsAPI } from "@/lib/api/extensions";
import { ExtensionsDashboardData } from "@/types/extension";
import Sidebar from "@/components/layouts/sidebar";
import ExtensionsHeader from "@/components/deals-finder/headers";
import ExtensionsContentRenderer from "@/components/deals-finder/extensions-content-renderer";
import {
	ExtensionsLoadingState,
	ExtensionsErrorState,
} from "@/components/deals-finder/extensions-states";
import { createExtensionsSidebarSections } from "@/components/deals-finder/sidebar-config";

const Platforms: React.FC = () => {
	const router = useRouter();
	const searchParams = useSearchParams();
	const { isDarkMode, toggleTheme } = useTheme();
	const { user } = useAuth();
	const { data: tokenBalance } = useTokenBalance();

	const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
	const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
	const [extensionsData, setExtensionsData] =
		useState<ExtensionsDashboardData | null>(null);
	const [loading, setLoading] = useState(true);
	const [activeTab, setActiveTab] = useState("home");

	useEffect(() => {
		const tab = searchParams.get("tab");
		if (tab) {
			setActiveTab(tab);
		}
	}, [searchParams]);

	const fetchExtensionsData = useCallback(async () => {
		try {
			setLoading(true);
			const data = await extensionsAPI.getDashboardData();
			setExtensionsData(data);
		} catch (error) {
			console.error("Failed to fetch extensions data:", error);
		} finally {
			setLoading(false);
		}
	}, []);

	useEffect(() => {
		fetchExtensionsData();
	}, [fetchExtensionsData]);

	const handleNavigateToSourceOra = () => {
		setActiveTab("source-ora");
		router.replace("/aep-solutions/platforms?tab=source-ora");
	};//localhost:3002/auth

	const handleTabChange = (newTab: string) => {
		setActiveTab(newTab);

		const params = new URLSearchParams();
		params.set("tab", newTab);

		const currentSection = searchParams.get("section");
		if (newTab === "billing" && currentSection) {
			params.set("section", currentSection);
		}

		router.replace(`/aep-solutions/platforms?${params.toString()}`);
	};

	const handleToggleSidebar = () => setSidebarCollapsed(!sidebarCollapsed);
	const handleToggleMobileMenu = () => setMobileMenuOpen(!mobileMenuOpen);
	const handleCloseMobileMenu = () => setMobileMenuOpen(false);
	const handleBackToHome = () => router.push("/");

	const handleUpgrade = () => {
		setActiveTab("billing");
		router.push("/aep-solutions/platforms?tab=billing&section=pricing");
	};

	const handleAnalyzeProduct = async (asin: string) => {
		const result = await extensionsAPI.analyzeProduct(asin);
		await fetchExtensionsData();
		return result;
	};

	const handleUpdateSettings = async (settings: any) => {
		await extensionsAPI.updateSettings(settings);
		await fetchExtensionsData();
	};

	const handleUpdateWeights = async (weights: any) => {
		await extensionsAPI.updateAIWeights(weights);
		await fetchExtensionsData();
	};

	const sidebarSections = createExtensionsSidebarSections(
		activeTab,
		handleTabChange,
		tokenBalance,
		extensionsData
	);

	if (loading) {
		return <ExtensionsLoadingState isDarkMode={isDarkMode} />;
	}

	if (!extensionsData) {
		return (
			<ExtensionsErrorState
				isDarkMode={isDarkMode}
				onRetry={fetchExtensionsData}
			/>
		);
	}

	return (
		<div className="flex h-screen bg-background text-foreground transition-colors duration-300">
			<Sidebar
				isCollapsed={sidebarCollapsed}
				onToggleCollapse={handleToggleSidebar}
				isDarkMode={isDarkMode}
				isMobileMenuOpen={mobileMenuOpen}
				onCloseMobileMenu={handleCloseMobileMenu}
				customSections={sidebarSections}
				showUserSection={false}
				showFooter={false}
			/>

			<div className="flex-1 flex flex-col min-w-0 bg-background">
				<ExtensionsHeader
					isDarkMode={isDarkMode}
					user={user}
					extensionsData={extensionsData}
					tokenBalance={tokenBalance}
					onBackToHome={handleBackToHome}
					onToggleTheme={toggleTheme}
					onToggleMobileMenu={handleToggleMobileMenu}
				/>

				<ExtensionsContentRenderer
					activeTab={activeTab}
					extensionsData={extensionsData}
					isDarkMode={isDarkMode}
					onAnalyzeProduct={handleAnalyzeProduct}
					onUpdateSettings={handleUpdateSettings}
					onUpdateWeights={handleUpdateWeights}
					onUpgrade={handleUpgrade}
					onNavigateToSourceOra={handleNavigateToSourceOra}
					section={searchParams.get("section")}
					onTabChange={handleTabChange}
				/>
			</div>
		</div>
	);
};

export default Platforms;