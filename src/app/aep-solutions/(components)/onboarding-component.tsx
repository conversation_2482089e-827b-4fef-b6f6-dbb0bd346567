/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import {
	CheckCircle,
	AlertCircle,
	Loader2,
	Save,
	RotateCcw,
	ArrowLeft,
	ArrowRight,
	Sun,
	Moon,
	Briefcase,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { ONBOARDING_FIELD_CONFIGS, onboardingSchema } from "@/constants";
import { useAuth } from "@/hooks/use-auth";
import { useTheme } from "@/hooks/useTheme";

type OnboardingData = z.infer<typeof onboardingSchema>;

interface OnboardingProps {
	onSubmit?: (data: OnboardingData) => void;
	onCancel?: () => void;
}

const OnboardingComponent: React.FC<OnboardingProps> = ({
	onSubmit,
	onCancel,
}) => {
	const [currentStep, setCurrentStep] = useState(1);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const { isDarkMode, toggleTheme } = useTheme();
	const [submitStatus, setSubmitStatus] = useState<
		"success" | "error" | null
        >(null);
    const {user} = useAuth()

	const form = useForm<OnboardingData>({
		resolver: zodResolver(onboardingSchema),
		defaultValues: {
			name: "",
			email: user?.email || "",
			ageRange: "",
			country: "",
			city: "",
			occupation: "",
			company: "",
			experience: "",
			salaryRange: "",
			ecommerceInterests: [],
			productTypes: "",
			targetMarket: "",
			skills: "",
			bio: "",
			newsletter: false,
			terms: false,
		},
	});

	// Load data from localStorage
	useEffect(() => {
		const savedData = localStorage.getItem("onboardingData");
		if (savedData) {
			const parsedData = JSON.parse(savedData);
			form.reset(parsedData);
		}
	}, [form]);

	// Save to localStorage
	useEffect(() => {
		const subscription = form.watch((value) => {
			localStorage.setItem("onboardingData", JSON.stringify(value));
		});
		return () => subscription.unsubscribe();
    }, [form]);

    useEffect(() => {
		if (user?.email) {
			form.setValue("email", user.email);
		}
	}, [form, user?.email]);



	const steps = [
		{ number: 1, title: "Agent", description: "Personal Info" },
		{ number: 2, title: "Business", description: "Professional" },
		{ number: 3, title: "Goals", description: "Ecommerce Focus" },
		{ number: 4, title: "Team", description: "Finalize" },
	];

	const handleSubmit = async (data: OnboardingData) => {
		setIsSubmitting(true);
		setSubmitStatus(null);

		try {
			await new Promise((resolve) => setTimeout(resolve, 2000));
			console.log("Onboarding submitted:", data);

			localStorage.setItem("onboardingComplete", "true");
			localStorage.setItem("userProfile", JSON.stringify(data));

			setSubmitStatus("success");

			// Redirect to dashboard after 2 seconds
			setTimeout(() => {
				window.location.href = "/aep-solutions/platforms";
			}, 2000);

			if (onSubmit) {
				onSubmit(data);
			}
		} catch (error) {
			console.error("Onboarding submission error:", error);
			setSubmitStatus("error");
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleReset = () => {
		form.reset();
		setSubmitStatus(null);
		localStorage.removeItem("onboardingData");
	};

	const nextStep = async () => {
		const fieldsToValidate = Array.from(ONBOARDING_FIELD_CONFIGS.entries())
			.filter(
				([, config]) => config.step === currentStep && config.required
			)
			.map(([key]) => key as keyof OnboardingData);

		const isValid = await form.trigger(fieldsToValidate);
		if (isValid) {
			setCurrentStep((prev) => Math.min(prev + 1, 4));
		}
	};

	const prevStep = () => {
		setCurrentStep((prev) => Math.max(prev - 1, 1));
	};

	const renderField = (key: string, config: any) => {
		const Icon = config.icon;

		switch (config.type) {
			case "text":
			case "email":
				return (
					<FormField
						key={key}
						control={form.control}
						name={key as keyof OnboardingData}
						render={({ field }) => (
							<FormItem className="space-y-3">
								<FormLabel
									className={`flex items-center gap-2 text-sm font-medium ${
										isDarkMode
											? "text-slate-300"
											: "text-slate-700"
									}`}>
									<Icon className="w-4 h-4 text-green-500" />
									{config.label}
									{config.required && (
										<span className="text-red-500">*</span>
									)}
								</FormLabel>
								<FormControl>
									<Input
										type={config.type}
										placeholder={config.placeholder}
										value={(field.value as string) || ""}
										onChange={field.onChange}
										onBlur={field.onBlur}
										name={field.name}
										ref={field.ref}
										disabled={
											field.disabled || key === "email"
										} // Disable email field
										readOnly={key === "email"} // Make email read-only
										className={`${
											isDarkMode
												? "bg-slate-800 border-slate-600 text-slate-200 placeholder-slate-400 focus:border-green-500 focus:ring-green-500/20"
												: "bg-white border-slate-300 text-slate-900 placeholder-slate-500 focus:border-green-500 focus:ring-green-500/20"
										} ${
											key === "email"
												? "cursor-not-allowed opacity-75"
												: ""
										}`} // Add visual indication for disabled email
									/>
								</FormControl>
								<FormDescription
									className={`text-xs ${
										isDarkMode
											? "text-slate-400"
											: "text-slate-600"
									}`}>
									{key === "email"
										? "Email is auto-filled from your account"
										: config.description}
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>
				);

			case "select":
				return (
					<FormField
						key={key}
						control={form.control}
						name={key as keyof OnboardingData}
						render={({ field }) => (
							<FormItem className="space-y-3">
								<FormLabel
									className={`flex items-center gap-2 text-sm font-medium ${
										isDarkMode
											? "text-slate-300"
											: "text-slate-700"
									}`}>
									<Icon className="w-4 h-4 text-green-500" />
									{config.label}
									{config.required && (
										<span className="text-red-500">*</span>
									)}
								</FormLabel>
								<Select
									onValueChange={field.onChange}
									value={(field.value as string) || ""}
									disabled={field.disabled}
									name={field.name}>
									<FormControl>
										<SelectTrigger
											className={`${
												isDarkMode
													? "bg-slate-800 border-slate-600 text-slate-200 focus:border-green-500 focus:ring-green-500/20"
													: "bg-white border-slate-300 text-slate-900 focus:border-green-500 focus:ring-green-500/20"
											}`}>
											<SelectValue
												placeholder={config.placeholder}
											/>
										</SelectTrigger>
									</FormControl>
									<SelectContent
										className={
											isDarkMode
												? "bg-slate-800 border-slate-600"
												: "bg-white border-slate-300"
										}>
										{config.options?.map((option: any) => (
											<SelectItem
												key={option.value}
												value={option.value}>
												{option.label}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<FormDescription
									className={`text-xs ${
										isDarkMode
											? "text-slate-400"
											: "text-slate-600"
									}`}>
									{config.description}
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>
				);

			case "checkbox":
				return (
					<FormField
						key={key}
						control={form.control}
						name={key as keyof OnboardingData}
						render={({ field }) => (
							<FormItem className="space-y-3">
								<FormLabel
									className={`flex items-center gap-2 text-sm font-medium ${
										isDarkMode
											? "text-slate-300"
											: "text-slate-700"
									}`}>
									<Icon className="w-4 h-4 text-green-500" />
									{config.label}
									{config.required && (
										<span className="text-red-500">*</span>
									)}
								</FormLabel>
								<div className="grid grid-cols-2 gap-3">
									{config.options?.map((option: any) => (
										<div
											key={option.value}
											className="flex items-center space-x-2">
											<Checkbox
												id={option.value}
												checked={(
													(field.value as string[]) ||
													[]
												).includes(option.value)}
												onCheckedChange={(checked) => {
													const currentValue =
														(field.value as string[]) ||
														[];
													if (checked) {
														field.onChange([
															...currentValue,
															option.value,
														]);
													} else {
														field.onChange(
															currentValue.filter(
																(
																	value: string
																) =>
																	value !==
																	option.value
															)
														);
													}
												}}
												disabled={field.disabled}
											/>
											<Label
												htmlFor={option.value}
												className={`text-sm ${
													isDarkMode
														? "text-slate-300"
														: "text-slate-700"
												}`}>
												{option.label}
											</Label>
										</div>
									))}
								</div>
								<FormDescription
									className={`text-xs ${
										isDarkMode
											? "text-slate-400"
											: "text-slate-600"
									}`}>
									{config.description}
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>
				);

			case "textarea":
				return (
					<FormField
						key={key}
						control={form.control}
						name={key as keyof OnboardingData}
						render={({ field }) => (
							<FormItem className="space-y-3">
								<FormLabel
									className={`flex items-center gap-2 text-sm font-medium ${
										isDarkMode
											? "text-slate-300"
											: "text-slate-700"
									}`}>
									<Icon className="w-4 h-4 text-green-500" />
									{config.label}
									{config.required && (
										<span className="text-red-500">*</span>
									)}
								</FormLabel>
								<FormControl>
									<Textarea
										placeholder={config.placeholder}
										value={(field.value as string) || ""}
										onChange={field.onChange}
										onBlur={field.onBlur}
										name={field.name}
										ref={field.ref}
										disabled={field.disabled}
										className={`min-h-[100px] resize-none ${
											isDarkMode
												? "bg-slate-800 border-slate-600 text-slate-200 placeholder-slate-400 focus:border-green-500 focus:ring-green-500/20"
												: "bg-white border-slate-300 text-slate-900 placeholder-slate-500 focus:border-green-500 focus:ring-green-500/20"
										}`}
									/>
								</FormControl>
								<FormDescription
									className={`text-xs ${
										isDarkMode
											? "text-slate-400"
											: "text-slate-600"
									}`}>
									{config.description}
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>
				);

			default:
				return null;
		}
	};

	const getCurrentStepFields = () => {
		return Array.from(ONBOARDING_FIELD_CONFIGS.entries()).filter(
			([, config]) => config.step === currentStep
		);
	};

	const getStepTitle = () => {
		const stepTitles = {
			1: "Personal Information",
			2: "Business Profile",
			3: "Ecommerce Focus",
			4: "Complete Your Profile",
		};
		return stepTitles[currentStep as keyof typeof stepTitles];
	};

	const getStepDescription = () => {
		const stepDescriptions = {
			1: "Create an intelligent agent that handles customer support 24/7",
			2: "Tell us about your professional background",
			3: "Help us understand your ecommerce interests and goals",
			4: "Add final details to complete your setup",
		};
		return stepDescriptions[currentStep as keyof typeof stepDescriptions];
	};

	return (
		<div
			className={`min-h-screen ${
				isDarkMode ? "bg-slate-900" : "bg-gray-50"
			}`}>
			{/* Header */}
			<header
				className={`fixed top-0 left-0 right-0 z-50 border-b ${
					isDarkMode
						? "border-slate-700 bg-slate-900/95 backdrop-blur-md"
						: "border-slate-200 bg-white/95 backdrop-blur-md"
				}`}>
				<div className="flex justify-between items-center px-6 py-4">
					<button className="flex items-center space-x-3 transition-transform hover:scale-105">
						<Image
							src="/logo.png"
							alt="ClickBuy Logo"
							width={40}
							height={40}
							className="w-full h-20"
							priority
						/>
						<div className="-ml-11">
							<h1
								className={`text-xl font-semibold ${
									isDarkMode
										? "text-slate-50"
										: "text-slate-900"
								}`}>
								ClickBuy
							</h1>
							<span className="text-sm font-medium text-green-500">
								Onboarding
							</span>
						</div>
					</button>

					<Button
						onClick={toggleTheme}
						variant="outline"
						size="sm"
						className={`p-2 ${
							isDarkMode
								? "border-slate-600 text-slate-300 hover:bg-slate-800"
								: "border-slate-300 text-slate-700 hover:bg-slate-50"
						}`}>
						{isDarkMode ? (
							<Sun className="w-4 h-4" />
						) : (
							<Moon className="w-4 h-4" />
						)}
					</Button>
				</div>
			</header>

			{/* Main Content */}
			<main className="min-h-screen flex flex-col items-center justify-center py-16 pt-32">
				<div className="w-full max-w-4xl mx-auto px-4">
					{/* Progress Steps */}
					<div className="flex items-center justify-center mb-12">
						{steps.map((step, index) => (
							<React.Fragment key={step.number}>
								<div className="flex flex-col items-center">
									<div
										className={`w-12 h-12 rounded-full flex items-center justify-center border-2 font-semibold ${
											currentStep >= step.number
												? "bg-green-500 border-green-500 text-white"
												: isDarkMode
												? "border-slate-600 text-slate-400"
												: "border-slate-300 text-slate-500"
										}`}>
										{currentStep > step.number ? (
											<CheckCircle className="w-6 h-6" />
										) : (
											step.number
										)}
									</div>
									<div className="mt-2 text-center">
										<div
											className={`text-sm font-medium ${
												currentStep >= step.number
													? isDarkMode
														? "text-slate-200"
														: "text-slate-700"
													: isDarkMode
													? "text-slate-500"
													: "text-slate-400"
											}`}>
											{step.title}
										</div>
										<div
											className={`text-xs ${
												isDarkMode
													? "text-slate-600"
													: "text-slate-400"
											}`}>
											{step.description}
										</div>
									</div>
								</div>
								{index < steps.length - 1 && (
									<div
										className={`w-16 h-0.5 mx-4 ${
											currentStep > step.number
												? "bg-green-500"
												: isDarkMode
												? "bg-slate-700"
												: "bg-slate-300"
										}`}
									/>
								)}
							</React.Fragment>
						))}
					</div>

					<div className="text-center mb-8">
						<div
							className={`text-sm ${
								isDarkMode ? "text-slate-400" : "text-slate-600"
							}`}>
							Step {currentStep} of {steps.length}
						</div>
					</div>

					{/* Main Card */}
					<div
						className={`w-full mx-auto rounded-2xl border shadow-xl lg:max-w-6xl ${
							isDarkMode
								? "bg-slate-800 border-slate-700"
								: "bg-white border-slate-200"
						}`}>
						{/* Status Messages */}
						{submitStatus === "success" && (
							<div className="p-6 border-b border-green-200 bg-green-50">
								<div className="flex items-center gap-3">
									<CheckCircle className="w-5 h-5 text-green-500" />
									<div>
										<h4 className="text-green-500 font-medium">
											Success!
										</h4>
										<p className="text-sm text-green-700">
											Profile created successfully.
											Redirecting to dashboard...
										</p>
									</div>
								</div>
							</div>
						)}

						{submitStatus === "error" && (
							<div className="p-6 border-b border-red-200 bg-red-50">
								<div className="flex items-center gap-3">
									<AlertCircle className="w-5 h-5 text-red-500" />
									<div>
										<h4 className="text-red-500 font-medium">
											Error
										</h4>
										<p className="text-sm text-red-700">
											There was an error creating your
											profile. Please try again.
										</p>
									</div>
								</div>
							</div>
						)}

						{/* Card Content */}
						<div className="p-8">
							{/* Step Icon and Title */}
							<div className="text-center mb-8">
								<div
									className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4 ${
										isDarkMode
											? "bg-slate-700"
											: "bg-slate-100"
									}`}>
									<Briefcase className="w-8 h-8 text-green-500" />
								</div>
								<h2
									className={`text-2xl font-bold mb-2 ${
										isDarkMode
											? "text-slate-50"
											: "text-slate-900"
									}`}>
									{getStepTitle()}
								</h2>
								<p
									className={`${
										isDarkMode
											? "text-slate-400"
											: "text-slate-600"
									}`}>
									{getStepDescription()}
								</p>
							</div>

							{/* Form */}
							<Form {...form}>
								<form
									onSubmit={form.handleSubmit(handleSubmit)}
									className="space-y-6">
									{/* Current Step Fields */}
									<div className="grid grid-cols-2 gap-8 ">
										{getCurrentStepFields().map(
											([key, config]) =>
												renderField(key, config)
										)}
									</div>

									{/* Additional Checkboxes for Step 4 */}
									{currentStep === 4 && (
										<div className="space-y-4 pt-6 border-t border-slate-200">
											<FormField
												control={form.control}
												name="newsletter"
												render={({ field }) => (
													<FormItem className="flex flex-row items-start space-x-3 space-y-0">
														<FormControl>
															<Checkbox
																checked={
																	field.value as boolean
																}
																onCheckedChange={
																	field.onChange
																}
																disabled={
																	field.disabled
																}
															/>
														</FormControl>
														<div className="space-y-1 leading-none">
															<FormLabel
																className={`text-sm ${
																	isDarkMode
																		? "text-slate-300"
																		: "text-slate-700"
																}`}>
																Subscribe to
																newsletter
															</FormLabel>
															<FormDescription
																className={`text-xs ${
																	isDarkMode
																		? "text-slate-400"
																		: "text-slate-600"
																}`}>
																Get updates on
																new features and
																opportunities
															</FormDescription>
														</div>
													</FormItem>
												)}
											/>

											<FormField
												control={form.control}
												name="terms"
												render={({ field }) => (
													<FormItem className="flex flex-row items-start space-x-3 space-y-0">
														<FormControl>
															<Checkbox
																checked={
																	field.value as boolean
																}
																onCheckedChange={
																	field.onChange
																}
																disabled={
																	field.disabled
																}
															/>
														</FormControl>
														<div className="space-y-1 leading-none">
															<FormLabel
																className={`text-sm ${
																	isDarkMode
																		? "text-slate-300"
																		: "text-slate-700"
																}`}>
																Accept terms and
																conditions
																<span className="text-red-500 ml-1">
																	*
																</span>
															</FormLabel>
															<FormDescription
																className={`text-xs ${
																	isDarkMode
																		? "text-slate-400"
																		: "text-slate-600"
																}`}>
																You agree to our
																Terms of Service
																and Privacy
																Policy
															</FormDescription>
														</div>
														<FormMessage />
													</FormItem>
												)}
											/>
										</div>
									)}

									{/* Navigation Buttons */}
									<div className="flex items-center justify-between pt-6">
										<div className="flex items-center gap-2">
											<Button
												type="button"
												variant="outline"
												onClick={prevStep}
												disabled={currentStep === 1}
												className={`${
													isDarkMode
														? "border-slate-600 text-slate-300 hover:bg-slate-700"
														: "border-slate-300 text-slate-700 hover:bg-slate-50"
												}`}>
												<ArrowLeft className="w-4 h-4 mr-2" />
												Previous
											</Button>

											<Button
												type="button"
												variant="outline"
												onClick={handleReset}
												disabled={isSubmitting}
												className={`${
													isDarkMode
														? "border-slate-600 text-slate-300 hover:bg-slate-700"
														: "border-slate-300 text-slate-700 hover:bg-slate-50"
												}`}>
												<RotateCcw className="w-4 h-4 mr-2" />
												Reset
											</Button>

											{onCancel && (
												<Button
													type="button"
													variant="outline"
													onClick={onCancel}
													disabled={isSubmitting}
													className={`${
														isDarkMode
															? "border-slate-600 text-slate-300 hover:bg-slate-700"
															: "border-slate-300 text-slate-700 hover:bg-slate-50"
													}`}>
													Cancel
												</Button>
											)}
										</div>

										{currentStep < 4 ? (
											<Button
												type="button"
												onClick={nextStep}
												className="bg-green-500 text-white hover:bg-green-600 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30 transition-all duration-300">
												Next
												<ArrowRight className="w-4 h-4 ml-2" />
											</Button>
										) : (
											<Button
												type="submit"
												disabled={isSubmitting}
												className="bg-green-500 text-white hover:bg-green-600 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed">
												{isSubmitting ? (
													<div className="flex items-center gap-2">
														<Loader2 className="w-4 h-4 animate-spin" />
														Creating Profile...
													</div>
												) : (
													<div className="flex items-center gap-2">
														<Save className="w-4 h-4" />
														Start Automating Support
													</div>
												)}
											</Button>
										)}
									</div>
								</form>
							</Form>
						</div>
					</div>
				</div>
			</main>
		</div>
	);
};

export default OnboardingComponent;
