// src/app/solutions/page.tsx
"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Search, Package, X, Clock } from "lucide-react";
import { faUsersCog, faShoppingCart } from "@fortawesome/free-solid-svg-icons";
import { useState } from "react";

interface SolutionsHubPageProps {
	isDarkMode?: boolean;
}

export default function SolutionsHubPage({
	isDarkMode = true,
}: SolutionsHubPageProps) {
	const [showComingSoonModal, setShowComingSoonModal] = useState(false);
	const handleDigitalForceLaunch = () => {
		window.location.href =
			"/aep-solutions/platforms?section=Digital Workforce&tab=ai-agent";
	};

	const handleAIDealSourcingLaunch = () => {
		window.location.href =
			"/aep-solutions/platforms?section=AI Deal Sourcing&tab=analyzer";
	};

	const handleMarketplaceLaunch = () => {
		window.location.href =
			"/aep-solutions/platforms?section=Cross Selling&tab=clickbuy-store";
	};

	const handleSystemiseFulfilmentLaunch = () => {
		window.location.href =
			"/aep-solutions/platforms?section=Systemise Fulfilment&tab=systemise-fulfilment";
	}

	const handleSystemiseFulfilmentExternal = () => {
		window.open(
			"https://www.systemisefulfilment.co.uk/home?ref=https%3A%2F%2Fwww.systemisefulfilment.co.uk%2Fa%2F2147974982%2FQyaKRbHz",
			"_blank"
		);
	};

	return (
		<div
			className={`min-h-screen ${
				isDarkMode ? "bg-slate-900" : "bg-gray-50"
			}`}>
			<main>
				{/* Single Combined Section */}
				<section
					className={`min-h-screen flex flex-col items-center justify-center relative overflow-hidden ${
						isDarkMode
							? "bg-gradient-to-br from-slate-900 to-slate-800"
							: "bg-gradient-to-br from-gray-50 to-gray-100"
					}`}>
					{/* Background Effects */}
					<div
						className={`absolute inset-0 pointer-events-none ${
							isDarkMode
								? "bg-[radial-gradient(circle_at_25%_25%,rgba(34,197,94,0.1)_0%,transparent_50%),radial-gradient(circle_at_75%_75%,rgba(34,197,94,0.05)_0%,transparent_50%)]"
								: "bg-[radial-gradient(circle_at_25%_25%,rgba(34,197,94,0.05)_0%,transparent_50%),radial-gradient(circle_at_75%_75%,rgba(34,197,94,0.03)_0%,transparent_50%)]"
						}`}
					/>

					<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10 py-16">
						{/* Hero Content */}
						<div className="animate-[fadeInUp_0.8s_ease-out] mb-16">
							<h1
								className={`text-4xl lg:text-5xl font-bold leading-tight mb-6 ${
									isDarkMode
										? "text-slate-50"
										: "text-gray-900"
								}`}>
								The Future is{" "}
								<span
									className={`bg-gradient-to-r bg-clip-text text-transparent ${
										isDarkMode
											? "from-slate-50 to-green-500"
											: "from-gray-900 to-green-600"
									}`}>
									Agentic eCommerce Platforms (AEP)
								</span>
							</h1>

							<div className="w-24 h-1 bg-gradient-to-r from-green-500 to-transparent mx-auto mb-8" />

							<p
								className={`text-xl lg:text-2xl max-w-4xl mx-auto ${
									isDarkMode
										? "text-slate-400"
										: "text-gray-600"
								}`}>
								Lower costs, build a moat, and scale with
								purpose. Choose your path to start.
							</p>
						</div>

						{/* Solutions Grid */}
						<div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-8 animate-[fadeInUp_0.8s_ease-out] [animation-delay:0.2s]">
							{/* Deal Sourcing Extension */}
							<Card
								className={`p-8 text-center group hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 relative overflow-hidden rounded-2xl h-full border ${
									isDarkMode
										? "bg-gradient-to-br from-slate-800 to-slate-700 border-slate-700"
										: "bg-gradient-to-br from-white to-gray-50 border-gray-200 shadow-lg"
								}`}>
								<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

								<CardContent className="space-y-8 p-0 flex flex-col h-full">
									<div className="flex justify-center">
										<div
											className={`w-20 h-20 rounded-full flex items-center justify-center ${
												isDarkMode
													? "bg-slate-700"
													: "bg-gray-100"
											}`}>
											<Search className="text-green-500 w-14 h-14" />
										</div>
									</div>

									<div className="flex-1">
										<h3
											className={`text-2xl font-semibold mb-6 ${
												isDarkMode
													? "text-slate-50"
													: "text-gray-900"
											}`}>
											AI Deal Sourcing
										</h3>
										<p
											className={`leading-relaxed text-lg ${
												isDarkMode
													? "text-slate-400"
													: "text-gray-600"
											}`}>
											Deploy our Amazon profit calculator
											with integrated CheckOra agent to
											assess and review all of your Amazon
											deals.
										</p>
									</div>

									<div className="flex gap-2 pt-4">
										<Button
											size="lg"
											onClick={handleAIDealSourcingLaunch}
											className="w-full flex-1 text-base px-4 bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] hover:from-[#12C2E9] hover:to-[#19D86C] hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30 transition-all duration-300 rounded-full">
											Launch Platform
										</Button>

										<Link
											href="/deals-finder"
											className="flex-1">
											<Button
												size="lg"
												variant="outline"
												className={`w-full text-base px-4 border-2 border-green-500 text-green-500 hover:bg-green-500 transition-all duration-300 rounded-full ${
													isDarkMode
														? "bg-transparent hover:text-slate-50"
														: "bg-white hover:text-white"
												}`}>
												Learn More
											</Button>
										</Link>
									</div>
								</CardContent>
							</Card>

							{/* Digital Workforce Platform */}
							<Card
								className={`p-8 text-center group hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 relative overflow-hidden rounded-2xl h-full border ${
									isDarkMode
										? "bg-gradient-to-br from-slate-800 to-slate-700 border-slate-700"
										: "bg-gradient-to-br from-white to-gray-50 border-gray-200 shadow-lg"
								}`}>
								<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

								<CardContent className="space-y-8 p-0 flex flex-col h-full">
									<div className="flex justify-center">
										<div
											className={`w-20 h-20 rounded-full flex items-center justify-center ${
												isDarkMode
													? "bg-slate-700"
													: "bg-gray-100"
											}`}>
											<FontAwesomeIcon
												icon={faUsersCog}
												className="text-green-500 text-4xl"
											/>
										</div>
									</div>

									<div className="flex-1">
										<h3
											className={`text-2xl font-semibold mb-6 ${
												isDarkMode
													? "text-slate-50"
													: "text-gray-900"
											}`}>
											Digital Workforce
										</h3>
										<p
											className={`leading-relaxed text-lg ${
												isDarkMode
													? "text-slate-400"
													: "text-gray-600"
											}`}>
											Suggest AI agents to automate tasks
											and scale operations in your
											business. Only Pay for agents once
											we build them and you use them no
											upfront costs..
										</p>
									</div>

									<div className="flex gap-2 pt-4">
										<Button
											size="lg"
											onClick={handleDigitalForceLaunch}
											className="w-full text-base px-4 bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] hover:from-[#12C2E9] hover:to-[#19D86C] hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30 transition-all duration-300 rounded-full flex-1">
											Launch Platform
										</Button>

										<Link
											href="/digital-workforce"
											className="flex-1">
											<Button
												size="lg"
												variant="outline"
												className={`w-full text-base px-4 border-2 border-green-500 text-green-500 hover:bg-green-500 transition-all duration-300 rounded-full ${
													isDarkMode
														? "bg-transparent hover:text-slate-50"
														: "bg-white hover:text-white"
												}`}>
												Learn More
											</Button>
										</Link>
									</div>
								</CardContent>
							</Card>

							{/* Cross Sell with ClickBuyDeals */}
							<Card
								className={`p-8 text-center group hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 relative overflow-hidden rounded-2xl h-full border ${
									isDarkMode
										? "bg-gradient-to-br from-slate-800 to-slate-700 border-slate-700"
										: "bg-gradient-to-br from-white to-gray-50 border-gray-200 shadow-lg"
								}`}>
								<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

								<CardContent className="space-y-8 p-0 flex flex-col h-full">
									<div className="flex justify-center">
										<div
											className={`w-20 h-20 rounded-full flex items-center justify-center ${
												isDarkMode
													? "bg-slate-700"
													: "bg-gray-100"
											}`}>
											<FontAwesomeIcon
												icon={faShoppingCart}
												className="text-green-500 text-4xl"
											/>
										</div>
									</div>

									<div className="flex-1">
										<h3
											className={`text-2xl font-semibold mb-6 ${
												isDarkMode
													? "text-slate-50"
													: "text-gray-900"
											}`}>
											Cross Sell with ClickBuyDeals
										</h3>
										<p
											className={`leading-relaxed text-lg ${
												isDarkMode
													? "text-slate-400"
													: "text-gray-600"
											}`}>
											No Up Front Costs - 10% Flat
											Commission. We succeed when you
											succeed.
										</p>
									</div>

									<div className="flex gap-2 pt-4">
										<Button
											size="lg"
											onClick={handleMarketplaceLaunch}
											className="w-full flex-1 text-base px-4 bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] hover:from-[#12C2E9] hover:to-[#19D86C] hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30 transition-all duration-300 rounded-full">
											Launch Platform
										</Button>

										<Link
											href="/marketplace"
											className="flex-1">
											<Button
												size="lg"
												variant="outline"
												className={`w-full text-base px-4 border-2 border-green-500 text-green-500 hover:bg-green-500 transition-all duration-300 rounded-full ${
													isDarkMode
														? "bg-transparent hover:text-slate-50"
														: "bg-white hover:text-white"
												}`}>
												Learn More
											</Button>
										</Link>
									</div>
								</CardContent>
							</Card>

							{/* Systemise Fulfilment */}
							<Card
								className={`p-8 text-center group hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 relative overflow-hidden rounded-2xl h-full border ${
									isDarkMode
										? "bg-gradient-to-br from-slate-800 to-slate-700 border-slate-700"
										: "bg-gradient-to-br from-white to-gray-50 border-gray-200 shadow-lg"
								}`}>
								<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

								<CardContent className="space-y-8 p-0 flex flex-col h-full">
									<div className="flex justify-center">
										<div
											className={`w-20 h-20 rounded-full flex items-center justify-center ${
												isDarkMode
													? "bg-slate-700"
													: "bg-gray-100"
											}`}>
											<Package className="text-green-500 w-14 h-14" />
										</div>
									</div>

									<div className="flex-1">
										<h3
											className={`text-2xl font-semibold mb-6 ${
												isDarkMode
													? "text-slate-50"
													: "text-gray-900"
											}`}>
											Systemise Fulfilment
										</h3>
										<p
											className={`leading-relaxed text-lg ${
												isDarkMode
													? "text-slate-400"
													: "text-gray-600"
											}`}>
											Professional eCommerce fulfilment
											service that handles your Amazon,
											Shopify & TikTok Shop orders across
											UK, EU & USA. Scale with real-time
											inventory tracking.
										</p>
									</div>

									<div className="flex gap-2 pt-4">
										<Button
											size="lg"
											onClick={
												handleSystemiseFulfilmentLaunch
											}
											className="w-full flex-1 text-base px-4 bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] hover:from-[#12C2E9] hover:to-[#19D86C] hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30 transition-all duration-300 rounded-full">
											Launch Platform
										</Button>

										<Button
											size="lg"
											variant="outline"
											onClick={() =>
												setShowComingSoonModal(true)
											}
											className={`w-full flex-1 text-base px-4 border-2 border-green-500 text-green-500 hover:bg-green-500 transition-all duration-300 rounded-full ${
												isDarkMode
													? "bg-transparent hover:text-slate-50"
													: "bg-white hover:text-white"
											}`}>
											Learn More
										</Button>
									</div>
								</CardContent>
							</Card>
						</div>
					</div>
				</section>

				{/* Coming Soon Modal */}
				{showComingSoonModal && (
					<div className="fixed inset-0 z-50 overflow-y-auto">
						<div
							className={`fixed inset-0 ${
								isDarkMode ? "bg-black/70" : "bg-white/70"
							} transition-opacity`}
							onClick={() => setShowComingSoonModal(false)}
						/>

						<div className="flex min-h-full items-center justify-center p-4">
							<div
								className={`relative w-full max-w-md transform rounded-2xl border shadow-2xl ${
									isDarkMode
										? "bg-gray-800 border-gray-700"
										: "bg-white border-gray-200"
								}`}>
								<div className="flex items-center justify-between p-6 pb-4">
									<div className="flex items-center gap-3">
										<div className="w-10 h-10 rounded-full flex items-center justify-center bg-orange-500/20">
											<Clock
												size={20}
												className="text-orange-500"
											/>
										</div>
										<div>
											<h3
												className={`text-lg font-semibold ${
													isDarkMode
														? "text-white"
														: "text-gray-900"
												}`}>
												Coming Soon
											</h3>
											<p
												className={`text-sm ${
													isDarkMode
														? "text-gray-300"
														: "text-gray-600"
												}`}>
												Systemise Fulfilment Details
											</p>
										</div>
									</div>

									<button
										onClick={() =>
											setShowComingSoonModal(false)
										}
										className={`p-2 rounded-lg transition-colors ${
											isDarkMode
												? "hover:bg-gray-700 text-gray-400 hover:text-white"
												: "hover:bg-gray-100 text-gray-500 hover:text-gray-700"
										}`}>
										<X size={18} />
									</button>
								</div>

								<div className="px-6 pb-6">
									<div
										className={`mb-6 p-4 rounded-lg border ${
											isDarkMode
												? "bg-orange-500/10 border-orange-500/30"
												: "bg-orange-50 border-orange-200"
										}`}>
										<div className="flex items-start gap-3">
											<Clock
												size={16}
												className="mt-0.5 text-orange-500"
											/>
											<div className="text-sm">
												<p
													className={`font-medium mb-1 ${
														isDarkMode
															? "text-white"
															: "text-gray-900"
													}`}>
													Page Under Development
												</p>
												<p
													className={`${
														isDarkMode
															? "text-gray-300"
															: "text-gray-600"
													}`}>
													We&apos;re working on creating a
													detailed page for Systemise
													Fulfilment. In the meantime,
													you can visit their website
													directly to learn more about
													their services.
												</p>
											</div>
										</div>
									</div>

									<div className="flex gap-3">
										<Button
											onClick={() =>
												setShowComingSoonModal(false)
											}
											variant="outline"
											className={`flex-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 ${
												isDarkMode
													? "bg-gray-700 text-gray-300 hover:bg-gray-600 border-gray-600"
													: "bg-gray-100 text-gray-700 hover:bg-gray-200 border-gray-300"
											}`}>
											Close
										</Button>

										<Button
											onClick={() => {
												setShowComingSoonModal(false);
												handleSystemiseFulfilmentExternal();
											}}
											className="flex-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] hover:from-[#12C2E9] hover:to-[#19D86C] hover:scale-105 active:scale-95">
											Visit Website
										</Button>
									</div>
								</div>
							</div>
						</div>
					</div>
				)}
			</main>
		</div>
	);
}
