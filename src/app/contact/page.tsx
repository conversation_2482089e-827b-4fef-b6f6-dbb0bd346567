/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Mail, Send, ArrowLeft, CheckCircle } from "lucide-react";
import { toast } from "sonner";

interface FormData {
	email: string;
	message: string;
}

const ContactSupportPage: React.FC = () => {
	const [formData, setFormData] = useState<FormData>({
		email: "",
		message: "",
	});
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isSubmitted, setIsSubmitted] = useState(false);

	const handleInputChange = useCallback(
		(field: keyof FormData, value: string) => {
			setFormData((prev) => ({
				...prev,
				[field]: value,
			}));
		},
		[]
	);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		// Validate form before submission
		if (!formData.email.trim() || !formData.message.trim()) {
			toast.error("Please fill in all required fields.");
			return;
		}

		// Basic email validation
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(formData.email)) {
			toast.error("Please enter a valid email address.");
			return;
		}

		setIsSubmitting(true);

		try {
			const response = await fetch(
				"https://app.clickbuy.ai/extension-backend/api/v1/feedback",
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify({
						email: formData.email.trim(),
						message: formData.message.trim(),
					}),
				}
			);

			if (!response.ok) {
				const errorData = await response.json().catch(() => ({}));
				throw new Error(
					errorData.detail ||
						errorData.message ||
						`HTTP ${response.status}: ${response.statusText}`
				);
			}

			// Success
			setIsSubmitted(true);
			toast.success(
				"Support request submitted successfully! We'll get back to you soon."
			);
		} catch (error: any) {
			console.error("Support request error:", error);

			let errorMessage =
				"Failed to submit support request. Please try again.";

			if (error.message) {
				errorMessage = error.message;
			}

			toast.error(errorMessage);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleReset = () => {
		setFormData({
			email: "",
			message: "",
		});
		setIsSubmitted(false);
	};

	const isFormValid = formData.email.trim() && formData.message.trim();

	if (isSubmitted) {
		return (
			<div className="min-h-screen bg-slate-900 p-4 flex items-center justify-center">
				<Card className="w-full max-w-md bg-slate-800 border-slate-700">
					<CardContent className="p-8 text-center">
						<CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
						<h2 className="text-2xl font-semibold text-slate-50 mb-2">
							Message Sent!
						</h2>
						<p className="text-slate-400 mb-6">
							Thank you for contacting us. We&apos;ll get back to you
							within 24 hours.
						</p>
						<Button
							onClick={handleReset}
							className="w-full bg-green-500 hover:bg-green-600 text-white">
							Send Another Message
						</Button>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-slate-900 p-4">
			<div className="max-w-2xl mx-auto pt-8">
				{/* Header */}
				<div className="mb-8">
					<Button
						variant="ghost"
						className="text-slate-400 hover:text-slate-200 mb-4 p-0"
						onClick={() => window.history.back()}>
						<ArrowLeft className="w-4 h-4 mr-2" />
						Back
					</Button>
					<h1 className="text-3xl font-bold text-slate-50 mb-2">
						Contact Support
					</h1>
					<p className="text-slate-400">
						Need help? Send us a message and we&apos;ll get back to you
						as soon as possible.
					</p>
				</div>

				{/* Contact Info Card */}
				<Card className="bg-slate-800 border-slate-700 mb-6">
					<CardContent className="py-2 px-4">
						<div className="flex items-center gap-4">
							<div className="p-3 bg-green-500/20 rounded-lg">
								<Mail className="w-6 h-6 text-green-500" />
							</div>
							<div>
								<h3 className="text-slate-200 font-semibold">
									Email Support
								</h3>
								<p className="text-slate-400">
									<EMAIL>
								</p>
								<p className="text-slate-500 text-sm mt-1">
									We typically respond within 24 hours
								</p>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Contact Form Card */}
				<Card className="bg-slate-800 border-slate-700">
					<CardHeader>
						<CardTitle className="text-slate-50">
							Send us a message
						</CardTitle>
					</CardHeader>
					<CardContent className="p-6">
						<form onSubmit={handleSubmit} className="space-y-6">
							<div>
								<Label
									htmlFor="support-email"
									className="text-slate-200 mb-2 block">
									Email Address *
								</Label>
								<Input
									id="support-email"
									type="email"
									value={formData.email}
									onChange={(e) =>
										handleInputChange(
											"email",
											e.target.value
										)
									}
									placeholder="Enter your email address"
									className="bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:border-green-500 focus:ring-green-500"
									required
									disabled={isSubmitting}
								/>
							</div>

							<div>
								<Label
									htmlFor="support-message"
									className="text-slate-200 mb-2 block">
									Message *
								</Label>
								<Textarea
									id="support-message"
									value={formData.message}
									onChange={(e) =>
										handleInputChange(
											"message",
											e.target.value
										)
									}
									placeholder="Please describe your issue or inquiry in detail. Include any relevant information that might help us assist you better."
									className="bg-slate-700 border-slate-600 text-slate-100 placeholder:text-slate-400 focus:border-green-500 focus:ring-green-500 min-h-[150px]"
									required
									disabled={isSubmitting}
								/>
								<p className="text-slate-500 text-sm mt-2">
									The more details you provide, the better we
									can help you.
								</p>
							</div>

							<div className="flex flex-col sm:flex-row gap-3 pt-4">
								<Button
									type="submit"
									disabled={!isFormValid || isSubmitting}
									className="flex-1 bg-green-500 hover:bg-green-600 text-white disabled:opacity-50 disabled:cursor-not-allowed">
									{isSubmitting ? (
										<>
											<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
											Sending Message...
										</>
									) : (
										<>
											<Send className="w-4 h-4 mr-2" />
											Send Message
										</>
									)}
								</Button>
								<Button
									type="button"
									variant="outline"
									onClick={handleReset}
									disabled={isSubmitting}
									className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:text-slate-100 disabled:opacity-50">
									Clear Form
								</Button>
							</div>
						</form>
					</CardContent>
				</Card>

				{/* Footer */}
				<div className="mt-8 text-center text-slate-500 text-sm">
					<p>
						For urgent matters, please email us directly at{" "}
						<a
							href="mailto:<EMAIL>"
							className="text-green-500 hover:text-green-400 underline">
							<EMAIL>
						</a>
					</p>
				</div>
			</div>
		</div>
	);
};

export default ContactSupportPage;
