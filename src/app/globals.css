@import "tailwindcss";
@import "tw-animate-css";

@theme {
  /* Brand colors as RGB values */
  --color-brand-primary: 16 163 127;
  --color-brand-secondary: 47 129 247;

  /* Other colors as HSL/OKLCH values */
  --color-background: oklch(1 0 0);
  --color-foreground: oklch(0.298 0 0);
  --color-card: oklch(0.985 0 0);
  --color-card-foreground: oklch(0.298 0 0);
  --color-popover: oklch(1 0 0);
  --color-popover-foreground: oklch(0.298 0 0);
  --color-primary: oklch(0.488 0.175 162);
  --color-primary-foreground: oklch(1 0 0);
  --color-secondary: oklch(0.98 0 0);
  --color-secondary-foreground: oklch(0.298 0 0);
  --color-muted: oklch(0.98 0 0);
  --color-muted-foreground: oklch(0.647 0 0);
  --color-accent: oklch(0.488 0.175 162);
  --color-accent-foreground: oklch(1 0 0);
  --color-destructive: oklch(0.647 0.205 27);
  --color-destructive-foreground: oklch(1 0 0);
  --color-border: oklch(0.914 0 0);
  --color-input: oklch(0.914 0 0);
  --color-ring: oklch(0.488 0.175 162);

  /* Chart colors */
  --color-chart-1: oklch(0.488 0.175 162);
  --color-chart-2: oklch(0.618 0.164 221);
  --color-chart-3: oklch(0.647 0.162 262);
  --color-chart-4: oklch(0.748 0.204 43);
  --color-chart-5: oklch(0.647 0.205 27);

  /* Sidebar colors */
  --color-sidebar: oklch(1 0 0);
  --color-sidebar-foreground: oklch(0.298 0 0);
  --color-sidebar-primary: oklch(0.488 0.175 162);
  --color-sidebar-primary-foreground: oklch(1 0 0);
  --color-sidebar-accent: oklch(0.98 0 0);
  --color-sidebar-accent-foreground: oklch(0.298 0 0);
  --color-sidebar-border: oklch(0.914 0 0);
  --color-sidebar-ring: oklch(0.488 0.175 162);

  /* Border radius */
  --radius: 0.5rem;
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);

  /* Animation keyframes */
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
}

/* Dark mode theme */
@media (prefers-color-scheme: dark) {
  @theme {
    --color-background: oklch(0.298 0 0);
    --color-foreground: oklch(1 0 0);
    --color-card: oklch(0.361 0 0);
    --color-card-foreground: oklch(1 0 0);
    --color-popover: oklch(0.361 0 0);
    --color-popover-foreground: oklch(1 0 0);
    --color-primary: oklch(0.488 0.175 162);
    --color-primary-foreground: oklch(1 0 0);
    --color-secondary: oklch(0.361 0 0);
    --color-secondary-foreground: oklch(1 0 0);
    --color-muted: oklch(0.361 0 0);
    --color-muted-foreground: oklch(0.651 0 0);
    --color-accent: oklch(0.488 0.175 162);
    --color-accent-foreground: oklch(1 0 0);
    --color-destructive: oklch(0.658 0.157 0);
    --color-destructive-foreground: oklch(1 0 0);
    --color-border: oklch(0.361 0 0);
    --color-input: oklch(0.361 0 0);
    --color-ring: oklch(0.488 0.175 162);

    /* Chart colors for dark mode */
    --color-chart-1: oklch(0.488 0.175 162);
    --color-chart-2: oklch(0.618 0.164 221);
    --color-chart-3: oklch(0.647 0.162 262);
    --color-chart-4: oklch(0.748 0.204 43);
    --color-chart-5: oklch(0.658 0.157 0);

    /* Sidebar colors for dark mode */
    --color-sidebar: oklch(0.361 0 0);
    --color-sidebar-foreground: oklch(1 0 0);
    --color-sidebar-primary: oklch(0.488 0.175 162);
    --color-sidebar-primary-foreground: oklch(1 0 0);
    --color-sidebar-accent: oklch(0.361 0 0);
    --color-sidebar-accent-foreground: oklch(1 0 0);
    --color-sidebar-border: oklch(0.361 0 0);
    --color-sidebar-ring: oklch(0.488 0.175 162);
  }
}

/* Support for manual dark mode class */
@custom-variant dark (&:is(.dark *));

:root {
  /* Keep RGB values for backward compatibility */
  --brand-primary: 16 163 127;
  --brand-secondary: 47 129 247;
}

.dark {
  /* Dark mode can also be applied via class */
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground transition-colors duration-200;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-weight: 400;
  }

  /* Ensure smooth theme transitions */
  html {
    @apply transition-colors duration-200;
  }

  /* Default light theme */
  :root {
    --color-background: oklch(1 0 0);
    --color-foreground: oklch(0.298 0 0);
    --color-card: oklch(1 0 0);
    --color-card-foreground: oklch(0.298 0 0);
    --color-popover: oklch(1 0 0);
    --color-popover-foreground: oklch(0.298 0 0);
    --color-primary: oklch(0.488 0.175 162);
    --color-primary-foreground: oklch(1 0 0);
    --color-secondary: oklch(0.961 0 0);
    --color-secondary-foreground: oklch(0.298 0 0);
    --color-muted: oklch(0.961 0 0);
    --color-muted-foreground: oklch(0.651 0 0);
    --color-accent: oklch(0.961 0 0);
    --color-accent-foreground: oklch(0.298 0 0);
    --color-destructive: oklch(0.658 0.157 0);
    --color-destructive-foreground: oklch(1 0 0);
    --color-border: oklch(0.898 0 0);
    --color-input: oklch(0.898 0 0);
    --color-ring: oklch(0.488 0.175 162);
  }

  /* Dark theme overrides */
  .dark {
    --color-background: oklch(0.298 0 0);
    --color-foreground: oklch(1 0 0);
    --color-card: oklch(0.361 0 0);
    --color-card-foreground: oklch(1 0 0);
    --color-popover: oklch(0.361 0 0);
    --color-popover-foreground: oklch(1 0 0);
    --color-primary: oklch(0.488 0.175 162);
    --color-primary-foreground: oklch(1 0 0);
    --color-secondary: oklch(0.361 0 0);
    --color-secondary-foreground: oklch(1 0 0);
    --color-muted: oklch(0.361 0 0);
    --color-muted-foreground: oklch(0.651 0 0);
    --color-accent: oklch(0.488 0.175 162);
    --color-accent-foreground: oklch(1 0 0);
    --color-destructive: oklch(0.658 0.157 0);
    --color-destructive-foreground: oklch(1 0 0);
    --color-border: oklch(0.361 0 0);
    --color-input: oklch(0.361 0 0);
    --color-ring: oklch(0.488 0.175 162);
  }
}

/* ===== CHATGPT-INSPIRED COMPONENT STYLES ===== */
@layer components {

  /* Primary Gradient Utilities */
  .bg-gradient-brand {
    background: linear-gradient(135deg, rgb(var(--brand-primary)), rgb(var(--brand-secondary)));
  }

  .bg-gradient-brand-reverse {
    background: linear-gradient(135deg, rgb(var(--brand-secondary)), rgb(var(--brand-primary)));
  }

  .bg-gradient-brand-vertical {
    background: linear-gradient(to bottom, rgb(var(--brand-primary)), rgb(var(--brand-secondary)));
  }

  .bg-gradient-brand-subtle {
    background: linear-gradient(135deg, rgba(16, 163, 127, 0.1), rgba(47, 129, 247, 0.1));
  }

  /* Text Gradients */
  .text-gradient-brand {
    background: linear-gradient(135deg, rgb(var(--brand-primary)), rgb(var(--brand-secondary)));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* ChatGPT-style Button Components - UPDATED FOR V4 */
  .btn-brand-primary {
    @apply bg-brand-primary text-white font-medium rounded-lg px-4 py-2 transition-all duration-200 hover:bg-brand-primary/90 focus:outline-none focus:ring-2 focus:ring-brand-primary/50;
  }

  .btn-brand-secondary {
    @apply bg-transparent border border-brand-primary text-brand-primary font-medium rounded-lg px-4 py-2 transition-all duration-200 hover:bg-brand-primary hover:text-white focus:outline-none focus:ring-2 focus:ring-brand-primary/50;
  }

  .btn-brand-outline {
    @apply border border-border text-foreground font-medium rounded-lg px-4 py-2 transition-all duration-200 hover:border-brand-primary hover:text-brand-primary focus:outline-none focus:ring-2 focus:ring-brand-primary/50;
  }

  .btn-brand-ghost {
    @apply bg-transparent text-muted-foreground font-medium rounded-lg px-4 py-2 transition-all duration-200 hover:bg-muted hover:text-foreground focus:outline-none focus:ring-2 focus:ring-brand-primary/50;
  }

  /* ChatGPT-style Card Components */
  .card-brand {
    @apply rounded-lg shadow-sm border border-border bg-card text-card-foreground transition-shadow duration-200 hover:shadow-md;
  }

  .card-brand-featured {
    @apply rounded-lg shadow-md border-2 border-brand-primary bg-card text-card-foreground transition-all duration-200 hover:shadow-lg;
  }

  .card-brand-interactive {
    @apply rounded-lg border border-border bg-card text-card-foreground transition-all duration-200 hover:border-brand-primary hover:shadow-md cursor-pointer;
  }

  /* Navigation Components */
  .nav-link-brand {
    @apply transition-colors duration-200 font-medium text-muted-foreground hover:text-brand-primary;
  }

  .nav-link-brand-active {
    @apply font-medium text-brand-primary;
  }

  /* Badge/Tag Components */
  .badge-brand {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-brand-primary text-white;
  }

  .badge-brand-outline {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border border-brand-primary text-brand-primary bg-transparent;
  }

  .badge-brand-secondary {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-brand-secondary text-white;
  }

  /* Input Components - UPDATED FOR V4 */
  .input-brand {
    @apply w-full rounded-lg border border-input bg-background px-3 py-2 text-sm transition-colors focus:border-brand-primary focus:outline-none focus:ring-2 focus:ring-brand-primary/20;
  }

  .textarea-brand {
    @apply w-full rounded-lg border border-input bg-background px-3 py-2 text-sm transition-colors focus:border-brand-primary focus:outline-none focus:ring-2 focus:ring-brand-primary/20 resize-none;
  }

  /* ChatGPT-style Message Components */
  .message-user {
    @apply ml-auto max-w-[80%] rounded-lg bg-brand-primary text-white px-4 py-2 font-medium;
  }

  .message-assistant {
    @apply mr-auto max-w-[80%] rounded-lg bg-card border border-border px-4 py-2;
  }

  /* Dashboard specific components - UPDATED FOR V4 */
  .dashboard-button {
    @apply bg-brand-primary text-white px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:bg-brand-primary/90;
  }

  .logout-button {
    @apply bg-transparent border border-destructive text-destructive px-4 py-2 rounded-lg font-medium transition-all duration-200 hover:bg-destructive hover:text-white;
  }

  /* Loading states */
  .loading-brand {
    @apply animate-pulse bg-muted rounded;
  }

  /* Utility Classes */
  .shadow-brand {
    box-shadow: 0 4px 14px 0 rgba(16, 163, 127, 0.15);
  }

  .shadow-brand-lg {
    box-shadow: 0 10px 25px 0 rgba(16, 163, 127, 0.2);
  }
}

/* ===== BRAND COLOR UTILITIES ===== */
@layer utilities {

  /* Brand Colors */
  .text-brand-primary {
    color: rgb(var(--brand-primary));
  }

  .text-brand-secondary {
    color: rgb(var(--brand-secondary));
  }

  .bg-brand-primary {
    background-color: rgb(var(--brand-primary));
  }

  .bg-brand-secondary {
    background-color: rgb(var(--brand-secondary));
  }

  .border-brand-primary {
    border-color: rgb(var(--brand-primary));
  }

  .border-brand-secondary {
    border-color: rgb(var(--brand-secondary));
  }
}

/* ===== KEYFRAMES ===== */
@keyframes accordion-down {
  from {
    height: 0;
  }

  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }

  to {
    height: 0;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== CHATGPT-STYLE SCROLLBARS ===== */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 10px;
  transition: all 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--brand-primary));
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* ===== DARK MODE SCROLLBARS ===== */
.dark ::-webkit-scrollbar-track {
  background: transparent;
}

.dark ::-webkit-scrollbar-thumb {
  background: hsla(215, 20.2%, 65.1%, 0.5);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--brand-primary));
}

/* ===== FIREFOX SCROLLBARS ===== */
* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground)) transparent;
}

.dark * {
  scrollbar-color: hsla(215, 20.2%, 65.1%, 0.5) transparent;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }
}

/* ===== ANIMATIONS ===== */
.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

/* ===== FOCUS STATES ===== */
.focus-brand:focus {
  outline: 2px solid rgb(var(--brand-primary));
  outline-offset: 2px;
}