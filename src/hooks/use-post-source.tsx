'use client';
import { getAccessToken } from "@/lib";
import { $http } from "@/lib/api/http";
import { mapFiltersToSearchRequest } from "@/lib/api/inspectora-api";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useState, useCallback, useContext } from "react";
import { toast } from "sonner";
import { createContext } from "react";


interface ContextType {
  mutate: (filters: FilterValues) => void;
  isPending: boolean;
  error: any;
  data: FilterResponse | undefined;
}

export interface FilterValues {
    "title": string,
    "price": number,
    "target_stores": number
}

export interface FilterResponse {
    success: boolean;
    message: string;
    data: {
        results: {
            store_name: string;
            price: number;
            product_url: string;
            availability: string;
            condition: string;
            shipping_cost: number;
            total_cost: number;
        }[];
        search_summary: string;
        total_results_found: number;
    };
    timestamp: string;
    search_params: {
        title: string;
        price_to_beat: number;
        target_stores: number;
    };
}

const InspectoraContext = createContext<ContextType | undefined>(undefined);

export const SourceOraProvider = ({ children }: { children: React.ReactNode }) => {

  const accessToken = typeof window !== 'undefined' ? getAccessToken() : null;
  const [source, setSource] = useState<FilterResponse>();

  const getSourceOra = () => {
    return useMutation({
      mutationFn: async (filters : FilterValues) => {
        const response = await $http.post<FilterResponse>(`/api/v1/sourceora/search-product`, filters, {
          headers: {
            accept: "application/json",
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`
          },
        });
        setSource(response.data as FilterResponse)
        return response.data
      },
      onSuccess: (data) => {
        console.log("Search results:", data);
        toast.success("Search successful");
      },
      onError: (error) => {
        console.error("Search error:", error);
        toast.error("Search failed");
      },
    });
  }

  const { mutate, isPending, error, } = getSourceOra();

  return <InspectoraContext.Provider 
    value={{ 
      mutate, 
      isPending, 
      error, 
      data: source,
    }}>
      {children}
    </InspectoraContext.Provider>;
};

export const useSourceora = () => {
  const context = useContext(InspectoraContext);
  if (!context) {
    throw new Error("useInspectoraAsins must be used within a InspectoraAsinsProvider");
  }
  return context;
};
