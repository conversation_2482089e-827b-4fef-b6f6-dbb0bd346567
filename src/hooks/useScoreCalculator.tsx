
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "./use-auth";
import { getAccessToken } from "@/lib";
import { $http } from "@/lib/api/http";


interface ScoreRequest {
  amazon_in_bb: boolean;
  asin_code: string;
  bsr: number;
  est_sales: number;
  gated: boolean;
  profit: number;
  roi: number;
  stock_level: number;
  amazon_buybox: boolean;
  fullResponse: boolean;
  isVatRegistered: boolean;
}

export const useScoreCalculator = ({
  ScoreRequest,
}: {
  ScoreRequest: ScoreRequest;
}) => {
  const { user } = useAuth();
  const queryKey = [
    `score-calculator`,
    ScoreRequest?.asin_code,
    ScoreRequest.profit,
    ScoreRequest.roi,
    user,
    ScoreRequest.isVatRegistered,
  ];
  const accessToken = getAccessToken();

  return useQuery({
    queryKey,
    queryFn: async () => {
      const response = await $http.post(
        `extension-backend/api/v1/weight/score`,
        ScoreRequest,
        {
          headers: {
            accept: "application/json",
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );
      return response.data;
    },
    enabled: !!ScoreRequest.asin_code && !!user && !!ScoreRequest.fullResponse,
  });
};
