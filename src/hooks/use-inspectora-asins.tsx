'use client';
import { FilterValues } from "@/constants/inspectoral-filter";
import { getAccessToken } from "@/lib";
import { $http } from "@/lib/api/http";
import { mapFiltersToSearchRequest } from "@/lib/api/inspectora-api";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useState, useCallback, useContext } from "react";
import { toast } from "sonner";
import { createContext } from "react";


interface ContextType {
  mutate: (filters: FilterValues) => void;
  isPending: boolean;
  error: any;
  data: string[];
  currentTab: string;
  setCurrentTab: React.Dispatch<React.SetStateAction<string>>;
}

const InspectoraContext = createContext<ContextType | undefined>(undefined);

export const InspectoraAsinsProvider = ({ children }: { children: React.ReactNode }) => {

  const accessToken = typeof window !== 'undefined' ? getAccessToken() : null;
  const [asins, setAsin] = useState<string[]>([]);
  const [currentTab, setCurrentTab] = useState<string>("filter");


  const getAsins = () => {
    return useMutation({
      mutationFn: async (filters : FilterValues) => {
        const searchRequest = mapFiltersToSearchRequest(filters);
        const response = await $http.post(`extension-backend/api/v1/inspectora/search`, searchRequest, {
          headers: {
            accept: "application/json",
            "Content-Type": "application/json",
            Authorization: `Bearer ${accessToken}`,
          },
        });
        setAsin(response.data)
        return response.data
      },
      onSuccess: (data) => {
        console.log("Search results:", data);
        setCurrentTab("result");
        toast.success("Search successful");
      },
      onError: (error) => {
        console.error("Search error:", error);
        toast.error("Search failed");
      },
    });
  }

  const { mutate, isPending, error, } = getAsins();

  return <InspectoraContext.Provider 
    value={{ 
      mutate, 
      isPending, 
      error, 
      data: asins,
      currentTab,
      setCurrentTab,
    }}>
      {children}
    </InspectoraContext.Provider>;
};

export const useInspectoraAsins = () => {
  const context = useContext(InspectoraContext);
  if (!context) {
    throw new Error("useInspectoraAsins must be used within a InspectoraAsinsProvider");
  }
  return context;
};
