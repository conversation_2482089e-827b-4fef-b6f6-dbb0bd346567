'use client'
// src/hooks/useTheme.tsx
import { createContext, useContext, useState, useEffect, ReactNode } from "react";

export type Theme = "dark" | "light";

interface ThemeContextType {
	theme: Theme;
	isDarkMode: boolean;
	toggleTheme: () => void;
	mounted: boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: ReactNode }) {
	const [theme, setTheme] = useState<Theme>("dark");
	const [mounted, setMounted] = useState(false);

	useEffect(() => {
		setMounted(true);

		// Only access localStorage after mounting
		try {
			const savedTheme = localStorage.getItem("theme") as Theme;
			if (
				savedTheme &&
				(savedTheme === "dark" || savedTheme === "light")
			) {
				setTheme(savedTheme);
				applyTheme(savedTheme);
			} else {
				// Check system preference
				const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
				const defaultTheme = systemPrefersDark ? "dark" : "light";
				setTheme(defaultTheme);
				applyTheme(defaultTheme);
				localStorage.setItem("theme", defaultTheme);
			}
		} catch {
			// Fallback if localStorage is not available
			applyTheme("dark");
		}
	}, []);

	const applyTheme = (newTheme: Theme) => {
		if (typeof document === "undefined") return;

		const root = document.documentElement;

		if (newTheme === "dark") {
			root.classList.add("dark");
			root.classList.remove("light");
		} else {
			root.classList.add("light");
			root.classList.remove("dark");
		}

		// Force a re-render by updating a CSS custom property
		root.style.setProperty('--theme-updated', Date.now().toString());
	};

	const toggleTheme = () => {
		if (!mounted) return;

		const newTheme: Theme = theme === "dark" ? "light" : "dark";
		setTheme(newTheme);
		applyTheme(newTheme);

		try {
			localStorage.setItem("theme", newTheme);
		} catch (error) {
			// Handle localStorage errors gracefully
			console.warn("Failed to save theme to localStorage:", error);
		}
	};

	const value = {
		theme: mounted ? theme : "dark",
		isDarkMode: mounted ? theme === "dark" : true,
		toggleTheme,
		mounted,
	};

	return (
		<ThemeContext.Provider value={value}>
			{children}
		</ThemeContext.Provider>
	);
}

export function useTheme() {
	const context = useContext(ThemeContext);
	if (context === undefined) {
		throw new Error('useTheme must be used within a ThemeProvider');
	}
	return context;
}
