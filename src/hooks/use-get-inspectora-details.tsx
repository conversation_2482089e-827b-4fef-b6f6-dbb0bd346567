"use client";

import { useMutation } from "@tanstack/react-query";
import { getAccessToken } from "@/lib";
import { $http } from "@/lib/api/http";
import { createContext, useContext, useState } from 'react';
import { SearchResult } from "@/components/deals-finder/inspectora-result";

type SortField = keyof SearchResult;
type SortDirection = "asc" | "desc";

interface InspectoraDetailsContextType {
  mutate: (asins: string[]) => void;
  isPending: boolean;
  error: any;
  data: any;
  results: SearchResult[];
  setResults: React.Dispatch<React.SetStateAction<SearchResult[]>>;
  sortField: SortField;
  setSortField: React.Dispatch<React.SetStateAction<SortField>>;
  sortDirection: SortDirection;
  setSortDirection: React.Dispatch<React.SetStateAction<SortDirection>>;
  currentPage: number;
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>;
  itemsPerPage: number;
  setItemsPerPage: React.Dispatch<React.SetStateAction<number>>;
  sourceoraModal: boolean;
  setSourceoraModal: React.Dispatch<React.SetStateAction<boolean>>;
  checkoraModal: boolean;
  setCheckoraModal: React.Dispatch<React.SetStateAction<boolean>>;
  checkModalAsin: string | null;
  setCheckModalAsin: React.Dispatch<React.SetStateAction<string | null>>;
  currentBatch: number;
  setCurrentBatch: React.Dispatch<React.SetStateAction<number>>;
  isLoadingMore: boolean;
  setIsLoadingMore: React.Dispatch<React.SetStateAction<boolean>>;
  loadingError: string | null;
  setLoadingError: React.Dispatch<React.SetStateAction<string | null>>;
  handleSort: (field: SortField) => void;
  handlePageChange: (page: number) => void;
  resetStates: () => void;
  loadNextBatch: (cachedAsins: string[], maxAsinPerBatch: number) => Promise<void>;
}

const InspectoraDetailsContext = createContext<InspectoraDetailsContextType | undefined>(undefined);

export const useInspectoraDetailsContext = () => {
  const context = useContext(InspectoraDetailsContext);
  if (!context) {
    throw new Error('useInspectoraDetailsContext must be used within an InspectoraDetailsProvider');
  }
  return context;
};

export const InspectoraDetailsProvider = ({ children }: { children: React.ReactNode }) => {
  const [results, setResults] = useState<SearchResult[]>([]);
  const accessToken = typeof window !== 'undefined' ? getAccessToken() : null;
  const [sortField, setSortField] = useState<SortField>("id");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(25);
  const [sourceoraModal, setSourceoraModal] = useState(false);
  const [checkoraModal, setCheckoraModal] = useState(false);
  const [checkModalAsin, setCheckModalAsin] = useState<string | null>(null);
  const [currentBatch, setCurrentBatch] = useState(0);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [loadingError, setLoadingError] = useState<string | null>(null);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const resetStates = () => {
    setResults([]);
    setSortField("id");
    setSortDirection("asc");
    setCurrentPage(1);
    setItemsPerPage(25);
    setSourceoraModal(false);
    setCheckoraModal(false);
    setCheckModalAsin(null);
    setCurrentBatch(0);
    setIsLoadingMore(false);
    setLoadingError(null);
  };

  const loadNextBatch = async (cachedAsins: string[], maxAsinPerBatch: number) => {
    const totalBatches = Math.ceil(cachedAsins.length / maxAsinPerBatch);
    const hasMoreBatches = currentBatch < totalBatches - 1;
    const loadedProductCount = results.length;

    if (isLoadingMore || !hasMoreBatches) {
      return;
    }

    setIsLoadingMore(true);
    setLoadingError(null);

    try {
      const nextBatch = currentBatch + 1;
      const startIndex = nextBatch * maxAsinPerBatch;
      const endIndex = Math.min(startIndex + maxAsinPerBatch, cachedAsins.length);
      const batchAsins = cachedAsins.slice(startIndex, endIndex);

      console.log(
        `📦 Loading batch ${nextBatch + 1}/${totalBatches}: ${batchAsins.length} products (max 10)`
      );

      const { getProductDetails } = await import("@/lib/api/inspectora-api");
      const response = await getProductDetails(batchAsins);

      if (!response.success) {
        throw new Error(response.error || "Failed to load product details");
      }

      const mapProductToSearchResult = (product: any, index: number) => ({
        id: index + 1,
        asin: product.asin,
        title: product.product_name || "Unknown Product",
        price: product.list_price ? product.list_price.toString() : "0",
        sales_rank: product.bsr || 0,
        item_weight: product.weight
          ? `${product.weight} ${product.weight_unit || "kg"}`
          : "N/A",
        amazon_in_buybox: Math.random() > 0.5,
        amazon_in_listing: !product.adult_product,
        amazon_url: product.amazon_url,
        category: product.category || "Unknown",
        manufacturer: product.manufacturer || "Unknown",
      });

      const newProducts = response.data.successful_results.map(
        (result: any, idx: number) =>
          mapProductToSearchResult(result.data, loadedProductCount + idx)
      );

      const updatedResults = [...results, ...newProducts];
      console.log('updated results', updatedResults);
      setResults(updatedResults);
      setCurrentBatch(nextBatch);

      console.log(
        `✅ Loaded ${newProducts.length} products. Total: ${updatedResults.length}/${cachedAsins.length}`
      );
    } catch (error) {
      console.error("❌ Failed to load batch:", error);
      setLoadingError(
        error instanceof Error ? error.message : "Failed to load more products"
      );
    } finally {
      setIsLoadingMore(false);
    }
  };

  const mutation = useMutation({
    mutationFn: async (asins: string[]) => {
      try {
        if (asins.length === 0) {
          throw new Error("No ASIN provided");
        }

        const response = await $http.post(
          `/amazon/api/v1/amz-product/catalog-item`,
          {
            asins,
          },
          {
            headers: {
              accept: "application/json",
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );

        return response.data;
      } catch (error) {
        console.error(`❌ Error fetching Inspectora details:`, error);
        throw error;
      }
    },
    onSuccess: (data) => {
        const newResults = data.data.successful_results
        console.log('hello here', newResults)
        const updatedResults = [...results, ...newResults];
        setResults(updatedResults);
    },
  });

  const { mutate, isPending, error, data } = mutation;

  return (
    <InspectoraDetailsContext.Provider
      value={{
        mutate,
        isPending,
        error,
        data,
        results,
        setResults,
        sortField,
        setSortField,
        sortDirection,
        setSortDirection,
        currentPage,
        setCurrentPage,
        itemsPerPage,
        setItemsPerPage,
        sourceoraModal,
        setSourceoraModal,
        checkoraModal,
        setCheckoraModal,
        checkModalAsin,
        setCheckModalAsin,
        currentBatch,
        setCurrentBatch,
        isLoadingMore,
        setIsLoadingMore,
        loadingError,
        setLoadingError,
        handleSort,
        handlePageChange,
        resetStates,
        loadNextBatch,
      }}
    >
      {children}
    </InspectoraDetailsContext.Provider>
  );
};
