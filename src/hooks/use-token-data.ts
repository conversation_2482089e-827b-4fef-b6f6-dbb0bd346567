/* eslint-disable @typescript-eslint/no-explicit-any */
// hooks/use-token-data.ts
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { tokenAPI } from "@/lib/api/token";

// Hook for token balance
export const useTokenBalance = () => {
	return useQuery({
		queryKey: ["tokenBalance"],
		queryFn: tokenAPI.getBalance,
		refetchInterval: 2 * 60 * 1000, // Refetch every 2 minutes
		staleTime: 1 * 60 * 1000, // Consider data stale after 1 minute
		gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes (previously cacheTime)
		retry: (failureCount, error: any) => {
			// Don't retry on authentication errors
			if (
				error?.response?.status === 401 ||
				error?.response?.status === 403
			) {
				return false;
			}
			return failureCount < 3;
		},
	});
};

// Hook for token history
export const useTokenHistory = (limit: number = 50) => {
	return useQuery({
		queryKey: ["tokenHistory", limit],
		queryFn: () => tokenAPI.getTokenHistory(limit),
		staleTime: 5 * 60 * 1000, // Consider data stale after 5 minutes
		gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
		retry: (failureCount, error: any) => {
			if (
				error?.response?.status === 401 ||
				error?.response?.status === 403
			) {
				return false;
			}
			return failureCount < 3;
		},
	});
};

// Hook for usage history
export const useUsageHistory = (limit: number = 100) => {
	return useQuery({
		queryKey: ["usageHistory", limit],
		queryFn: () => tokenAPI.getUsageHistory(limit),
		staleTime: 2 * 60 * 1000, // Consider data stale after 2 minutes
		gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
		retry: (failureCount, error: any) => {
			if (
				error?.response?.status === 401 ||
				error?.response?.status === 403
			) {
				return false;
			}
			return failureCount < 3;
		},
	});
};

// Combined hook for all token data
export const useTokenData = () => {
	const balance = useTokenBalance();
	const history = useTokenHistory();
	const usage = useUsageHistory();
	const queryClient = useQueryClient();

	const refetchAll = async () => {
		try {
			await Promise.all([
				balance.refetch(),
				history.refetch(),
				usage.refetch(),
			]);
		} catch (error) {
			console.error("Error refetching token data:", error);
		}
	};

	const invalidateAll = () => {
		queryClient.invalidateQueries({ queryKey: ["tokenBalance"] });
		queryClient.invalidateQueries({ queryKey: ["tokenHistory"] });
		queryClient.invalidateQueries({ queryKey: ["usageHistory"] });
	};

	return {
		balance: balance.data,
		tokenHistory: history.data,
		usageHistory: usage.data,
		isLoading: balance.isLoading || history.isLoading || usage.isLoading,
		isError: balance.isError || history.isError || usage.isError,
		error: balance.error || history.error || usage.error,
		refetch: refetchAll,
		invalidateAll,
		// Individual loading states if needed
		balanceLoading: balance.isLoading,
		historyLoading: history.isLoading,
		usageLoading: usage.isLoading,
		// Individual error states if needed
		balanceError: balance.isError,
		historyError: history.isError,
		usageError: usage.isError,
	};
};

// Hook specifically for token purchase success handling
export const useTokenPurchaseSuccess = () => {
	const queryClient = useQueryClient();

	const handlePurchaseSuccess = () => {
		// Invalidate all token-related queries to refetch fresh data
		queryClient.invalidateQueries({ queryKey: ["tokenBalance"] });
		queryClient.invalidateQueries({ queryKey: ["tokenHistory"] });
		queryClient.invalidateQueries({ queryKey: ["usageHistory"] });

		// Also invalidate payment methods in case default changed
		queryClient.invalidateQueries({ queryKey: ["payment-methods"] });
	};

	return { handlePurchaseSuccess };
};
