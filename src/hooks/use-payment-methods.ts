/* eslint-disable @typescript-eslint/no-explicit-any */
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
	getPaymentMethods,
	setDefaultPaymentMethod,
	deletePaymentMethod,
} from "@/lib/api/stripe-api";
import { toast } from "sonner";

export const usePaymentMethods = () => {
	return useQuery({
		queryKey: ["payment-methods"],
		queryFn: getPaymentMethods,
		staleTime: 5 * 60 * 1000, // 5 minutes
		gcTime: 10 * 60 * 1000, // 10 minutes (previously cacheTime)
		retry: (failureCount, error: any) => {
			// Don't retry on 401/403 errors
			if (
				error?.response?.status === 401 ||
				error?.response?.status === 403
			) {
				return false;
			}
			return failureCount < 3;
		},
	});
};

export const useSetDefaultPaymentMethod = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: (paymentMethodId: string) => {
			// Use stripe_payment_method_id for the API call
			return setDefaultPaymentMethod(paymentMethodId);
		},
		onSuccess: () => {
			toast.success("Default payment method updated successfully");
			// Invalidate and refetch payment methods
			queryClient.invalidateQueries({ queryKey: ["payment-methods"] });
			// Also invalidate token balance in case it affects display
			queryClient.invalidateQueries({ queryKey: ["tokenBalance"] });
		},
		onError: (error: any) => {
			console.error("Set default payment method error:", error);
			const errorMessage =
				error.message || "Failed to set default payment method";
			toast.error(errorMessage);
		},
	});
};

export const useDeletePaymentMethod = () => {
	const queryClient = useQueryClient();
	return useMutation({
		mutationFn: (paymentMethodId: string) => {
			// Use stripe_payment_method_id for the API call
			return deletePaymentMethod(paymentMethodId);
		},
		onSuccess: () => {
			toast.success("Payment method deleted successfully");
			// Invalidate and refetch payment methods
			queryClient.invalidateQueries({ queryKey: ["payment-methods"] });
			// Also invalidate token balance in case it affects display
			queryClient.invalidateQueries({ queryKey: ["tokenBalance"] });
		},
		onError: (error: any) => {
			console.error("Delete payment method error:", error);
			const errorMessage =
				error.message || "Failed to delete payment method";
			toast.error(errorMessage);
		},
	});
};

// Additional utility hook for payment method operations
export const usePaymentMethodOperations = () => {
	const setDefault = useSetDefaultPaymentMethod();
	const deleteMethod = useDeletePaymentMethod();
	const {
		data: paymentMethods,
		isLoading,
		error,
		refetch,
	} = usePaymentMethods();

	return {
		paymentMethods: paymentMethods || [],
		isLoading,
		error,
		refetch,
		setDefault: setDefault.mutate,
		deleteMethod: deleteMethod.mutate,
		isSettingDefault: setDefault.isPending,
		isDeleting: deleteMethod.isPending,
		isOperating: setDefault.isPending || deleteMethod.isPending,
	};
};
