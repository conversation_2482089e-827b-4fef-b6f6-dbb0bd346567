// src/hooks/useTime.ts
import { useState, useEffect } from 'react';

/**
 * Custom hook for real-time clock functionality
 * Updates every second and returns current time
 */
export function useTime() {
  const [time, setTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return time;
}