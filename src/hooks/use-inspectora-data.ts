/* eslint-disable @typescript-eslint/no-explicit-any */
import { useQuery } from "@tanstack/react-query";
import {
	fetchInspectoraData,
	inspectoraKeys,
	normalizeCountryCode,
} from "@/lib/api/inspectora-api";
import { useMemo } from "react";
import { FilterValues, InspectoraApiResponse } from "@/constants/inspectoral-filter";

// Hook for fetching inspectora data
export const useInspectoraData = (
	asin: string | null,
	country: string | null
) => {
	return useQuery({
		queryKey: inspectoraKeys.productData(asin || "", country || ""),
		queryFn: () => {
			if (!asin || !country) {
				throw new Error("ASIN and country are required");
			}

			const normalizedCountry = normalizeCountryCode(country);
			return fetchInspectoraData(asin, normalizedCountry);
		},
		enabled: !!(asin && country), // Only run if both are present
		retry: (failureCount, error: any) => {
			// Don't retry for client errors (4xx)
			if (
				error?.response?.status >= 400 &&
				error?.response?.status < 500
			) {
				return false;
			}
			return failureCount < 2;
		},
		staleTime: 5 * 60 * 1000,
		gcTime: 10 * 60 * 1000,
		refetchOnWindowFocus: false,
	});
};

export const mapApiResponseToFilters = (
	data: InspectoraApiResponse,
	originalCountry?: string
): FilterValues => {
	// Enhanced country mapping
	const countryToMarketplaceMap: Record<string, string> = {
		us: "us",
		uk: "uk",
		gb: "uk",
		de: "de",
		fr: "fr",
		it: "it",
		es: "es",
		ca: "ca",
		jp: "jp",
	};

	// Use original country if provided, otherwise map from API response
	const marketplaceId = originalCountry
		? normalizeCountryCode(originalCountry)
		: countryToMarketplaceMap[data.market_id?.toLowerCase()] ||
		  data.market_id?.toLowerCase() ||
		  "";

	return {
		current_SALES_gte: "",
		current_SALES_lte: "",
		isLowest_SALES: null,
		marketplace_id: marketplaceId,
		item_height_between: {
			min: data.itemHeight_gte?.toString() || "",
			max: data.itemHeight_lte?.toString() || "",
		},
		item_width_between: {
			min: data.itemWidth_gte?.toString() || "",
			max: data.itemWidth_lte?.toString() || "",
		},
		item_weight_between: {
			min: data.itemWeight_gte?.toString() || "",
			max: data.itemWeight_lte?.toString() || "",
		},
		brand: data.brand || "",
		sales_rank_between: {
			min: "",
			max: data.current_SALES_lte?.toString() || "",
		},
		amazon_in_buybox: data.amz_in_buy_box,
		amazon_in_listing: data.availabilityAmazon,
		buy_price_between: {
			min: data.current_AMAZON_gte?.toString() || "",
			max: data.current_AMAZON_lte?.toString() || "",
		},
	};
};

// Hook that combines data fetching and mapping
export const useInspectoraFilters = (
	asin: string | null,
	country: string | null
) => {
	const { data, isLoading, error, isSuccess, refetch } = useInspectoraData(
		asin,
		country
	);

	const mappedFilters = useMemo(() => {
		return data
			? mapApiResponseToFilters(data, country || undefined)
			: null;
	}, [data, country]); // Only recalculate when data or country actually changes

	return {
		filters: mappedFilters,
		rawData: data,
		isLoading,
		error,
		isSuccess,
		refetch,
		hasData: !!data,
		hasError: !!error,
	};
};
