/* eslint-disable @typescript-eslint/no-explicit-any */
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect, useRef } from "react";
import { authApi, tokenUtils } from "@/lib/api/auth";
import { updateAuthState } from "@/lib/api/client";
import {
	LoginCredentials,
	RegisterCredentials,
	SellerRegistrationData,
	SellerOnboardingData,
	TokenResponse,
	User,
} from "@/types/auth";
import { useAuthStore } from "@/stores/auth-store";

export const useAuth = () => {
	const queryClient = useQueryClient();
	const initializingRef = useRef(false);
	const {
		user,
		token,
		isAuthenticated,
		isLoggingIn,
		isLoggingOut,
		login: setLoginState,
		logout: setLogoutState,
		setLoggingIn,
		setLoggingOut,
		setUser,
	} = useAuthStore();

	const {
		data: profile,
		isLoading: isLoadingProfile,
		error: profileError,
		refetch: refetchProfile,
	} = useQuery({
		queryKey: ["auth", "profile"],
		queryFn: authApi.getProfile,
		enabled: !!tokenUtils.getAccessToken() && !user,
		retry: false,
		staleTime: 1000 * 60 * 5,
		refetchOnWindowFocus: false,
		refetchOnMount: false,
		refetchOnReconnect: false,
	});


	const loginMutation = useMutation({
		mutationFn: authApi.login,
		onMutate: () => {
			setLoggingIn(true);
		},
		onSuccess: async (data: TokenResponse) => {
			tokenUtils.setTokens(data.access_token, data.refresh_token);

			try {
				// Update auth state - profile will be fetched by React Query
				updateAuthState(true);

				// Invalidate queries to trigger profile refetch
				queryClient.invalidateQueries({ queryKey: ["auth", "profile"] });
			} catch (error) {
				console.error("❌ Failed to complete login:", error);
				tokenUtils.clearTokens();
				updateAuthState(false);
				throw error;
			}
		},
		onError: (error: any) => {
			console.error("❌ Login failed:", error);
			tokenUtils.clearTokens();
			updateAuthState(false);
		},
		onSettled: () => {
			setLoggingIn(false);
		},
	});

	// Register mutation
	const registerMutation = useMutation({
		mutationFn: authApi.register,
		onSuccess: (user: User) => {
			console.log("✅ Registration successful:", user);
			// Note: Backend should auto-login after registration
			// If not, you might need to call login here
		},
		onError: (error: any) => {
			console.error("❌ Registration failed:", error);
		},
	});

	// Seller registration mutation
	const sellerRegisterMutation = useMutation({
		mutationFn: authApi.registerSeller,
		onSuccess: (user: User) => {
			console.log("✅ Seller registration successful:", user);
		},
		onError: (error: any) => {
			console.error("❌ Seller registration failed:", error);
		},
	});

	// Upgrade to seller mutation
	const upgradeToSellerMutation = useMutation({
		mutationFn: authApi.upgradeToSeller,
		onSuccess: (updatedProfile: User) => {
			// Update user in store
			setUser(updatedProfile);

			// Update profile query cache
			queryClient.setQueryData(["auth", "profile"], updatedProfile);

			console.log("✅ Seller upgrade successful");
		},
		onError: (error: any) => {
			console.error("❌ Seller upgrade failed:", error);
		},
	});

	// Logout mutation
	const logoutMutation = useMutation({
		mutationFn: async () => {
			// Clear tokens and state
			tokenUtils.clearTokens();
			updateAuthState(false);
		},
		onMutate: () => {
			setLoggingOut(true);
		},
		onSuccess: () => {
			// Update auth state
			setLogoutState();

			// Clear all queries
			queryClient.clear();

			console.log("✅ Logout successful");

			// Redirect to home page
			if (typeof window !== "undefined") {
				window.location.href = "/";
			}
		},
		onError: (error) => {
			console.error("❌ Logout error:", error);
		},
		onSettled: () => {
			setLoggingOut(false);
		},
	});

	// Initialize auth state from stored tokens
	const initializeAuth = useCallback(async () => {
		// Prevent multiple simultaneous initialization attempts
		if (initializingRef.current) {
			return;
		}

		const hasTokens = tokenUtils.hasTokens();

		if (hasTokens && !isAuthenticated && !isLoadingProfile) {
			try {
				initializingRef.current = true;
				console.log("🔄 Initializing auth from stored tokens...");

				// Try to initialize auth
				const isValid = await tokenUtils.initializeAuth();

				if (isValid) {
					updateAuthState(true);
					// Profile will be fetched automatically by the query when enabled condition changes
					console.log("✅ Auth initialized successfully");
				} else {
					console.log("❌ Stored tokens invalid");
					setLogoutState();
				}
			} catch (error) {
				console.error("❌ Auth initialization failed:", error);
				setLogoutState();
			} finally {
				initializingRef.current = false;
			}
		} else if (!hasTokens && isAuthenticated) {
			// Clear state if no tokens but store thinks we're authenticated
			setLogoutState();
		}
	}, [isAuthenticated, isLoadingProfile, setLogoutState]);

	// Update store when profile is fetched
	useEffect(() => {
		if (profile && tokenUtils.getAccessToken()) {
			setLoginState(profile, tokenUtils.getAccessToken()!);
		}
	}, [profile, setLoginState]);

	// Initialize auth on mount
	useEffect(() => {
		initializeAuth();
	}, [initializeAuth]);

	// Listen for token events from API client
	useEffect(() => {
		const handleTokensCleared = () => {
			if (isAuthenticated) {
				setLogoutState();
				queryClient.clear();
			}
		};

		const handleTokensUpdated = () => {
			if (!isAuthenticated && !isLoadingProfile) {
				// Profile will be fetched automatically by the query when enabled condition changes
				updateAuthState(true);
			}
		};

		if (typeof window !== "undefined") {
			window.addEventListener("auth:tokens-cleared", handleTokensCleared);
			window.addEventListener("auth:tokens-updated", handleTokensUpdated);

			return () => {
				window.removeEventListener(
					"auth:tokens-cleared",
					handleTokensCleared
				);
				window.removeEventListener(
					"auth:tokens-updated",
					handleTokensUpdated
				);
			};
		}
	}, [isAuthenticated, isLoadingProfile, setLogoutState, queryClient]);

	// Periodic token refresh (every 10 minutes when authenticated)
	useEffect(() => {
		if (isAuthenticated) {
			const interval = setInterval(async () => {
				try {
					await tokenUtils.refreshIfNeeded();
				} catch (error) {
					console.error("Background token refresh failed:", error);
				}
			}, 1000 * 60 * 10); // Every 10 minutes

			return () => clearInterval(interval);
		}
	}, [isAuthenticated]);

	return {
		// Auth state
		user,
		token,
		isAuthenticated,
		isLoadingProfile,
		profileError,

		// Loading states
		isLoggingIn,
		isLoggingOut,
		isRegistering: registerMutation.isPending,
		isRegisteringSeller: sellerRegisterMutation.isPending,
		isUpgradingToSeller: upgradeToSellerMutation.isPending,

		// Actions
		login: (credentials: LoginCredentials) =>
			loginMutation.mutate(credentials),
		register: (userData: RegisterCredentials) =>
			registerMutation.mutate(userData),
		registerSeller: (sellerData: SellerRegistrationData) =>
			sellerRegisterMutation.mutate(sellerData),
		upgradeToSeller: (sellerData: SellerOnboardingData) =>
			upgradeToSellerMutation.mutate(sellerData),
		logout: () => logoutMutation.mutate(),

		// Profile management
		refetchProfile,

		// Permission helpers
		hasRole: (role: string) => user?.role === role,
		hasScope: (scope: string) => user?.scopes?.includes(scope) ?? false,
		isAdmin: () => user?.role === "admin",
		isSeller: () => user?.role === "seller" || user?.role === "admin",
		isCustomer: () => user?.role === "customer",

		// Error states
		loginError: loginMutation.error,
		registerError: registerMutation.error,
		sellerRegisterError: sellerRegisterMutation.error,
		upgradeError: upgradeToSellerMutation.error,
		logoutError: logoutMutation.error,

		// Utility methods
		initializeAuth,
	};
};

// Specialized hooks for different auth flows
export const useLogin = () => {
	const { login, isLoggingIn, loginError } = useAuth();
	return { login, isLoading: isLoggingIn, error: loginError };
};

export const useRegister = () => {
	const { register, isRegistering, registerError } = useAuth();
	return { register, isLoading: isRegistering, error: registerError };
};

export const useSellerRegistration = () => {
	const { registerSeller, isRegisteringSeller, sellerRegisterError } =
		useAuth();
	return {
		registerSeller,
		isLoading: isRegisteringSeller,
		error: sellerRegisterError,
	};
};

export const useSellerUpgrade = () => {
	const { upgradeToSeller, isUpgradingToSeller, upgradeError } = useAuth();
	return {
		upgradeToSeller,
		isLoading: isUpgradingToSeller,
		error: upgradeError,
	};
};

export const useLogout = () => {
	const { logout, isLoggingOut, logoutError } = useAuth();
	return { logout, isLoading: isLoggingOut, error: logoutError };
};
