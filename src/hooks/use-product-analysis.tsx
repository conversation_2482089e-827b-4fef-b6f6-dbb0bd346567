"use client";

import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useReducer,
  useState,
} from "react";

import {
  getCurrentBuyBoxPrice,
  getCurrentProfit,
  getCurrentROI,
} from "../lib/index";
import { useParams } from "next/navigation";
import { useGetGatedStatus } from "./useGetGatedStatus";
import { useProfitCalculator } from "./useProfitCalculator";
import { useScoreCalculator } from "./useScoreCalculator";
import { ProductData } from "@/lib/types/home";
import { useGetKeepaData } from "./useGetKeepaData";


interface DataContextType {
  productData: ProductData | null;
  setProductData: React.Dispatch<React.SetStateAction<ProductData | null>>;
  selectedCountry: "GB" | "US";
  setSelectedCountry: React.Dispatch<React.SetStateAction<"GB" | "US">>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  dataLoadingProgress: number;
  setDataLoadingProgress: React.Dispatch<React.SetStateAction<number>>;
  pageUrl: string | null;
  setPageUrl: React.Dispatch<React.SetStateAction<string | null>>;
  isVatRegistered: boolean;
  setIsVatRegistered: React.Dispatch<React.SetStateAction<boolean>>;
  isGated: boolean | undefined;
  setIsGated: React.Dispatch<React.SetStateAction<boolean | undefined>>;
  vat: number;
  setVat: React.Dispatch<React.SetStateAction<number>>;
  inputBuyBoxPrice: string;
  setInputBuyBoxPrice: React.Dispatch<React.SetStateAction<string>>;
  inputPurchasePrice: string;
  setInputPurchasePrice: React.Dispatch<React.SetStateAction<string>>;
  fullResponse: boolean;
  setFullResponse: React.Dispatch<React.SetStateAction<boolean>>;
  profitLoading: boolean;
  hasNotProfitLoaded: boolean;
  asin: string | null;
  setAsin: React.Dispatch<React.SetStateAction<string | null>>;
  keepaLoading: boolean;
}

const ProductContext = createContext<DataContextType | undefined>(undefined);

export const ProductProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [productData, setProductData] = useState<ProductData | null>(null);
  const [selectedCountry, setSelectedCountry] = useState<"GB" | "US">("GB");
  const [isLoading, setIsLoading] = useState(true);
  const [dataLoadingProgress, setDataLoadingProgress] = useState(0);
  const [pageUrl, setPageUrl] = useState<string | null>(null);
  const [isVatRegistered, setIsVatRegistered] = useState(false);
  const [isGated, setIsGated] = useState<boolean | undefined>(undefined);
  const [vat, setVat] = useState<number>(20);
  const [inputBuyBoxPrice, setInputBuyBoxPrice] = useState<string>("0");
  const [inputPurchasePrice, setInputPurchasePrice] = useState("");
  const [fullResponse, setFullResponse] = useState(false);
  const [hasNotProfitLoaded, setProfitNotLoaded] = useState(true);
  const [fbaFee, setFbaFee] = useState(null);
  const [referralFee, setReferralFee] = useState(null);
  const [asin, setAsin] = useState<string | null>(null);


  const { data: gated } = useGetGatedStatus({
    GatedRequest: {
      asin_code: productData?.asin || "",
      countryCode: selectedCountry || "GB",
      fullResponse: true,
    },
  });

  const { data: fullData, isLoading: keepaLoading } = useGetKeepaData({
    KeepaRequest: {
      asin: asin || "",
      country: selectedCountry || "GB",
      vat: vat,
      sellerPrice:
        (productData &&
          getCurrentBuyBoxPrice({
            isVatRegistered: isVatRegistered,
            productData: productData,
          })) ||
        0,
      fullResponse: true 
    },
  });

  const { data: profitData, isLoading: profitLoading } = useProfitCalculator({
    ProfitCalcRequest: {
      fba_fees: fbaFee || 0,
      referral_percent: referralFee || 0,
      variable_closing_fee: fullResponse
        ? isVatRegistered
          ? productData?.pricing?.vat_pricing?.variable_closing_fee || 0
          : productData?.pricing?.non_vat_pricing?.variable_closing_fee || 0
        : productData?.pricing?.metrics?.variable_closing_fee_real || 0,
      buy_box_price: Number(inputBuyBoxPrice || 0),
      seller_price: Number(inputPurchasePrice || 0),
      sales_rank: fullData?.pricing?.sales_rank || 0,
      country: selectedCountry,
      vat: vat,
      asin_code: productData?.asin || "",
      fullResponse: fullData && fullResponse,
    },
  });

  useEffect(() => {
    if (profitData) {
      setProductData((prevState) => ({
        ...prevState,
        pricing: {
          ...prevState?.pricing,
          non_vat_pricing: {
            ...prevState?.pricing?.non_vat_pricing,
            variable_closing_fee:
              prevState?.pricing?.non_vat_pricing?.variable_closing_fee || 0,
            seller_price:
              prevState?.pricing?.non_vat_pricing?.seller_price || 0,
            sales_rank: prevState?.pricing?.non_vat_pricing?.sales_rank || 0,
            // update below
            referral_fee: profitData.metrics.referral_fee || 0,
            fba_fee: profitData.metrics?.fba_fees || 0,
            buy_box_price: profitData?.metrics?.buybox_price || 0,
            roi: profitData.not_vat_registed.fees.roi || 0,
            total_fee: profitData.not_vat_registed.fees.total_fees || 0,
            profit: profitData.not_vat_registed.fees.profit || 0,
          },
          vat_pricing: {
            ...prevState?.pricing?.vat_pricing,
            variable_closing_fee:
              prevState?.pricing?.vat_pricing?.variable_closing_fee || 0,
            seller_price: prevState?.pricing?.vat_pricing?.seller_price || 0,
            sales_rank: prevState?.pricing?.vat_pricing?.sales_rank || 0,
            // update below
            referral_fee: profitData.metrics.referral_fee || 0,
            fba_fee: profitData.metrics?.fba_fees || 0,
            buy_box_price: profitData?.metrics?.buybox_price || 0,
            roi: profitData.vat_registed.fees.roi || 0,
            total_fee: profitData.vat_registed.fees.total_fees || 0,
            profit: profitData.vat_registed.fees.profit || 0,
          },
        },
      }));
      setProfitNotLoaded(false);
    }
  }, [profitData]);

  useEffect(() => {
    if (gated && gated.restrictions && gated.restrictions.length > 0) {
      setIsGated(true);
    } else {
      setIsGated(false);
    }
  }, [gated]);

  useEffect(() => {
    if (fullData) {
      setProductData((prevState) => ({
        ...prevState,
        mainImage: fullData.summary.image_url || prevState?.mainImage || "",
        title: fullData.summary.title || prevState?.title || "",
        brand: fullData.summary.brand || prevState?.brand || "",
        category: fullData.summary.category || prevState?.category || "",
        rating: fullData.summary.rating?.toString() || prevState?.rating || "",
        reviewCount: fullData.summary.reviews?.toString() || prevState?.reviewCount || "",
        ean: fullData.summary && Array.isArray(fullData.summary.ean) ? fullData.summary.ean[0] : prevState?.ean || "",
        features: fullData.summary.features || prevState?.features || [],
        estimated_sales: fullData.summary.monthly_sold || prevState?.estimated_sales || 0,
        summary: {
          title: fullData.summary.title || prevState?.summary?.title || "",
          brand: fullData.summary.brand || prevState?.summary?.brand || "",
          category: fullData.summary.category || prevState?.summary?.category || "",
          rating: fullData.summary.rating?.toString() || prevState?.summary?.rating || "",
          reviews: fullData.summary.reviews?.toString() || prevState?.summary?.reviews || "",
          features: fullData.summary.features || prevState?.summary?.features || [],
          ean: fullData.summary.ean && Array.isArray(fullData.summary.ean) ? fullData.summary.ean[0] : prevState?.summary?.ean || "",
          google_url: fullData.summary.google_url || prevState?.summary?.google_url || "",
          hagglezon_url: fullData.summary.hagglezon_url || prevState?.summary?.hagglezon_url || "",
          euro_search_url: fullData.summary.euro_search_url || prevState?.summary?.euro_search_url || "",
          ebay_active_listing_url: fullData?.summary?.ebay_active_listing_url || "",
          ebay_sold_listing_url: fullData?.summary?.ebay_sold_listing_url || "",
          bsr: fullData?.summary?.bsr || "",
        },
        pricing: {
          sales_rank: fullData.pricing.sales_rank || 0,
          non_vat_pricing: {
            buy_box_price: fullData?.pricing?.buy_box_price.price || 0,
            fba_fee: fullData.pricing.live_analysis.metrics.fba_fees_real || 0,
            profit: fullData.pricing.live_analysis.not_vat_registed.fees.profit || 0,
            referral_fee: fullData.pricing.referral_fee.referral_fee_percentage || 0,
            roi: fullData.pricing.live_analysis.not_vat_registed.fees.roi || 0,
            sales_rank: fullData.pricing.sales_rank || 0,
            seller_price: fullData.pricing.live_analysis.metrics.seller_price_real || 0,
            total_fee: fullData.pricing.live_analysis.not_vat_registed.fees.total_fees || 0,
            variable_closing_fee: fullData.pricing.live_analysis.metrics.variable_closing_fee_real || 0,
          },
          vat_pricing: {
            buy_box_price: fullData?.pricing?.buy_box_price.price || 0,
            fba_fee: fullData.pricing.live_analysis.metrics.fba_fees || 0,
            profit: fullData.pricing.live_analysis.vat_registed.fees.profit || 0,
            referral_fee: fullData.pricing.referral_fee.referral_fee_percent || 0,
            roi: fullData.pricing.live_analysis.vat_registed.fees.roi || 0,
            sales_rank: fullData.pricing.sales_rank || 0,
            seller_price: fullData.pricing.live_analysis.metrics.seller_price || 0,
            total_fee: fullData.pricing.live_analysis.vat_registed.fees.total_fees || 0,
            variable_closing_fee: fullData.pricing.live_analysis.metrics.variable_closing_fee || 0,
          },
        },
        offers: {
          total: fullData.offers.offers || 0,
          fba: fullData.offers.fba || 0,
          fbm: fullData.offers.fbm || 0,
          sellers_offers: fullData.offers.sellers_offers || [],
        },
        warnings: {
          adult_product: fullData.warnings.adult_product || false,
          amz_in_buy_box: fullData.warnings.amz_in_buy_box || false,
          meltable: fullData.warnings.meltable || false,
          variations: fullData.warnings.variations || undefined,
        },
        dimensions: fullData.dimensions || undefined,
        graph_data: fullData.graph_data || undefined,
        rank_and_price_history: fullData.rank_and_price_history || undefined,
        buy_box_history: fullData.buy_box_history || undefined,
      }));

      if (!fbaFee && !referralFee && fullData.summary.bsr) {
        setFbaFee(fullData.pricing.fba_fees.price || 0);
        setReferralFee(fullData.pricing.referral_fee.referral_fee_percent || 0);
      }
    }
    setFullResponse(true);
  }, [fullData]);


  useEffect(() => {
    console.log('i ran')
    const price = productData?.pricing?.non_vat_pricing?.buy_box_price || productData?.pricing?.vat_pricing?.seller_price || "0";
    setInputBuyBoxPrice(price.toString());
    const randomMultiplier = 0.95 + Math.random() * 0.1;
    const purchasePrice = (parseFloat(price.toString()) * randomMultiplier).toFixed(2);
    setInputPurchasePrice(purchasePrice);
  }, [productData?.price, productData?.pricing?.non_vat_pricing?.buy_box_price, productData?.pricing?.vat_pricing?.seller_price, fullData]);


  return (
    <ProductContext.Provider
      value={{
        productData,
        setProductData,
        selectedCountry,
        setSelectedCountry,
        isLoading,
        setIsLoading,
        dataLoadingProgress,
        setDataLoadingProgress,
        pageUrl,
        setPageUrl,
        isVatRegistered,
        setIsVatRegistered,
        isGated,
        setIsGated,
        vat,
        setVat,
        inputBuyBoxPrice,
        setInputBuyBoxPrice,
        inputPurchasePrice,
        setInputPurchasePrice,
        fullResponse,
        setFullResponse,
        profitLoading,
        hasNotProfitLoaded,
        asin,
        setAsin,
        keepaLoading,
      }}
    >
      {children}
    </ProductContext.Provider>
  );
};

export const useProductContext = () => {
  const context = useContext(ProductContext);
  if (!context) {
    throw new Error("useProductContext must be used within a ProductProvider");
  }
  return context;
};
