// src/lib/constants.ts
import {
	BarChart3,
	MessageSquare,
	Bot,
	UserCheck,
	Settings,
	CreditCard,
	Briefcase,
	TrendingUp,
	User,
	Mail,
	Users,
	Globe,
	MapPin,
	Star,
	DollarSign,
	ShoppingCart,
	Package,
	Target,
} from "lucide-react";
import * as z from "zod";
import { SidebarSection } from "@/types/extension";

export const SIDEBAR_SECTIONS: SidebarSection[] = [
	{
		title: "Dashboard",
		items: [
			{
				icon: BarChart3,
				label: "Dashboard",
				active: true,
			},
		],
	},
	{
		title: "Support",
		items: [
			{
				icon: MessageSquare,
				label: "Conversations",
			},
			{
				icon: Bot,
				label: "AI Agents",
			},
			{
				icon: UserCheck,
				label: "Human Agents",
			},
		],
	},
	{
		title: "AI Tools",
		items: [
			{
				icon: Settings,
				label: "Create Agent",
			},
			{
				icon: Bot,
				label: "Aira",
				badge: "learning",
			},
		],
	},
	{
		title: "Analytics",
		items: [
			{
				icon: BarChart3,
				label: "Analytics",
			},
			{
				icon: TrendingUp,
				label: "SEO Analytics",
			},
		],
	},
	{
		title: "Account",
		items: [
			{
				icon: CreditCard,
				label: "Credits",
			},
			{
				icon: Briefcase,
				label: "Workspace",
			},
		],
	},
];

export const COMPANY_INFO = {
	name: "ClickBuy",
	poweredBy: "ClickBuy",
	copyright: "© 2025 ClickBuy Inc.",
	user: "AI",
};

export const DEFAULT_CHART_DATA = [3, 5, 2, 7, 4, 8, 6];

export const SITE_CONFIG = {
	name: "ClickBuy Deals",
	description: "AI Clickbuy Deal Finder Chrome Extension",
	url: "https://clickbuydeals.com",
	email: "<EMAIL>",
} as const;

export const COLORS = {
	primary: "#19D86C",
	secondary: "#12C2E9",
	background: "#111215",
	cardBackground: "#191b1f",
	border: "#22232a",
} as const;

export const NAVIGATION = {
	pricing: "#pricing-plan",
	download: "#download",
} as const;


export const ONBOARDING_FIELD_CONFIGS = new Map([
	// Step 1: Personal Info
	[
		"name",
		{
			label: "Full Name",
			type: "text",
			icon: User,
			placeholder: "Enter your full name",
			description: "Your full name",
			required: true,
			step: 1,
		},
	],
	[
		"email",
		{
			label: "Email Address",
			type: "email",
			icon: Mail,
			placeholder: "Enter your email address",
			description: "We'll use this to contact you",
			required: true,
			step: 1,
		},
	],
	[
		"ageRange",
		{
			label: "Age Range",
			type: "select",
			icon: Users,
			placeholder: "Select your age range",
			options: [
				{ value: "18-24", label: "18-24 years" },
				{ value: "25-34", label: "25-34 years" },
				{ value: "35-44", label: "35-44 years" },
				{ value: "45-54", label: "45-54 years" },
				{ value: "55-64", label: "55-64 years" },
				{ value: "65+", label: "65+ years" },
			],
			description: "Your age range",
			required: true,
			step: 1,
		},
	],
	[
		"country",
		{
			label: "Country",
			type: "select",
			icon: Globe,
			placeholder: "Select your country",
			options: [
				{ value: "us", label: "United States" },
				{ value: "ca", label: "Canada" },
				{ value: "uk", label: "United Kingdom" },
				{ value: "de", label: "Germany" },
				{ value: "fr", label: "France" },
				{ value: "jp", label: "Japan" },
				{ value: "au", label: "Australia" },
				{ value: "in", label: "India" },
				{ value: "br", label: "Brazil" },
				{ value: "ng", label: "Nigeria" },
				{ value: "other", label: "Other" },
			],
			description: "Your current country of residence",
			required: true,
			step: 1,
		},
	],
	[
		"city",
		{
			label: "City",
			type: "text",
			icon: MapPin,
			placeholder: "Enter your city",
			description: "Your current city",
			required: true,
			step: 1,
		},
	],
	// Step 2: Business Profile
	[
		"occupation",
		{
			label: "Occupation",
			type: "text",
			icon: Briefcase,
			placeholder: "e.g., Marketing Manager, Entrepreneur",
			description: "Your current job title or profession",
			required: true,
			step: 2,
		},
	],
	[
		"company",
		{
			label: "Company/Organization",
			type: "text",
			icon: Briefcase,
			placeholder: "Enter your company name",
			description: "Where you currently work (optional)",
			required: false,
			step: 2,
		},
	],
	[
		"experience",
		{
			label: "Experience Level",
			type: "select",
			icon: Star,
			placeholder: "Select your experience level",
			options: [
				{ value: "entry", label: "Entry Level (0-2 years)" },
				{ value: "mid", label: "Mid Level (3-5 years)" },
				{ value: "senior", label: "Senior Level (6-10 years)" },
				{ value: "expert", label: "Expert Level (10+ years)" },
			],
			description: "Your professional experience level",
			required: true,
			step: 2,
		},
	],
	[
		"salaryRange",
		{
			label: "Annual Income Range",
			type: "select",
			icon: DollarSign,
			placeholder: "Select your income range (optional)",
			options: [
				{ value: "under_30k", label: "Under $30,000" },
				{ value: "30k_50k", label: "$30,000 - $50,000" },
				{ value: "50k_75k", label: "$50,000 - $75,000" },
				{ value: "75k_100k", label: "$75,000 - $100,000" },
				{ value: "100k_150k", label: "$100,000 - $150,000" },
				{ value: "150k_plus", label: "$150,000+" },
			],
			description: "Optional - helps us understand your budget",
			required: false,
			step: 2,
		},
	],
	// Step 3: Ecommerce Focus
	[
		"ecommerceInterests",
		{
			label: "Ecommerce Categories",
			type: "checkbox",
			icon: ShoppingCart,
			options: [
				{ value: "electronics", label: "Electronics & Gadgets" },
				{ value: "fashion", label: "Fashion & Apparel" },
				{ value: "home_garden", label: "Home & Garden" },
				{ value: "health_beauty", label: "Health & Beauty" },
				{ value: "sports_outdoors", label: "Sports & Outdoors" },
				{ value: "toys_games", label: "Toys & Games" },
				{ value: "automotive", label: "Automotive" },
				{ value: "books_media", label: "Books & Media" },
				{ value: "food_beverage", label: "Food & Beverage" },
				{ value: "jewelry", label: "Jewelry & Accessories" },
			],
			description: "Select all categories you're interested in",
			required: true,
			step: 3,
		},
	],
	[
		"productTypes",
		{
			label: "Specific Product Types",
			type: "text",
			icon: Package,
			placeholder: "e.g., Wireless headphones, Organic skincare",
			description: "What specific products do you want to source?",
			required: false,
			step: 3,
		},
	],
	[
		"targetMarket",
		{
			label: "Target Market/Audience",
			type: "text",
			icon: Target,
			placeholder: "e.g., Young professionals, Parents",
			description: "Who is your target customer?",
			required: false,
			step: 3,
		},
	],
	// Step 4: Finalize
	[
		"skills",
		{
			label: "Skills & Expertise",
			type: "textarea",
			icon: Star,
			placeholder: "List your key skills (comma-separated)",
			description:
				"e.g., Digital Marketing, Product Research, Negotiation",
			required: false,
			step: 4,
		},
	],
	[
		"bio",
		{
			label: "Bio",
			type: "textarea",
			icon: User,
			placeholder: "Tell us about yourself and your goals...",
			description:
				"A brief description about yourself (max 500 characters)",
			required: false,
			step: 4,
		},
	],
]);

export const onboardingSchema = z.object({
	name: z.string().min(2, "Name must be at least 2 characters"),
	email: z.string().email("Invalid email address"),
	ageRange: z.string().min(1, "Age range is required"),
	country: z.string().min(1, "Country is required"),
	city: z.string().min(2, "City must be at least 2 characters"),
	occupation: z.string().min(2, "Occupation must be at least 2 characters"),
	company: z.string().optional(),
	experience: z.string().min(1, "Experience level is required"),
	salaryRange: z.string().optional(),
	ecommerceInterests: z
		.array(z.string())
		.min(1, "Please select at least one interest"),
	productTypes: z.string().optional(),
	targetMarket: z.string().optional(),
	skills: z.string().optional(),
	bio: z.string().max(500, "Bio must be less than 500 characters").optional(),
	newsletter: z.boolean(),
	terms: z.boolean().refine((val) => val === true, {
		message: "You must accept the terms and conditions",
	}),
});