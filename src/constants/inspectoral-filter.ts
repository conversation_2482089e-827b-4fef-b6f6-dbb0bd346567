import { Al<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Box, Crown, DollarSign, GitBranch, MapPin, MessageSquare, Package, Percent, ShoppingBag, ShoppingCart, Star, Store, Tag, Trophy, Truck, Users, Weight } from "lucide-react";

export interface InspectoraApiResponse {
	market_id: string;
	itemHeight_lte: number;
	itemWeight_lte: number;
	itemWeight_gte: number;
	itemHeight_gte: number;
	itemWidth_lte: number;
	itemWidth_gte: number;
	brand: string;
	current_SALES_lte: number;
	amz_in_buy_box: boolean;
	availabilityAmazon: boolean;
	current_AMAZON_lte: number;
	current_AMAZON_gte: number;
}

export interface FilterValues {
	marketplace_id: string;
	brand: string;
	current_SALES_gte: string;
	current_SALES_lte: string;
	isLowest_SALES: boolean | null;
	current_BUY_BOX_SHIPPING_gte: string;
	current_BUY_BOX_SHIPPING_lte: string;
	isOutOfStock: boolean | null;
	isEbayOutOfStock: boolean | null;
	current_EBAY_NEW_gte: string;
	current_EBAY_NEW_lte: string;
	current_COUNT_NEW_gte: string;
	current_COUNT_NEW_lte: string;
	buyBoxSeller: string;
	buyBoxStatsAmazon30_gte: string;
	buyBoxStatsAmazon30_lte: string;
	buyBoxStatsAmazon90_gte: string;
	buyBoxStatsAmazon90_lte: string;
	buyBoxStatsTopSeller30_gte: string;
	buyBoxStatsTopSeller30_lte: string;
	buyBoxStatsTopSeller90_gte: string;
	buyBoxStatsTopSeller90_lte: string;
	buyBoxStatsSellerCount30_gte: string;
	buyBoxStatsSellerCount30_lte: string;
	buyBoxStatsSellerCount90_gte: string;
	buyBoxStatsSellerCount90_lte: string;
	buyBoxStandardDeviation30_gte: string;
	buyBoxStandardDeviation30_lte: string;
	buyBoxStandardDeviation90_gte: string;
	buyBoxStandardDeviation90_lte: string;
	buyBoxStandardDeviation365_gte: string;
	buyBoxStandardDeviation365_lte: string;
	hasParentASIN: boolean | null;
	monthlySold_gte: string;
	monthlySold_lte: string;
	current_RATING_gte: string;
	current_RATING_lte: string;
	current_COUNT_REVIEWS_gte: string;
	current_COUNT_REVIEWS_lte: string;
	availabilityAmazon: number[];
	packageLength_gte: string;
	packageLength_lte: string;
	packageWidth_gte: string;
	packageWidth_lte: string;
	packageHeight_gte: string;
	packageHeight_lte: string;
	packageWeight_gte: string;
	packageWeight_lte: string;
	isHazMat: boolean | null;
	isHeatSensitive: boolean | null;
}

export interface SearchRequest {
	brand?: string;
	title?: string;
	current_SALES_lte?: number;
	current_SALES_gte?: number;
	isLowest_SALES?: boolean;
	current_BUY_BOX_SHIPPING_gte?: number;
	current_BUY_BOX_SHIPPING_lte?: number;
	isOutOfStock?: boolean;
	isEbayOutOfStock?: boolean;
	current_EBAY_NEW_gte?: number;
	current_EBAY_NEW_lte?: number;
	current_COUNT_NEW_gte?: number;
	current_COUNT_NEW_lte?: number;
	buyBoxSeller?: string[];
	buyBoxStatsAmazon30_gte?: number;
	buyBoxStatsAmazon30_lte?: number;
	buyBoxStatsAmazon90_gte?: number;
	buyBoxStatsAmazon90_lte?: number;
	buyBoxStatsTopSeller30_gte?: number;
	buyBoxStatsTopSeller30_lte?: number;
	buyBoxStatsTopSeller90_gte?: number;
	buyBoxStatsTopSeller90_lte?: number;
	buyBoxStatsSellerCount30_gte?: number;
	buyBoxStatsSellerCount30_lte?: number;
	buyBoxStatsSellerCount90_gte?: number;
	buyBoxStatsSellerCount90_lte?: number;
	buyBoxStandardDeviation30_gte?: number;
	buyBoxStandardDeviation30_lte?: number;
	buyBoxStandardDeviation90_gte?: number;
	buyBoxStandardDeviation90_lte?: number;
	buyBoxStandardDeviation365_gte?: number;
	buyBoxStandardDeviation365_lte?: number;
	hasParentASIN?: boolean;
	monthlySold_gte?: number;
	monthlySold_lte?: number;
	current_RATING_gte?: number;
	current_RATING_lte?: number;
	current_COUNT_REVIEWS_gte?: number;
	current_COUNT_REVIEWS_lte?: number;
	availabilityAmazon?: number[];
	packageLength_gte?: number;
	packageLength_lte?: number;
	packageWidth_gte?: number;
	packageWidth_lte?: number;
	packageHeight_gte?: number;
	packageHeight_lte?: number;
	packageWeight_gte?: number;
	packageWeight_lte?: number;
	isHazMat?: boolean;
	isHeatSensitive?: boolean;
	sort?: string[];
	perPage?: number;
	page?: number;
	domain: string;
}

export interface SearchResponse {
	success: boolean;
	data: string[]; // Just array of ASINs like ["B123", "B456", ...]
	error?: string;
	status_code: number;
}

export interface CatalogRequest {
	asins: string[];
}

export interface CatalogResponse {
	success: boolean;
	data: {
		total_requested: number;
		successful_count: number;
		failed_count: number;
		successful_results: Array<{
			asin: string;
			success: boolean;
			data: ProductData;
			status_code: number;
		}>;
		// eslint-disable-next-line @typescript-eslint/no-explicit-any
		failed_results: Array<any>;
	};
	error?: string | null;
	status_code: number;
}

export interface ProductData {
	manufacturer: string;
	amazon_url: string;
	asin: string;
	product_name: string;
	category: string;
	bsr: number;
	brand: string;
	weight?: number | null;
	weight_unit?: string | null;
	list_price?: number | null;
	currency?: string | null;
	adult_product: boolean;
}


export const INSPECTORA_TABS = [
    {
        id: "filter",
        label: "Filter",
        icon: null,
    },
    {
        id: "result",
        label: "Result",
        icon: null,
    },
] as const;

export type TabId = (typeof INSPECTORA_TABS)[number]["id"];

export const FILTER_CONFIGS = new Map([
	[
		"marketplace_id",
		{
			label: "Marketplace ID",
			type: "select",
			icon: MapPin,
			options: [
				// { value: "us", label: "United States (US)" },
				// { value: "de", label: "Germany (DE)" },
				// { value: "jp", label: "Japan (JP)" },
				{ value: "uk", label: "United Kingdom (UK)" },
				// { value: "fr", label: "France (FR)" },
				// { value: "ca", label: "Canada (CA)" },
				// { value: "it", label: "Italy (IT)" },
				// { value: "es", label: "Spain (ES)" },
			],
		},
	],
	[
		"sales_rank_section",
		{
			label: "Sales Rank & Price Types",
			type: "sales_rank_component",
			icon: Star,
		},
	],
	[
		"buy_box_section",
		{ label: "Buy Box Price", type: "buy_box_component", icon: Truck },
	],
	[
		"ebay_new_section",
		{
			label: "eBay New Price",
			type: "ebay_new_component",
			icon: Truck,
		},
	],
	[
		"new_offer_count_section",
		{
			label: "New Offer Count",
			type: "new_offer_count_component",
			icon: Users,
		},
	],
	[
		"seller_ids_section",
		{
			label: "Seller IDs",
			type: "seller_ids_component",
			icon: Users,
		},
	],
	[
		"buy_box_amazon_section",
		{
			label: "Buy Box Amazon %",
			type: "buy_box_amazon_component",
			icon: Percent,
		},
	],
	[
		"buy_box_top_seller_section",
		{
			label: "Buy Box Top Seller %",
			type: "buy_box_top_seller_component",
			icon: Crown,
		},
	],
	[
		"buy_box_winner_count_section",
		{
			label: "Buy Box Winner Count",
			type: "buy_box_winner_count_component",
			icon: Trophy,
		},
	],
	[
		"buy_box_standard_deviation_section",
		{
			label: "Buy Box Standard Deviation",
			type: "buy_box_standard_deviation_component",
			icon: BarChart,
		},
	],
	[
		"variations_section",
		{
			label: "Variations",
			type: "variations_component",
			icon: GitBranch,
		},
	],
	[
		"bought_in_past_month_section",
		{
			label: "Bought in past month",
			type: "bought_in_past_month_component",
			icon: ShoppingBag,
		},
	],
	[
		"rating_section",
		{
			label: "Rating",
			type: "rating_component",
			icon: Star,
		},
	],
	[
		"rating_count_section",
		{
			label: "Rating Count",
			type: "rating_count_component",
			icon: MessageSquare,
		},
	],
	[
		"availability_amazon_section",
		{
			label: "Availability Amazon",
			type: "availability_amazon_component",
			icon: Package,
		},
	],
	[
		"package_dimension_section",
		{
			label: "Package Dimension",
			type: "package_dimension_component",
			icon: Box,
		},
	],
	[
		"package_weight_section",
		{
			label: "Package Weight",
			type: "package_weight_component",
			icon: Weight,
		},
	],
	[
		"hazmat_section",
		{
			label: "HazMat Classification",
			type: "hazmat_component",
			icon: AlertTriangle,
		},
	],

	[
		"item_height_between",
		{
			label: "Item Height Range",
			type: "range",
			icon: Package,
			unit: "cm",
			placeholder: { min: "Min height", max: "Max height" },
		},
	],
	[
		"item_width_between",
		{
			label: "Item Width Range",
			type: "range",
			icon: Package,
			unit: "cm",
			placeholder: { min: "Min width", max: "Max width" },
		},
	],
	[
		"item_weight_between",
		{
			label: "Item Weight Range",
			type: "range",
			icon: Weight,
			unit: "kg",
			placeholder: { min: "Min weight", max: "Max weight" },
		},
	],
	[
		"brand",
		{
			label: "Brand",
			type: "text",
			icon: Tag,
			placeholder: "e.g., Nike, Apple, Samsung",
		},
	],
	[
		"amazon_in_buybox",
		{
			label: "Amazon in Buy Box",
			type: "boolean",
			icon: ShoppingCart,
		},
	],
	[
		"amazon_in_listing",
		{
			label: "Amazon in Listing",
			type: "boolean",
			icon: Store,
		},
	],
	[
		"buy_price_between",
		{
			label: "Buy Price Range",
			type: "range",
			icon: DollarSign,
			unit: "$",
			placeholder: { min: "Min price", max: "Max price" },
		},
	],
]);

// Types
export interface InspectoraPageProps {
    isDarkMode: boolean;
    section?: string;
}
