import { ReactNode } from "react";

export interface MetricCardProps {
	title: string;
	value: string | number;
	subtitle?: string;
	trend?: "up" | "down" | "neutral";
	trendValue?: string;
	icon: ReactNode;
	color?: "green" | "red" | "blue" | "purple" | "orange";
}

export interface SidebarItemProps {
	icon?: React.ComponentType<{ size?: number }>;
	label: string;
	active?: boolean;
	badge?: string;
	onClick?: () => void;
}

export interface SidebarSection {
	title: string;
	items: SidebarItemProps[];
}

export interface AnalyticsCardProps {
	title: string;
	value: string | number;
	subtitle?: string;
	icon: ReactNode;
	color?: "green" | "red" | "blue" | "purple" | "orange";
	rightValue?: string;
}

export interface MiniChartProps {
	trend: "up" | "down";
	data?: number[];
}

export interface DeviceStatsProps {
	desktop: number;
	mobile: number;
	percentage: string;
}

export interface PerformanceMetrics {
	errorRate?: string;
	totalMessages?: string;
	sessions: number;
}

export interface DashboardMetrics {
	openTickets: number;
	newChats: number;
	avgResponseTime: string;
	escalationRate: number;
	resolutionTime: string;
	engagementRate: number;
	bounceRate: number;
	conversions: number;
	avgSession: string;
}

export type ColorVariant = "green" | "red" | "blue" | "purple" | "orange";

export interface ButtonProps {
	children: React.ReactNode;
	variant?: "primary" | "secondary" | "ghost" | "outline";
	size?: "sm" | "md" | "lg";
	onClick?: () => void;
	disabled?: boolean;
	className?: string;
	type?: "button" | "submit" | "reset";
	title?: string;
}

export interface ActionButtonProps extends ButtonProps {
	icon?: React.ReactNode;
	loading?: boolean;
	fullWidth?: boolean;
}

export interface IconButtonProps extends Omit<ButtonProps, "children"> {
	icon: React.ReactNode;
	"aria-label": string;
	tooltip?: string;
}

export interface FormButtonProps extends ButtonProps {
	loading?: boolean;
	loadingText?: string;
	successText?: string;
	errorText?: string;
	state?: "idle" | "loading" | "success" | "error";
}

export interface NavButtonProps extends ButtonProps {
	href?: string;
	external?: boolean;
	active?: boolean;
}

export interface InputProps {
	placeholder?: string;
	value?: string;
	onChange?: (value: string) => void;
	icon?: ReactNode;
	className?: string;
}

export interface ExtensionProfile {
	id: string;
	name: string;
	version: string;
	status: "Active" | "Inactive" | "Paused";
	type:
		| "Product Analysis"
		| "Price Tracker"
		| "Competitor Monitor"
		| "AI Assistant";
	description: string;
	permissions: string[];
	installedDate: string;
	lastUsed: string;
	usageStats: {
		totalScans: number;
		monthlyScans: number;
		successRate: number;
	};
}

export interface TokenUsage {
	current: number;
	limit: number;
	resetDate: string;
	usageHistory: {
		date: string;
		used: number;
		feature: string;
	}[];
	plan: "Free" | "Basic" | "Pro" | "Enterprise";
	costPerToken: number;
}

export interface ProductAnalysis {
	id: string;
	asin: string;
	title: string;
	brand: string;
	category: string;
	image: string;
	currentPrice: number;
	bsr: number;
	rating: number;
	reviewCount: number;
	profitability: {
		buyPrice: number;
		sellPrice: number;
		profit: number;
		roi: number;
		margin: number;
	};
	competitorData: {
		totalSellers: number;
		fbaSellers: number;
		buyBoxWinner: string;
		priceRange: {
			min: number;
			max: number;
			avg: number;
		};
	};
	trends: {
		priceHistory: {
			date: string;
			price: number;
			seller: string;
		}[];
		salesRank: {
			date: string;
			rank: number;
		}[];
		buyBoxHistory: {
			date: string;
			seller: string;
			price: number;
			percentage: number;
		}[];
	};
	aiInsights: {
		profitabilityScore: number;
		riskLevel: "Low" | "Medium" | "High";
		recommendation: "Buy" | "Avoid" | "Research More";
		reasons: string[];
		marketTrend: "Increasing" | "Stable" | "Decreasing";
		seasonality: string;
		competitionLevel: "Low" | "Medium" | "High";
	};
	restrictions: {
		hazmat: boolean;
		oversized: boolean;
		gated: boolean;
		ipClaims: boolean;
		variations: boolean;
		meltable: boolean;
	};
	lastAnalyzed: string;
	analysisTime: number; // in milliseconds
}

export interface AIWeights {
	profitMargin: number;
	salesVelocity: number;
	competitionLevel: number;
	priceStability: number;
	seasonality: number;
	bsrTrend: number;
	reviewScore: number;
	brandRecognition: number;
	returnRate: number;
	fbaFees: number;
}

export interface ExtensionSettings {
	aiWeights: AIWeights;
	scanning: {
		autoScan: boolean;
		scanInterval: number; // in minutes
		batchSize: number;
		alertThresholds: {
			minProfit: number;
			minROI: number;
			maxBSR: number;
			minRating: number;
		};
	};
	notifications: {
		priceAlerts: boolean;
		profitAlerts: boolean;
		competitorAlerts: boolean;
		lowTokenWarning: boolean;
		emailNotifications: boolean;
	};
	display: {
		defaultCurrency: "USD" | "GBP" | "EUR";
		showProfitability: boolean;
		showAIInsights: boolean;
		compactMode: boolean;
		colorCoding: boolean;
	};
	apiKeys: {
		keepa: string;
		mws: string;
		sellerAmp: string;
		other: { [key: string]: string };
	};
}

export interface ExtensionAnalytics {
	totalProducts: number;
	profitableProducts: number;
	averageROI: number;
	totalProfit: number;
	scanningStats: {
		totalScans: number;
		monthlyScans: number;
		successRate: number;
		averageTime: number;
	};
	aiUsage: {
		totalAnalyses: number;
		monthlyAnalyses: number;
		accuracyRate: number;
		topRecommendations: {
			type: string;
			count: number;
		}[];
	};
	topCategories: {
		category: string;
		products: number;
		avgProfit: number;
	}[];
	performanceMetrics: {
		date: string;
		scans: number;
		profits: number;
		accuracy: number;
	}[];
}

export interface Notification {
	id: string;
	type:
		| "Price Alert"
		| "Profit Alert"
		| "Token Warning"
		| "System Update"
		| "AI Insight";
	title: string;
	message: string;
	productId?: string;
	priority: "Low" | "Medium" | "High" | "Critical";
	isRead: boolean;
	createdDate: string;
	actionUrl?: string;
}

export interface ExtensionsDashboardData {
	profile: ExtensionProfile;
	tokenUsage: TokenUsage;
	recentAnalyses: ProductAnalysis[];
	analytics: ExtensionAnalytics;
	settings: ExtensionSettings;
	notifications: Notification[];
	savedProducts: ProductAnalysis[];
	watchlist: {
		id: string;
		name: string;
		products: string[]; // ASINs
		alerts: boolean;
		createdDate: string;
	}[];
}
