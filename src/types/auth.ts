export interface LoginCredentials {
	username: string; // email
	password: string;
}

export interface RegisterCredentials {
	email: string;
	password: string;
}

export interface SellerRegistrationData {
	email: string;
	password: string;
	business_name: string;
	business_address: string;
	business_phone: string;
	tax_id: string;
	business_type: string;
}

export interface SellerOnboardingData {
	business_name: string;
	business_address: string;
	business_phone: string;
	tax_id: string;
	business_type: string;
}

export interface TokenResponse {
	access_token: string;
	token_type: string;
	refresh_token?: string;
	amz_verify: boolean;
}

export interface User {
	id: string;
	username: string;
	email: string;
	disabled: boolean;
	is_verified: boolean;
	amz_verify: boolean;
	scopes: string[];
	role: "customer" | "seller" | "admin";
	is_free_user: boolean;
	business_name?: string;
	business_address?: string;
	business_phone?: string;
	business_type?: string;
	created_at: string;
	updated_at: string;
}

export interface RefreshTokenResponse {
	access_token: string;
	token_type: string;
	refresh_token?: string;
}

export interface TokenValidationResponse {
	message: string;
	success: boolean;
	token: string;
}

export interface AuthHealthResponse {
	status: string;
	service: string;
}

export interface AuthState {
	user: User | null;
	token: string | null;
	isAuthenticated: boolean;
}

export interface ApiError {
	detail: string;
	status?: number;
}
