export interface NavigationItem {
	name: string;
	href: string;
}

export interface FeatureCard {
	link?: string;
	icon: React.ReactNode;
	title: string;
	description: string;
	buttonText?: string;
	subText?: string;
}

export interface BenefitCard {
	icon: React.ReactNode;
	title: string;
	description: string;
}

export interface SocialLink {
	name: string;
	href: string;
	icon: React.ReactNode;
	text?: string;
}

export interface FooterSection {
	title: string;
	links: Array<{
		name: string;
		href: string;
		component?: string
	}>;
}
