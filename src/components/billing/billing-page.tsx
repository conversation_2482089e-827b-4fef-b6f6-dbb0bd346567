"use client";

import React, { useState, useEffect } from "react";
import { CreditCard, History, DollarSign } from "lucide-react";
import AddPaymentMethodModal from "./add-payment-method-modal";
import PaymentMethodsTab from "./payment-methods";
import PricingTab from "./pricing-tab";
import BillingHistoryTab from "./billing-history-tab";
import { useTokenBalance } from "@/hooks/use-token-data";
import { StripeProviderWrapper } from "../provider";

// Navigation configuration
const BILLING_TABS = [
	{
		id: "overview",
		label: "Overview",
		icon: null,
	},
	{
		id: "payment-methods",
		label: "Payment Methods",
		icon: null,
	},
	{
		id: "billing-history",
		label: "Billing History",
		icon: null,
	},
	{
		id: "pricing",
		label: "Pricing",
		icon: null,
	},
] as const;

type TabId = (typeof BILLING_TABS)[number]["id"];

const BillingPage: React.FC<{ isDarkMode: boolean; section?: string }> = ({
	isDarkMode,
	section,
}) => {
	const [activeTab, setActiveTab] = useState<TabId>(
		(section as TabId) || "overview"
	);
	const [isModalOpen, setIsModalOpen] = useState(false);
	const { data: tokenBalance, isLoading: isTokenLoading } = useTokenBalance();

	useEffect(() => {
		if (section && BILLING_TABS.some((tab) => tab.id === section)) {
			setActiveTab(section as TabId);
		}
	}, [section]);

	const handleGridItemClick = (tab: TabId) => {
		setActiveTab(tab);
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat("en-GB", {
			style: "currency",
			currency: "GBP",
			minimumFractionDigits: 3,
		}).format(amount);
	};

	// Calculate estimated value of remaining tokens (example: $0.002 per token)
	const getTokenValue = () => {
		if (!tokenBalance || isTokenLoading) return 0;
		return tokenBalance.current_tokens * 0.002; // $0.002 per token
	};

	const renderTabContent = () => {
		switch (activeTab) {
			case "overview":
				return (
					<>
						<div className="mb-8">
							<h2 className="text-lg font-semibold mb-2">
								{tokenBalance?.plan_type
									? `${
											tokenBalance.plan_type
												.charAt(0)
												.toUpperCase() +
											tokenBalance.plan_type.slice(1)
									  } Plan`
									: "Token Balance"}
							</h2>
							<p className="text-gray-400 text-sm mb-1">
								{isTokenLoading
									? "Loading..."
									: "Credit Remaining"}
							</p>
							<p
								className={`text-3xl font-bold mb-2 ${
									isDarkMode ? "text-white" : "text-black"
								}`}>
								{isTokenLoading
									? "..."
									: formatCurrency(getTokenValue())}
							</p>
							{tokenBalance && (
								<div className="space-y-1">
									<p
										className={`text-sm ${
											isDarkMode
												? "text-gray-400"
												: "text-gray-600"
										}`}>
										{tokenBalance.current_tokens.toLocaleString()}{" "}
										tokens remaining
									</p>
									{tokenBalance.expires_at &&
										!tokenBalance.is_expired && (
											<p
												className={`text-xs ${
													isDarkMode
														? "text-gray-500"
														: "text-gray-500"
												}`}>
												Expires in{" "}
												{tokenBalance.days_remaining}{" "}
												days (
												{new Date(
													tokenBalance.expires_at
												).toLocaleDateString()}
												)
											</p>
										)}
									{tokenBalance.is_expired && (
										<p className="text-xs text-red-500">
											Tokens have expired
										</p>
									)}
								</div>
							)}
						</div>

						<div className="mb-6">
							<button
								onClick={() => setIsModalOpen(true)}
								className="bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] font-bold rounded-md text-lg px-8 py-3 hover:from-[#12C2E9] hover:to-[#19D86C] transition-all cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
								disabled={isTokenLoading}>
								Add Payment Details
							</button>
						</div>

						{/* Low tokens warning */}
						{tokenBalance &&
							tokenBalance.current_tokens < 100 &&
							!tokenBalance.is_expired && (
								<div className="mb-6 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
									<div className="flex items-start gap-3">
										<div className="text-yellow-500 text-sm">
											⚠️
										</div>
										<div>
											<h4 className="text-yellow-500 font-medium mb-1">
												Low Token Balance
											</h4>
											<p
												className={`text-sm ${
													isDarkMode
														? "text-gray-300"
														: "text-gray-700"
												}`}>
												You have{" "}
												{tokenBalance.current_tokens}{" "}
												tokens remaining. Consider
												purchasing more tokens to avoid
												service interruption.
											</p>
										</div>
									</div>
								</div>
							)}

						{/* Expired tokens warning */}
						{tokenBalance?.is_expired && (
							<div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
								<div className="flex items-start gap-3">
									<div className="text-red-500 text-sm">
										🚨
									</div>
									<div>
										<h4 className="text-red-500 font-medium mb-1">
											Tokens Expired
										</h4>
										<p
											className={`text-sm ${
												isDarkMode
													? "text-gray-300"
													: "text-gray-700"
											}`}>
											Your tokens have expired. Purchase a
											new plan to continue using our
											services.
										</p>
									</div>
								</div>
							</div>
						)}

						<div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-8 text-sm">
							<div
								className={`p-4 rounded-lg ${
									isDarkMode ? "bg-gray-800" : "bg-white"
								} border ${
									isDarkMode
										? "border-gray-700"
										: "border-gray-200"
								} cursor-pointer hover:bg-opacity-80 transition-all`}
								onClick={() =>
									handleGridItemClick("payment-methods")
								}>
								<div className="flex items-center gap-3">
									<CreditCard
										size={20}
										className="text-[#19D86C]"
									/>
									<div>
										<p
											className={`font-semibold ${
												isDarkMode
													? "text-white"
													: "text-gray-900"
											}`}>
											Payment Methods
										</p>
										<p
											className={`text-sm ${
												isDarkMode
													? "text-gray-400"
													: "text-gray-600"
											}`}>
											Add or change payment method
										</p>
									</div>
								</div>
							</div>
							<div
								className={`p-4 rounded-lg ${
									isDarkMode ? "bg-gray-800" : "bg-white"
								} border ${
									isDarkMode
										? "border-gray-700"
										: "border-gray-200"
								} cursor-pointer hover:bg-opacity-80 transition-all`}
								onClick={() =>
									handleGridItemClick("billing-history")
								}>
								<div className="flex items-center gap-3">
									<History
										size={20}
										className="text-[#19D86C]"
									/>
									<div>
										<p
											className={`font-semibold ${
												isDarkMode
													? "text-white"
													: "text-gray-900"
											}`}>
											Billing History
										</p>
										<p
											className={`text-sm ${
												isDarkMode
													? "text-gray-400"
													: "text-gray-600"
											}`}>
											View past and current invoices
										</p>
									</div>
								</div>
							</div>
							<div
								className={`p-4 rounded-lg ${
									isDarkMode ? "bg-gray-800" : "bg-white"
								} border ${
									isDarkMode
										? "border-gray-700"
										: "border-gray-200"
								} cursor-pointer hover:bg-opacity-80 transition-all`}
								onClick={() => handleGridItemClick("pricing")}>
								<div className="flex items-center gap-3">
									<DollarSign
										size={20}
										className="text-[#19D86C]"
									/>
									<div>
										<p
											className={`font-semibold ${
												isDarkMode
													? "text-white"
													: "text-gray-900"
											}`}>
											Pricing
										</p>
										<p
											className={`text-sm ${
												isDarkMode
													? "text-gray-400"
													: "text-gray-600"
											}`}>
											View pricing and purchase tokens
										</p>
									</div>
								</div>
							</div>
						</div>
					</>
				);
			case "payment-methods":
				return <PaymentMethodsTab isDarkMode={isDarkMode} />;
			case "billing-history":
				return <BillingHistoryTab isDarkMode={isDarkMode} />;
			case "pricing":
				return <PricingTab isDarkMode={isDarkMode} />;
			default:
				return null;
		}
	};

	return (
		<StripeProviderWrapper>
			<div
				className={`${
					isDarkMode
						? "bg-gradient-to-br from-slate-900 to-slate-800 text-white"
						: "bg-gradient-to-br from-gray-50 to-gray-100 text-black"
				} min-h-screen p-10`}>
				<div
					className={`absolute inset-0 pointer-events-none ${
						isDarkMode
							? "bg-[radial-gradient(circle_at_25%_25%,rgba(34,197,94,0.1)_0%,transparent_50%),radial-gradient(circle_at_75%_75%,rgba(34,197,94,0.05)_0%,transparent_50%)]"
							: "bg-[radial-gradient(circle_at_25%_25%,rgba(34,197,94,0.05)_0%,transparent_50%),radial-gradient(circle_at_75%_75%,rgba(34,197,94,0.03)_0%,transparent_50%)]"
					}`}
				/>
				<header className={`mb-6 ${isDarkMode ? "" : ""}`}>
					<h1 className="text-2xl font-semibold">Billing</h1>
					<nav>
						<ul className="flex space-x-4 mt-2">
							{BILLING_TABS.map((tab) => (
								<li key={tab.id}>
									<button
										onClick={() => {
											setActiveTab(tab.id);
											// Update URL
											if (typeof window !== "undefined") {
												const url = new URL(
													window.location.href
												);
												url.searchParams.set(
													"section",
													tab.id
												);
												window.history.pushState(
													{},
													"",
													url.toString()
												);
											}
										}}
										className={`pb-2 cursor-pointer transition-all ${
											activeTab === tab.id
												? "border-b-2 border-[#19D86C]"
												: `border-none ${
														isDarkMode
															? "text-gray-400 hover:text-gray-300"
															: "text-gray-600 hover:text-gray-500"
												  }`
										} ${
											isDarkMode
												? "text-white"
												: "text-gray-600"
										}`}>
										{tab.label}
									</button>
								</li>
							))}
						</ul>
					</nav>
				</header>

				<div className="px-4 sm:px-20">{renderTabContent()}</div>

				<AddPaymentMethodModal
					isOpen={isModalOpen}
					onClose={() => setIsModalOpen(false)}
					isDarkMode={isDarkMode}
				/>
			</div>
		</StripeProviderWrapper>
	);
};

export default BillingPage;
