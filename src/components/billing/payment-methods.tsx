"use client";

import React, { useState } from "react";
import {
  usePaymentMethods,
  useDeletePaymentMethod,
  useSetDefaultPaymentMethod,
} from "@/hooks/use-payment-methods";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Trash2, Star, StarOff, CreditCard, AlertCircle, RefreshCw, Plus } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import AddPaymentMethodModal from "./add-payment-method-modal";

interface PaymentMethodsTabProps {
  isDarkMode: boolean;
}

const PaymentMethodsTab: React.FC<PaymentMethodsTabProps> = ({ isDarkMode }) => {
  const { data: methods, isLoading, error, refetch } = usePaymentMethods();
  const setDefault = useSetDefaultPaymentMethod();
  const remove = useDeletePaymentMethod();

  const [selectedMethodId, setSelectedMethodId] = useState<string | null>(null);
  const [actionType, setActionType] = useState<"delete" | "default" | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [addPaymentModalOpen, setAddPaymentModalOpen] = useState(false);

  const handleConfirm = () => {
    if (!selectedMethodId || !actionType) return;

    if (actionType === "delete") {
      remove.mutate(selectedMethodId);
    } else if (actionType === "default") {
      setDefault.mutate(selectedMethodId);
    }

    setDialogOpen(false);
    setSelectedMethodId(null);
    setActionType(null);
  };

  const getCardBrandColor = (brand: string) => {
    const brandColors = {
      visa: "text-blue-600",
      mastercard: "text-red-600",
      amex: "text-green-600",
      discover: "text-orange-600",
      default: "text-gray-600"
    };
    return brandColors[brand.toLowerCase() as keyof typeof brandColors] || brandColors.default;
  };

  const isPaymentMethodExpired = (method: any) => {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    
    return method.exp_year < currentYear || 
           (method.exp_year === currentYear && method.exp_month < currentMonth);
  };

  const isPaymentMethodExpiringSoon = (method: any) => {
    const now = new Date();
    const warningDate = new Date(now.getFullYear(), now.getMonth() + 3, 1); // 3 months ahead
    const expiryDate = new Date(method.exp_year, method.exp_month - 1, 1);
    
    return expiryDate <= warningDate && !isPaymentMethodExpired(method);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="flex items-center gap-3">
          <RefreshCw className="animate-spin h-6 w-6 text-[#19D86C]" />
          <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>
            Loading payment methods...
          </span>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className={`text-lg font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
          Failed to Load Payment Methods
        </h3>
        <p className={`text-sm mb-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          {error.message || 'Unable to fetch payment methods'}
        </p>
        <Button onClick={() => refetch()} variant="outline" size="sm">
          <RefreshCw className="w-4 h-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        {/* Header with Add Button */}
        <div className="flex items-center justify-between">
          <div>
            <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              Payment Methods
            </h3>
            <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Manage your saved payment methods
            </p>
          </div>
          <Button
            onClick={() => setAddPaymentModalOpen(true)}
            className="bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] hover:from-[#12C2E9] hover:to-[#19D86C]"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Payment Method
          </Button>
        </div>

        {/* Payment Methods Grid */}
        {methods && methods.length > 0 ? (
          <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {methods.map((method) => {
              const isExpired = isPaymentMethodExpired(method);
              const isExpiringSoon = isPaymentMethodExpiringSoon(method);
              
              return (
                <Card
                  key={method.id}
                  className={`p-4 transition-all hover:shadow-md ${
                    isDarkMode 
                      ? "bg-gray-800 border-gray-700 text-white hover:bg-gray-750" 
                      : "bg-white border-gray-200 text-black hover:bg-gray-50"
                  } ${isExpired ? 'border-red-500/50' : isExpiringSoon ? 'border-yellow-500/50' : ''}`}
                >
                  <CardContent className="p-0 space-y-4">
                    {/* Card Info */}
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
                          <CreditCard size={20} className={getCardBrandColor(method.brand)} />
                        </div>
                        <div>
                          <p className={`font-semibold capitalize ${getCardBrandColor(method.brand)}`}>
                            {method.brand}
                          </p>
                          <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            •••• •••• •••• {method.last4}
                          </p>
                        </div>
                      </div>
                      
                      {method.is_default && (
                        <div className="flex items-center gap-1 bg-green-500/10 text-green-500 px-2 py-1 rounded-full text-xs font-medium">
                          <Star size={12} />
                          Default
                        </div>
                      )}
                    </div>

                    {/* Expiration Info */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          Expires
                        </span>
                        <span className={`text-sm font-medium ${
                          isExpired ? 'text-red-500' : 
                          isExpiringSoon ? 'text-yellow-500' : 
                          isDarkMode ? 'text-gray-300' : 'text-gray-700'
                        }`}>
                          {String(method.exp_month).padStart(2, "0")}/{method.exp_year}
                        </span>
                      </div>

                      {/* Warning Messages */}
                      {isExpired && (
                        <div className="flex items-center gap-2 text-red-500 text-xs">
                          <AlertCircle size={12} />
                          <span>Expired</span>
                        </div>
                      )}
                      
                      {isExpiringSoon && !isExpired && (
                        <div className="flex items-center gap-2 text-yellow-500 text-xs">
                          <AlertCircle size={12} />
                          <span>Expires soon</span>
                        </div>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2 pt-2">
                      {method.is_default ? (
                        <Button 
                          disabled 
                          size="sm" 
                          className="flex-1 bg-green-500/20 text-green-500 border-green-500/30 cursor-not-allowed"
                        >
                          <Star className="w-3 h-3 mr-1 fill-current" />
                          Default
                        </Button>
                      ) : (
                        <Button
                          size="sm"
                          variant="outline"
                          className={`flex-1 ${
                            isDarkMode 
                              ? 'border-gray-600 text-gray-300 hover:bg-gray-700' 
                              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                          onClick={() => {
                            setSelectedMethodId(method.stripe_payment_method_id);
                            setActionType("default");
                            setDialogOpen(true);
                          }}
                          disabled={setDefault.isPending || remove.isPending}
                        >
                          <StarOff className="w-3 h-3 mr-1" />
                          Set Default
                        </Button>
                      )}

                      <Button
                        size="sm"
                        variant="destructive"
                        className="px-3"
                        onClick={() => {
                          setSelectedMethodId(method.stripe_payment_method_id);
                          setActionType("delete");
                          setDialogOpen(true);
                        }}
                        disabled={setDefault.isPending || remove.isPending}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        ) : (
          /* Empty State */
          <div className="text-center py-12">
            <div className={`mx-auto mb-4 p-3 rounded-full w-fit ${
              isDarkMode ? 'bg-gray-800' : 'bg-gray-100'
            }`}>
              <CreditCard size={32} className={isDarkMode ? 'text-gray-600' : 'text-gray-400'} />
            </div>
            <h3 className={`text-lg font-medium mb-2 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              No Payment Methods
            </h3>
            <p className={`text-sm mb-6 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              Add a payment method to start purchasing tokens and manage your billing.
            </p>
            <Button
              onClick={() => setAddPaymentModalOpen(true)}
              className="bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] hover:from-[#12C2E9] hover:to-[#19D86C]"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Your First Payment Method
            </Button>
          </div>
        )}

        {/* Loading overlay for operations */}
        {(setDefault.isPending || remove.isPending) && (
          <div className="fixed inset-0 bg-black/20 flex items-center justify-center z-50">
            <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg`}>
              <div className="flex items-center gap-3">
                <RefreshCw className="animate-spin h-5 w-5 text-[#19D86C]" />
                <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>
                  {setDefault.isPending ? 'Setting default...' : 'Deleting payment method...'}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className={isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-black'}>
          <DialogHeader>
            <DialogTitle>
              {actionType === "delete" ? "Delete Payment Method" : "Set as Default"}
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              {actionType === "delete"
                ? "Are you sure you want to delete this payment method? This action cannot be undone and may affect any active subscriptions."
                : "Do you want to set this payment method as your default? It will be used for future purchases unless you specify otherwise."}
            </p>
          </div>
          <DialogFooter className="gap-2">
            <Button 
              variant="outline" 
              onClick={() => setDialogOpen(false)}
              className={isDarkMode ? 'border-gray-600 text-gray-300' : 'border-gray-300 text-gray-700'}
            >
              Cancel
            </Button>
            <Button
              variant={actionType === "delete" ? "destructive" : "default"}
              onClick={handleConfirm}
              className={actionType === "delete" ? undefined : "bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215]"}
            >
              {actionType === "delete" ? "Delete" : "Set as Default"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Payment Method Modal */}
      <AddPaymentMethodModal
        isOpen={addPaymentModalOpen}
        onClose={() => setAddPaymentModalOpen(false)}
        isDarkMode={isDarkMode}
      />
    </>
  );
};

export default PaymentMethodsTab;