import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  CreditCard, 
  Shield, 
  RefreshCw
} from 'lucide-react';
import { PaymentMethod } from '@/lib/api/stripe-api';

interface PurchaseConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (paymentMethodId: string) => Promise<void>;
  planDetails: {
    name: string;
    tokens: number;
    price: number;
    description: string;
  };
  paymentMethods: PaymentMethod[];
  isProcessing: boolean;
  isDarkMode: boolean;
}

const PurchaseConfirmationModal: React.FC<PurchaseConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  planDetails,
  paymentMethods,
  isProcessing,
  isDarkMode
}) => {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      // Set default payment method
      const defaultMethod = paymentMethods.find(method => method.is_default);
      if (defaultMethod) {
        setSelectedPaymentMethod(defaultMethod.stripe_payment_method_id);
      } else if (paymentMethods.length > 0) {
        setSelectedPaymentMethod(paymentMethods[0].stripe_payment_method_id);
      }
    } else {
      // Reset state when modal closes
      setSelectedPaymentMethod('');
    }
  }, [isOpen, paymentMethods]);

  const formatPrice = (priceInCents: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(priceInCents / 100);
  };

  const getSelectedPaymentMethodDetails = () => {
    return paymentMethods.find(method => method.stripe_payment_method_id === selectedPaymentMethod);
  };

  const handleSubmit = async () => {
    if (!selectedPaymentMethod) return;
    
    try {
      await onConfirm(selectedPaymentMethod);
    } catch (error) {
      // Error handling is done in parent component
    }
  };

  const canSubmit = selectedPaymentMethod && !isProcessing;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`max-w-md ${isDarkMode ? 'bg-gray-900 text-white border-gray-700' : 'bg-white text-black border-gray-200'}`}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="w-5 h-5 text-green-500" />
            Confirm Purchase
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Plan Details */}
          <div className={`p-4 rounded-lg border ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-50 border-gray-200'}`}>
            <h3 className="font-semibold mb-2">{planDetails.name} Plan</h3>
            <p className={`text-sm mb-3 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {planDetails.description}
            </p>
            <div className="flex justify-between items-center">
              <span className="text-sm">Tokens:</span>
              <span className="font-semibold">{planDetails.tokens.toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm">Price:</span>
              <span className="font-semibold text-lg">{formatPrice(planDetails.price)}</span>
            </div>
          </div>

          {/* Payment Method Selection */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Payment Method
            </label>
            <Select value={selectedPaymentMethod} onValueChange={setSelectedPaymentMethod}>
              <SelectTrigger className={`${
                isDarkMode ? 'bg-gray-800 border-gray-700 text-white' : 'bg-white border-gray-300 text-black'
              }`}>
                <div className="flex items-center gap-2">
                  <CreditCard className="w-4 h-4" />
                  <SelectValue placeholder="Select payment method" />
                </div>
              </SelectTrigger>
              <SelectContent className={isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-300'}>
                {paymentMethods.map((method) => (
                  <SelectItem key={method.stripe_payment_method_id} value={method.stripe_payment_method_id}>
                    <div className="flex items-center gap-2">
                      <span className="capitalize">{method.brand}</span>
                      <span>•••• {method.last4}</span>
                      {method.is_default && (
                        <span className="text-xs bg-green-500/20 text-green-500 px-1 py-0.5 rounded">
                          Default
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Security Notice */}
          <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-gray-800/50' : 'bg-gray-50'}`}>
            <div className="flex items-start gap-2">
              <Shield className="w-4 h-4 text-green-500 mt-0.5" />
              <div>
                <p className={`text-xs ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  Your payment is secured with 256-bit SSL encryption.
                </p>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isProcessing}
            className={isDarkMode ? 'border-gray-600 text-gray-300' : 'border-gray-300 text-gray-700'}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!canSubmit}
            className="bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessing ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Processing...
              </>
            ) : (
              `Purchase ${formatPrice(planDetails.price)}`
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PurchaseConfirmationModal;