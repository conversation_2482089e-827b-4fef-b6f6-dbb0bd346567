/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Drawer, DrawerContent, DrawerHeader, DrawerTitle, DrawerFooter } from "@/components/ui/drawer";
import { CardElement, useStripe, useElements } from "@stripe/react-stripe-js";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { useMediaQuery } from "react-responsive";
import { createSetupIntent, confirmSetupIntent, SetupIntentResponse } from "@/lib/api/stripe-api";
import { toast } from 'sonner';

interface AddPaymentMethodModalProps {
  isOpen: boolean;
  onClose: () => void;
  isDarkMode: boolean;
}

const AddPaymentMethodModal: React.FC<AddPaymentMethodModalProps> = ({ isOpen, onClose, isDarkMode }) => {
  const stripe = useStripe();
  const elements = useElements();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const isMobile = useMediaQuery({ maxWidth: 767 });

  useEffect(() => {
    setIsClient(true);
  }, []);

  const createSetupIntentMutation = useMutation<SetupIntentResponse, Error>({
    mutationFn: createSetupIntent,
    onError: (error) => {
      console.error('Setup intent creation error:', error);
      toast.error(error.message || 'Failed to initialize payment setup.');
    },
  });

  const confirmSetupIntentMutation = useMutation<void, Error, string>({
    mutationFn: confirmSetupIntent,
    onSuccess: () => {
      toast.success('Payment method added successfully.');
      // Invalidate payment methods query to refresh the list
      queryClient.invalidateQueries({ queryKey: ['payment-methods'] });
      queryClient.invalidateQueries({ queryKey: ['tokenBalance'] });
      onClose();
    },
    onError: (error) => {
      console.error('Setup intent confirmation error:', error);
      toast.error(error.message || 'Failed to save payment method.');
    },
  });

  useEffect(() => {
    if (
      isOpen &&
      stripe &&
      isClient &&
      !createSetupIntentMutation.isPending &&
      !createSetupIntentMutation.data
    ) {
      createSetupIntentMutation.mutate();
    }
    if (!isOpen && createSetupIntentMutation.data) {
      createSetupIntentMutation.reset();
    }
  }, [isOpen, stripe, isClient, createSetupIntentMutation]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!stripe || !elements || !createSetupIntentMutation.data || !isClient) {
      toast.error('Payment system not ready. Please try again.');
      return;
    }

    setIsSubmitting(true);
    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      toast.error('Card information not found. Please try again.');
      setIsSubmitting(false);
      return;
    }

    try {
      const { client_secret, setup_intent_id } = createSetupIntentMutation.data;
      
      const result = await stripe.confirmCardSetup(client_secret, {
        payment_method: { 
          card: cardElement,
          billing_details: {
            // You can add billing details here if needed
          }
        },
      });

      if (result.error) {
        console.error('Stripe confirmCardSetup error:', result.error);
        toast.error(result.error.message || 'Failed to add payment method.');
      } else if (result.setupIntent?.status === "succeeded") {
        confirmSetupIntentMutation.mutate(setup_intent_id);
      } else {
        toast.error('Payment method setup was not completed. Please try again.');
      }
    } catch (error: any) {
      console.error('Payment method setup error:', error);
      toast.error(error.message || 'An error occurred while adding payment method.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting && !confirmSetupIntentMutation.isPending) {
      createSetupIntentMutation.reset();
      onClose();
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        color: isDarkMode ? "#ffffff" : "#000000",
        fontSize: "16px",
        fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
        fontSmoothing: "antialiased",
        "::placeholder": { 
          color: isDarkMode ? "#a0aec0" : "#a0aec0" 
        },
        backgroundColor: "transparent",
      },
      invalid: { 
        color: "#f56565",
        iconColor: "#f56565"
      },
      complete: {
        color: isDarkMode ? "#ffffff" : "#000000",
      }
    },
    hidePostalCode: false,
  };

  const isLoading = isSubmitting || confirmSetupIntentMutation.isPending;
  const isDisabled = isLoading || !stripe || !elements || !createSetupIntentMutation.data;

  if (!isClient) return null;

  const content = (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="mb-6">
        <label className={`block text-sm font-medium mb-3 ${isDarkMode ? "text-white" : "text-gray-900"}`}>
          Card Details
        </label>
        <div className={`p-4 rounded-md border transition-colors ${
          isDarkMode 
            ? "border-gray-700 bg-gray-800 focus-within:border-[#19D86C]" 
            : "border-gray-200 bg-white focus-within:border-[#19D86C]"
        }`}>
          <CardElement options={cardElementOptions} />
        </div>
        <p className={`text-xs mt-2 ${isDarkMode ? "text-gray-400" : "text-gray-600"}`}>
          Your payment information is encrypted and secure.
        </p>
      </div>

      {createSetupIntentMutation.isError && (
        <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-md">
          <p className="text-red-500 text-sm">
            Failed to initialize payment setup. Please try again.
          </p>
        </div>
      )}

      <DialogFooter className="sm:justify-end gap-2">
        <Button
          type="button"
          variant="outline"
          onClick={handleClose}
          disabled={isLoading}
          className={`${
            isDarkMode 
              ? "text-white border-gray-700 hover:bg-gray-700" 
              : "text-gray-900 border-gray-200 hover:bg-gray-50"
          }`}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isDisabled}
          className="bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] hover:from-[#12C2E9] hover:to-[#19D86C] disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 border-2 border-[#111215] border-t-transparent rounded-full animate-spin"></div>
              Processing...
            </div>
          ) : (
            "Add Payment Method"
          )}
        </Button>
      </DialogFooter>
    </form>
  );

  return isMobile ? (
    <Drawer open={isOpen} onOpenChange={handleClose}>
      <DrawerContent className={isDarkMode ? "bg-gray-900 text-white" : "bg-white text-black"}>
        <DrawerHeader>
          <DrawerTitle>Add Payment Method</DrawerTitle>
        </DrawerHeader>
        <div className="px-4">
          {content}
        </div>
        <DrawerFooter />
      </DrawerContent>
    </Drawer>
  ) : (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className={`${isDarkMode ? "bg-gray-900 text-white" : "bg-white text-black"} max-w-md`}>
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold">Add Payment Method</DialogTitle>
        </DialogHeader>
        {content}
      </DialogContent>
    </Dialog>
  );
};

export default AddPaymentMethodModal;