/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState, useMemo } from "react";
import {
	Calendar,
	CreditCard,
	TrendingUp,
	DollarSign,
	Download,
	RefreshCw,
	AlertCircle,
	CheckCircle,
	Clock,
	Filter,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useTokenHistory } from "@/hooks/use-token-data";

interface BillingHistoryTabProps {
	isDarkMode: boolean;
}

const BillingHistoryTab: React.FC<BillingHistoryTabProps> = ({
	isDarkMode,
}) => {
	const [filterPlan, setFilterPlan] = useState<string>("all");
	const [filterAction, setFilterAction] = useState<string>("all");
	const [sortBy, setSortBy] = useState<string>("newest");

	const {
		data: tokenHistory,
		isLoading,
		error,
		refetch,
	} = useTokenHistory(100);

	// Calculate total spending and statistics
	const statistics = useMemo(() => {
		if (!tokenHistory?.history) {
			return {
				totalSpent: 0,
				totalTokens: 0,
				totalTransactions: 0,
				averageTransaction: 0,
				planBreakdown: {},
				monthlySpending: {},
			};
		}

		const history = tokenHistory.history;
		let totalSpent = 0;
		let totalTokens = 0;
		const planBreakdown: Record<
			string,
			{ count: number; amount: number; tokens: number }
		> = {};
		const monthlySpending: Record<string, number> = {};

		// Token pricing based on your plans (in cents)
		const planPricing: Record<string, number> = {
			light: 1000, // $10.00
			basic: 10000, // $100.00
			standard: 200000, // $2000.00
			ultimate: 5000000, // $50000.00
			custom: 1, // $0.01 per token
		};

		history.forEach((transaction) => {
			const planType = transaction.plan_type.toLowerCase();
			const tokensAdded = transaction.tokens_added;

			// Calculate amount based on plan or custom pricing
			let amount = 0;
			if (planType === "custom") {
				amount = tokensAdded * planPricing.custom; // $0.01 per token
			} else {
				amount = planPricing[planType] || 0;
			}

			totalSpent += amount;
			totalTokens += tokensAdded;

			// Plan breakdown
			if (!planBreakdown[planType]) {
				planBreakdown[planType] = { count: 0, amount: 0, tokens: 0 };
			}
			planBreakdown[planType].count += 1;
			planBreakdown[planType].amount += amount;
			planBreakdown[planType].tokens += tokensAdded;

			// Monthly breakdown
			const month = new Date(transaction.created_at).toLocaleDateString(
				"en-US",
				{
					year: "numeric",
					month: "short",
				}
			);
			monthlySpending[month] = (monthlySpending[month] || 0) + amount;
		});

		return {
			totalSpent,
			totalTokens,
			totalTransactions: history.length,
			averageTransaction:
				history.length > 0 ? totalSpent / history.length : 0,
			planBreakdown,
			monthlySpending,
		};
	}, [tokenHistory]);

	// Filter and sort transactions
	const filteredAndSortedHistory = useMemo(() => {
		if (!tokenHistory?.history) return [];

		const filtered = tokenHistory.history.filter((transaction) => {
			const planMatch =
				filterPlan === "all" ||
				transaction.plan_type.toLowerCase() === filterPlan;
			const actionMatch =
				filterAction === "all" || transaction.action === filterAction;
			return planMatch && actionMatch;
		});

		// Sort transactions
		filtered.sort((a, b) => {
			switch (sortBy) {
				case "newest":
					return (
						new Date(b.created_at).getTime() -
						new Date(a.created_at).getTime()
					);
				case "oldest":
					return (
						new Date(a.created_at).getTime() -
						new Date(b.created_at).getTime()
					);
				case "highest":
					return b.tokens_added - a.tokens_added;
				case "lowest":
					return a.tokens_added - b.tokens_added;
				default:
					return 0;
			}
		});

		return filtered;
	}, [tokenHistory, filterPlan, filterAction, sortBy]);

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat("en-GB", {
			style: "currency",
			currency: "GBP",
			minimumFractionDigits: 3,
		}).format(amount);
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		});
	};

	const getPlanDisplayName = (planType: string) => {
		const names: Record<string, string> = {
			light: "Light",
			basic: "Basic",
			standard: "Standard",
			ultimate: "Ultimate",
			custom: "Custom",
		};
		return names[planType.toLowerCase()] || planType;
	};

	const getPlanAmount = (transaction: any) => {
		const planPricing: Record<string, number> = {
			light: 1000,
			basic: 10000,
			standard: 200000,
			ultimate: 5000000,
			custom: 1,
		};

		const planType = transaction.plan_type.toLowerCase();
		if (planType === "custom") {
			return transaction.tokens_added * planPricing.custom;
		}
		return planPricing[planType] || 0;
	};

	const getActionBadge = (action: string) => {
		const badges = {
			purchase: {
				color: "bg-blue-500/20 text-blue-500",
				icon: CheckCircle,
			},
			top_up: {
				color: "bg-green-500/20 text-green-500",
				icon: TrendingUp,
			},
			expired: { color: "bg-red-500/20 text-red-500", icon: Clock },
		};

		const badge = badges[action as keyof typeof badges] || badges.purchase;
		const Icon = badge.icon;

		return (
			<div
				className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${badge.color}`}>
				<Icon size={12} />
				{action.charAt(0).toUpperCase() + action.slice(1)}
			</div>
		);
	};

	if (isLoading) {
		return (
			<div className="flex items-center justify-center py-12">
				<div className="flex items-center gap-3">
					<RefreshCw className="animate-spin h-6 w-6 text-green-500" />
					<span
						className={isDarkMode ? "text-white" : "text-gray-900"}>
						Loading billing history...
					</span>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="text-center py-12">
				<AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
				<h3
					className={`text-lg font-medium mb-2 ${
						isDarkMode ? "text-white" : "text-gray-900"
					}`}>
					Failed to Load Billing History
				</h3>
				<p
					className={`text-sm mb-4 ${
						isDarkMode ? "text-gray-400" : "text-gray-600"
					}`}>
					{error.message || "Unable to fetch billing history"}
				</p>
				<Button onClick={() => refetch()} variant="outline" size="sm">
					<RefreshCw className="w-4 h-4 mr-2" />
					Try Again
				</Button>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h3
						className={`text-lg font-semibold ${
							isDarkMode ? "text-white" : "text-gray-900"
						}`}>
						Billing History
					</h3>
					<p
						className={`text-sm ${
							isDarkMode ? "text-gray-400" : "text-gray-600"
						}`}>
						View your token purchase history and spending analytics
					</p>
				</div>
				<Button
					variant="outline"
					size="sm"
					className={`${
						isDarkMode
							? "border-gray-600 text-gray-300"
							: "border-gray-300 text-gray-700"
					}`}>
					<Download className="w-4 h-4 mr-2" />
					Export
				</Button>
			</div>

			{/* Statistics Cards */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<div
					className={`p-4 rounded-lg border ${
						isDarkMode
							? "bg-gray-800 border-gray-700"
							: "bg-white border-gray-200"
					}`}>
					<div className="flex items-center gap-2 mb-2">
						<DollarSign className="w-5 h-5 text-green-500" />
						<span
							className={`text-sm font-medium ${
								isDarkMode ? "text-gray-400" : "text-gray-600"
							}`}>
							Total Spent
						</span>
					</div>
					<p
						className={`text-2xl font-bold ${
							isDarkMode ? "text-white" : "text-gray-900"
						}`}>
						{formatCurrency(statistics.totalSpent)}
					</p>
				</div>

				<div
					className={`p-4 rounded-lg border ${
						isDarkMode
							? "bg-gray-800 border-gray-700"
							: "bg-white border-gray-200"
					}`}>
					<div className="flex items-center gap-2 mb-2">
						<TrendingUp className="w-5 h-5 text-blue-500" />
						<span
							className={`text-sm font-medium ${
								isDarkMode ? "text-gray-400" : "text-gray-600"
							}`}>
							Total Tokens
						</span>
					</div>
					<p
						className={`text-2xl font-bold ${
							isDarkMode ? "text-white" : "text-gray-900"
						}`}>
						{statistics.totalTokens.toLocaleString()}
					</p>
				</div>

				<div
					className={`p-4 rounded-lg border ${
						isDarkMode
							? "bg-gray-800 border-gray-700"
							: "bg-white border-gray-200"
					}`}>
					<div className="flex items-center gap-2 mb-2">
						<CreditCard className="w-5 h-5 text-purple-500" />
						<span
							className={`text-sm font-medium ${
								isDarkMode ? "text-gray-400" : "text-gray-600"
							}`}>
							Transactions
						</span>
					</div>
					<p
						className={`text-2xl font-bold ${
							isDarkMode ? "text-white" : "text-gray-900"
						}`}>
						{statistics.totalTransactions}
					</p>
				</div>

				<div
					className={`p-4 rounded-lg border ${
						isDarkMode
							? "bg-gray-800 border-gray-700"
							: "bg-white border-gray-200"
					}`}>
					<div className="flex items-center gap-2 mb-2">
						<Calendar className="w-5 h-5 text-orange-500" />
						<span
							className={`text-sm font-medium ${
								isDarkMode ? "text-gray-400" : "text-gray-600"
							}`}>
							Avg. Transaction
						</span>
					</div>
					<p
						className={`text-2xl font-bold ${
							isDarkMode ? "text-white" : "text-gray-900"
						}`}>
						{formatCurrency(statistics.averageTransaction)}
					</p>
				</div>
			</div>

			{/* Filters */}
			<div className="flex flex-wrap gap-4 items-center">
				<div className="flex items-center gap-2">
					<Filter className="w-4 h-4" />
					<span
						className={`text-sm font-medium ${
							isDarkMode ? "text-gray-300" : "text-gray-700"
						}`}>
						Filters:
					</span>
				</div>

				<Select value={filterPlan} onValueChange={setFilterPlan}>
					<SelectTrigger
						className={`w-32 ${
							isDarkMode
								? "bg-gray-800 border-gray-700"
								: "bg-white border-gray-300"
						}`}>
						<SelectValue placeholder="Plan" />
					</SelectTrigger>
					<SelectContent
						className={
							isDarkMode
								? "bg-gray-800 border-gray-700"
								: "bg-white border-gray-300"
						}>
						<SelectItem value="all">All Plans</SelectItem>
						<SelectItem value="light">Light</SelectItem>
						<SelectItem value="basic">Basic</SelectItem>
						<SelectItem value="standard">Standard</SelectItem>
						<SelectItem value="ultimate">Ultimate</SelectItem>
						<SelectItem value="custom">Custom</SelectItem>
					</SelectContent>
				</Select>

				<Select value={filterAction} onValueChange={setFilterAction}>
					<SelectTrigger
						className={`w-32 ${
							isDarkMode
								? "bg-gray-800 border-gray-700"
								: "bg-white border-gray-300"
						}`}>
						<SelectValue placeholder="Action" />
					</SelectTrigger>
					<SelectContent
						className={
							isDarkMode
								? "bg-gray-800 border-gray-700"
								: "bg-white border-gray-300"
						}>
						<SelectItem value="all">All Actions</SelectItem>
						<SelectItem value="purchase">Purchase</SelectItem>
						<SelectItem value="top_up">Top Up</SelectItem>
						<SelectItem value="expired">Expired</SelectItem>
					</SelectContent>
				</Select>

				<Select value={sortBy} onValueChange={setSortBy}>
					<SelectTrigger
						className={`w-32 ${
							isDarkMode
								? "bg-gray-800 border-gray-700"
								: "bg-white border-gray-300"
						}`}>
						<SelectValue placeholder="Sort" />
					</SelectTrigger>
					<SelectContent
						className={
							isDarkMode
								? "bg-gray-800 border-gray-700"
								: "bg-white border-gray-300"
						}>
						<SelectItem value="newest">Newest</SelectItem>
						<SelectItem value="oldest">Oldest</SelectItem>
						<SelectItem value="highest">Highest Amount</SelectItem>
						<SelectItem value="lowest">Lowest Amount</SelectItem>
					</SelectContent>
				</Select>
			</div>

			{/* Transactions Table */}
			<div
				className={`rounded-lg border ${
					isDarkMode ? "border-gray-700" : "border-gray-200"
				} overflow-hidden`}>
				<div
					className={`px-4 py-3 border-b ${
						isDarkMode
							? "border-gray-700 bg-gray-800"
							: "border-gray-200 bg-gray-50"
					}`}>
					<h4
						className={`font-medium ${
							isDarkMode ? "text-white" : "text-gray-900"
						}`}>
						Transaction History ({filteredAndSortedHistory.length})
					</h4>
				</div>

				{filteredAndSortedHistory.length === 0 ? (
					<div className="text-center py-12">
						<CreditCard
							className={`h-12 w-12 mx-auto mb-4 ${
								isDarkMode ? "text-gray-600" : "text-gray-400"
							}`}
						/>
						<h3
							className={`text-lg font-medium mb-2 ${
								isDarkMode ? "text-white" : "text-gray-900"
							}`}>
							No Transactions Found
						</h3>
						<p
							className={`text-sm ${
								isDarkMode ? "text-gray-400" : "text-gray-600"
							}`}>
							{tokenHistory?.history?.length === 0
								? "You haven't made any token purchases yet."
								: "No transactions match your current filters."}
						</p>
					</div>
				) : (
					<div className="overflow-x-auto">
						<table className="w-full">
							<thead
								className={`${
									isDarkMode ? "bg-gray-800" : "bg-gray-50"
								}`}>
								<tr>
									<th
										className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${
											isDarkMode
												? "text-gray-400"
												: "text-gray-500"
										}`}>
										Date
									</th>
									<th
										className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${
											isDarkMode
												? "text-gray-400"
												: "text-gray-500"
										}`}>
										Plan
									</th>
									<th
										className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${
											isDarkMode
												? "text-gray-400"
												: "text-gray-500"
										}`}>
										Action
									</th>
									<th
										className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${
											isDarkMode
												? "text-gray-400"
												: "text-gray-500"
										}`}>
										Tokens
									</th>
									<th
										className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${
											isDarkMode
												? "text-gray-400"
												: "text-gray-500"
										}`}>
										Amount
									</th>
									<th
										className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${
											isDarkMode
												? "text-gray-400"
												: "text-gray-500"
										}`}>
										Payment ID
									</th>
								</tr>
							</thead>
							<tbody
								className={`divide-y ${
									isDarkMode
										? "divide-gray-700"
										: "divide-gray-200"
								}`}>
								{filteredAndSortedHistory.map(
									(transaction, index) => (
										<tr
											key={index}
											className={`${
												isDarkMode
													? "hover:bg-gray-800"
													: "hover:bg-gray-50"
											} transition-colors`}>
											<td
												className={`px-4 py-4 whitespace-nowrap text-sm ${
													isDarkMode
														? "text-gray-300"
														: "text-gray-700"
												}`}>
												{formatDate(
													transaction.created_at
												)}
											</td>
											<td className="px-4 py-4 whitespace-nowrap">
												<span
													className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
														transaction.plan_type ===
														"basic"
															? "bg-green-500/20 text-green-500"
															: transaction.plan_type ===
															  "light"
															? "bg-blue-500/20 text-blue-500"
															: transaction.plan_type ===
															  "standard"
															? "bg-purple-500/20 text-purple-500"
															: transaction.plan_type ===
															  "ultimate"
															? "bg-orange-500/20 text-orange-500"
															: "bg-gray-500/20 text-gray-500"
													}`}>
													{getPlanDisplayName(
														transaction.plan_type
													)}
												</span>
											</td>
											<td className="px-4 py-4 whitespace-nowrap">
												{getActionBadge(
													transaction.action
												)}
											</td>
											<td
												className={`px-4 py-4 whitespace-nowrap text-sm font-medium ${
													isDarkMode
														? "text-white"
														: "text-gray-900"
												}`}>
												+
												{transaction.tokens_added.toLocaleString()}
											</td>
											<td
												className={`px-4 py-4 whitespace-nowrap text-sm font-medium ${
													isDarkMode
														? "text-white"
														: "text-gray-900"
												}`}>
												{formatCurrency(
													getPlanAmount(transaction)
												)}
											</td>
											<td
												className={`px-4 py-4 whitespace-nowrap text-sm ${
													isDarkMode
														? "text-gray-400"
														: "text-gray-600"
												}`}>
												<code className="text-xs bg-gray-500/20 px-2 py-1 rounded">
													{transaction.stripe_payment_intent_id?.slice(
														0,
														16
													)}
													...
												</code>
											</td>
										</tr>
									)
								)}
							</tbody>
						</table>
					</div>
				)}
			</div>
		</div>
	);
};

export default BillingHistoryTab;
