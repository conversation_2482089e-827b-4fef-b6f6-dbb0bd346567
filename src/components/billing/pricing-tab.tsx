/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect } from "react";
import { Check, Headphones } from "lucide-react";
// import {
// 	Dialog,
// 	DialogContent,
// 	DialogHeader,
// 	DialogTitle,
// 	DialogFooter,
// } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
	purchaseTokens,
	getPaymentMethods,
	PaymentMethod,
} from "@/lib/api/stripe-api";
import { useTokenPurchaseSuccess } from "@/hooks/use-token-data";
import PurchaseConfirmationModal from "./purchase-confirmation-modal";

interface PricingTabProps {
	isDarkMode: boolean;
}

interface PricingTier {
	name: string;
	price: string;
	period: string;
	checkOraRuns: string;
	inspectOraRuns: string;
	isPopular?: boolean;
}

const pricingTiers: PricingTier[] = [
	{
		name: "Light",
		price: "£0.99",
		period: "/one-time",
		checkOraRuns: "50",
		inspectOraRuns: "68",
	},
	{
		name: "Basic",
		price: "£2.99",
		period: "/one-time",
		checkOraRuns: "500",
		inspectOraRuns: "682",
	},
	{
		name: "Standard",
		price: "£9.99",
		period: "/one-time",
		checkOraRuns: "5,000",
		inspectOraRuns: "6,818",
		isPopular: true,
	},
	{
		name: "Premium",
		price: "£14.99",
		period: "/one-time",
		checkOraRuns: "10,000",
		inspectOraRuns: "13,636",
	},
	{
		name: "Ultimate",
		price: "£22.99",
		period: "/one-time",
		checkOraRuns: "20,000",
		inspectOraRuns: "27,273",
	},
];

const FAQ_DATA = [
	{
		question: "How do tokens work?",
		answer: "Tokens are consumed when you make API calls to our services. Each service has different token costs based on complexity. You can monitor your usage in real-time through your dashboard.",
	},
	{
		question: "What happens when I run out of tokens?",
		answer: "When you exhaust your tokens, API calls will be temporarily suspended until you upgrade your plan or wait for your next billing cycle. We'll send notifications when you're running low.",
	},
	{
		question: "Can I upgrade or downgrade my plan anytime?",
		answer: "Yes, you can change your plan at any time. Upgrades take effect immediately, while downgrades take effect at the next billing cycle. Unused tokens from upgrades are prorated.",
	},
	{
		question: "Do unused tokens roll over?",
		answer: "Tokens have validity periods based on your plan. Light plan tokens expire after 30 days, Basic after 60 days, Standard after 90 days, and Ultimate tokens never expire.",
	},
	{
		question: "Is there a free tier?",
		answer: "We offer a free trial with limited tokens to help you get started. Once exhausted, you'll need to choose a paid plan to continue using our services.",
	},
	{
		question: "What payment methods do you accept?",
		answer: "We accept all major credit cards (Visa, MasterCard, American Express), PayPal, and bank transfers for enterprise customers. All payments are processed securely.",
	},
	{
		question: "Can I get a custom plan?",
		answer: "Yes! For unique requirements or high-volume usage, we offer custom plans. Contact our sales team to discuss your specific needs and get a tailored solution.",
	},
	{
		question: "Is there a setup fee?",
		answer: "No setup fees for any of our plans. You only pay for the tokens you purchase. Enterprise customers may have optional professional services available.",
	},
];

const PricingTab: React.FC<PricingTabProps> = ({ isDarkMode }) => {
	const [openFaqIndex, setOpenFaqIndex] = useState<number | null>(null);
	// const [isCustomDialogOpen, setIsCustomDialogOpen] = useState(false);
	const [isPurchaseConfirmationOpen, setIsPurchaseConfirmationOpen] =
		useState(false);
	// const [customTokens, setCustomTokens] = useState<string>("10");
	const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
	const [, setSelectedPaymentMethod] = useState<string | undefined>(
		undefined
	);
	const [loadingPlan, setLoadingPlan] = useState<string | null>(null);
	const [isFetchingPaymentMethods, setIsFetchingPaymentMethods] =
		useState(false);
	const [selectedPlanForPurchase, setSelectedPlanForPurchase] = useState<
		string | null
	>(null);

	const { handlePurchaseSuccess } = useTokenPurchaseSuccess();

	// Fetch payment methods on mount
	useEffect(() => {
		const fetchPaymentMethods = async () => {
			setIsFetchingPaymentMethods(true);
			try {
				const methods = await getPaymentMethods();
				setPaymentMethods(methods);
				const defaultMethod = methods.find(
					(method) => method.is_default
				);
				if (defaultMethod) {
					setSelectedPaymentMethod(
						defaultMethod.stripe_payment_method_id
					);
				} else if (methods.length > 0) {
					setSelectedPaymentMethod(
						methods[0].stripe_payment_method_id
					);
				}
			} catch (error) {
				console.error("Failed to fetch payment methods:", error);
				toast.error("Failed to fetch payment methods.");
			} finally {
				setIsFetchingPaymentMethods(false);
			}
		};
		fetchPaymentMethods();
	}, []);

	// const calculateCustomPrice = (tokens: number) => {
	// 	return tokens * 1; // £0.01 per token, in pence
	// };

	// const handleCustomTokensChange = (value: string) => {
	// 	const numericValue = value.replace(/\D/g, "");

	// 	if (numericValue === "") {
	// 		setCustomTokens("");
	// 		return;
	// 	}

	// 	const parsedValue = parseInt(numericValue, 10);
	// 	if (!isNaN(parsedValue)) {
	// 		setCustomTokens(parsedValue.toString());
	// 	}
	// };

	// const getCustomTokensNumeric = (): number => {
	// 	const value = parseInt(customTokens, 10);
	// 	return isNaN(value) ? 0 : value;
	// };

	const handlePurchase = async (planIndex: number) => {
		if (paymentMethods.length === 0) {
			toast.error("Please add a payment method first.");
			return;
		}

		const planName = pricingTiers[planIndex].name.toLowerCase();
		setSelectedPlanForPurchase(planName);
		setIsPurchaseConfirmationOpen(true);
	};

	const handleConfirmPurchase = async (paymentMethodId: string) => {
		if (!selectedPlanForPurchase) return;

		setLoadingPlan(selectedPlanForPurchase);

		try {
			const response = await purchaseTokens(
				selectedPlanForPurchase,
				paymentMethodId
			);

			toast.success(response.message || "Token purchase successful!");
			handlePurchaseSuccess();
			setIsPurchaseConfirmationOpen(false);
			setSelectedPlanForPurchase(null);
		} catch (error: any) {
			console.error("Purchase error:", error);
			const errorMessage = error.message || "Failed to process payment.";
			toast.error(errorMessage);
			throw error;
		} finally {
			setLoadingPlan(null);
		}
	};

	const toggleFaq = (index: number) => {
		setOpenFaqIndex(openFaqIndex === index ? null : index);
	};

	const isPlanLoading = (planName: string) => {
		return loadingPlan === planName.toLowerCase();
	};

	const getButtonText = (planName: string) => {
		if (isPlanLoading(planName)) {
			return "Processing...";
		}
		return "Get Started";
	};

	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const isButtonDisabled = (name: string) => {
		return (
			loadingPlan !== null ||
			isFetchingPaymentMethods ||
			paymentMethods.length === 0
		);
	};

	const getPlanDetailsForConfirmation = () => {
		if (!selectedPlanForPurchase) return null;

		const tier = pricingTiers.find(
			(t) => t.name.toLowerCase() === selectedPlanForPurchase
		);
		if (!tier) return null;

		// Convert price string to number in pence
		const priceValue = parseFloat(tier.price.replace("£", "")) * 100;

		return {
			name: tier.name,
			tokens: parseInt(tier.checkOraRuns.replace(",", "")),
			price: priceValue,
			description: `Up to ${tier.checkOraRuns} CheckOra Runs or ${tier.inspectOraRuns} InspectOra Runs`,
		};
	};

	return (
		<div
			className={`${
				isDarkMode ? "text-white" : "text-black"
			} min-h-screen mt-20`}>
			<div className="text-center mb-12">
				<h2
					className={`text-4xl font-bold mb-4 ${
						isDarkMode ? "text-white" : "text-gray-900"
					}`}>
					Token Based Pay As You Go Pricing
				</h2>
				<p
					className={`text-lg mb-8 ${
						isDarkMode ? "text-gray-400" : "text-gray-600"
					}`}>
					Purchase a credit bundle once. Spend your credits on runs
					for any AI agent. The more you buy, the better the value.
				</p>
			</div>

			{/* Payment Methods Warning */}
			{paymentMethods.length === 0 && !isFetchingPaymentMethods && (
				<div className="mb-8 p-4 bg-red-500/10 border border-red-500/20 rounded-lg max-w-4xl mx-auto">
					<div className="flex items-start gap-3">
						<div className="text-red-500 text-sm">⚠️</div>
						<div>
							<h4 className="text-red-500 font-medium mb-1">
								No Payment Method Found
							</h4>
							<p
								className={`text-sm ${
									isDarkMode
										? "text-gray-300"
										: "text-gray-700"
								}`}>
								Please add a payment method first to purchase
								tokens.{" "}
								<a
									href="/aep-solutions/platforms?tab=billing&section=payment-methods"
									className="text-blue-500 hover:underline">
									Add payment method
								</a>
							</p>
						</div>
					</div>
				</div>
			)}

			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-12">
				{pricingTiers.map((tier, index) => (
					<div
						key={index}
						className={`relative rounded-2xl p-6 border-2 transition-all hover:scale-105 ${
							tier.isPopular
								? "border-green-500 shadow-lg shadow-green-500/20"
								: isDarkMode
								? "border-gray-700 hover:border-gray-600"
								: "border-gray-200 hover:border-gray-300"
						} ${isDarkMode ? "bg-gray-800" : "bg-white"}`}>
						{tier.isPopular && (
							<div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
								<span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
									Popular
								</span>
							</div>
						)}

						<div className="text-center mb-6">
							<h3
								className={`text-xl font-semibold mb-4 ${
									isDarkMode ? "text-white" : "text-gray-900"
								}`}>
								{tier.name}
							</h3>
							<div className="mb-6">
								<span
									className={`text-3xl font-bold ${
										isDarkMode
											? "text-green-500"
											: "text-green-500"
									}`}>
									{tier.price}
								</span>
								<span
									className={`text-sm ${
										isDarkMode
											? "text-gray-400"
											: "text-gray-600"
									}`}>
									{tier.period}
								</span>
							</div>
						</div>

						<div className="mb-6">
							<ul className="space-y-3 text-sm">
								<li className="flex items-center justify-center gap-2">
									<Check className="w-4 h-4 text-green-500 flex-shrink-0" />
									<span
										className={`${
											isDarkMode
												? "text-gray-300"
												: "text-gray-700"
										}`}>
										Up to{" "}
										<strong>{tier.checkOraRuns}</strong>{" "}
										CheckOra Runs
									</span>
								</li>
								<li
									className={`text-center text-xs ${
										isDarkMode
											? "text-gray-400"
											: "text-gray-500"
									}`}>
									OR
								</li>
								<li className="flex items-center justify-center gap-2">
									<Check className="w-4 h-4 text-green-500 flex-shrink-0" />
									<span
										className={`${
											isDarkMode
												? "text-gray-300"
												: "text-gray-700"
										}`}>
										Up to{" "}
										<strong>{tier.inspectOraRuns}</strong>{" "}
										InspectOra Runs
									</span>
								</li>
								<li
									className={`text-center text-xs italic mt-2 ${
										isDarkMode
											? "text-gray-400"
											: "text-gray-500"
									}`}>
									Mix & match as needed.
								</li>
							</ul>
						</div>

						<Button
							className={`w-full transition-all duration-300 ${
								tier.isPopular
									? "bg-green-500 text-white hover:bg-green-600 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30"
									: "bg-transparent border border-green-500 text-green-500 hover:bg-green-500 hover:text-white"
							} ${
								isButtonDisabled(tier.name)
									? "opacity-50 cursor-not-allowed"
									: ""
							}`}
							onClick={() => handlePurchase(index)}
							disabled={isButtonDisabled(tier.name)}>
							{getButtonText(tier.name)}
						</Button>
					</div>
				))}
			</div>

			{/* Pricing Notice */}
			{/* <div className="max-w-4xl mx-auto mb-12">
				<div className="flex items-start gap-3 p-4 bg-slate-800 border border-slate-700 rounded-lg">
					<div className="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5">
						ℹ️
					</div>
					<p
						className={`text-sm ${
							isDarkMode ? "text-gray-300" : "text-gray-700"
						}`}>
						<strong>Please Note:</strong> Credits are valid for a
						30-day period of time. After a 30-day period, the
						credits will expire and new credits will need to be
						purchased.
					</p>
				</div>
			</div> */}

			{/* Purchase Confirmation Modal */}
			{selectedPlanForPurchase && (
				<PurchaseConfirmationModal
					isOpen={isPurchaseConfirmationOpen}
					onClose={() => {
						setIsPurchaseConfirmationOpen(false);
						setSelectedPlanForPurchase(null);
					}}
					onConfirm={handleConfirmPurchase}
					planDetails={getPlanDetailsForConfirmation()!}
					paymentMethods={paymentMethods}
					isProcessing={isPlanLoading(selectedPlanForPurchase)}
					isDarkMode={isDarkMode}
				/>
			)}

			{/* FAQ Section */}
			<div className="mb-12 mt-30">
				<div className="text-center mb-8">
					<h3
						className={`text-3xl font-bold mb-4 ${
							isDarkMode ? "text-white" : "text-gray-900"
						}`}>
						Frequently Asked Questions
					</h3>
					<p
						className={`text-lg ${
							isDarkMode ? "text-gray-400" : "text-gray-600"
						}`}>
						Got questions? We&apos;ve got answers.
					</p>
				</div>

				<div className="max-w-3xl mx-auto space-y-4">
					{FAQ_DATA.map((faq, index) => (
						<div
							key={index}
							className={`rounded-lg border ${
								isDarkMode
									? "border-gray-700 bg-gray-800"
									: "border-gray-200 bg-white"
							} overflow-hidden`}>
							<button
								onClick={() => toggleFaq(index)}
								className={`w-full px-6 py-4 text-left flex items-center justify-between hover:bg-opacity-80 ${
									isDarkMode
										? "hover:bg-gray-700"
										: "hover:bg-gray-50"
								}`}>
								<span
									className={`font-semibold ${
										isDarkMode
											? "text-white"
											: "text-gray-900"
									}`}>
									{faq.question}
								</span>
								<div
									className={`transform transition-transform ${
										openFaqIndex === index
											? "rotate-180"
											: ""
									}`}>
									<svg
										width="20"
										height="20"
										viewBox="0 0 20 20"
										fill="currentColor">
										<path
											fillRule="evenodd"
											d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
											clipRule="evenodd"
										/>
									</svg>
								</div>
							</button>
							{openFaqIndex === index && (
								<div
									className={`px-6 pb-4 ${
										isDarkMode
											? "text-gray-300"
											: "text-gray-700"
									}`}>
									{faq.answer}
								</div>
							)}
						</div>
					))}
				</div>
			</div>

			{/* Support Section */}
			<div
				className={`rounded-2xl p-8 text-center ${
					isDarkMode
						? "bg-gray-800 border border-gray-700"
						: "bg-gray-50 border border-gray-200"
				}`}>
				<Headphones size={48} className="mx-auto mb-4 text-green-500" />
				<h3
					className={`text-2xl font-bold mb-2 ${
						isDarkMode ? "text-white" : "text-gray-900"
					}`}>
					Still Have Questions?
				</h3>
				<p
					className={`text-lg ${
						isDarkMode ? "text-gray-400" : "text-gray-600"
					}`}>
					Our support team is here to help you choose the right plan
					and get started.
				</p>
				<div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
					<Button className="bg-gradient-to-r from-green-500 to-green-600 text-white font-bold rounded-full px-6 py-3 hover:from-green-600 hover:to-green-700 transition-all">
						Contact Support
					</Button>
					<Button
						variant="outline"
						className={`px-6 py-3 rounded-full font-bold border-2 transition-all ${
							isDarkMode
								? "border-gray-600 text-white hover:border-gray-500"
								: "border-gray-300 text-gray-900 hover:border-gray-400"
						}`}>
						Schedule a Demo
					</Button>
				</div>
			</div>
		</div>
	);
};

export default PricingTab;
