import React from "react";

interface IconProps {
	className?: string;
	size?: number;
}

export const ChromeExtensionIcon: React.FC<IconProps> = ({
	className = "",
	size = 24,
}) => (
	<svg
		width={size}
		height={size}
		viewBox="0 0 64 64"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		className={className}>
		<circle
			cx="32"
			cy="32"
			r="30"
			stroke="currentColor"
			strokeWidth="4"
			fill="none"
		/>
		<circle
			cx="32"
			cy="32"
			r="18"
			stroke="currentColor"
			strokeWidth="3"
			fill="none"
		/>
		<circle cx="32" cy="32" r="6" fill="currentColor" />
		<path
			d="M32 14 L32 2"
			stroke="currentColor"
			strokeWidth="3"
			strokeLinecap="round"
		/>
		<path
			d="M32 62 L32 50"
			stroke="currentColor"
			strokeWidth="3"
			strokeLinecap="round"
		/>
		<path
			d="M50 32 L62 32"
			stroke="currentColor"
			strokeWidth="3"
			strokeLinecap="round"
		/>
		<path
			d="M2 32 L14 32"
			stroke="currentColor"
			strokeWidth="3"
			strokeLinecap="round"
		/>
		<path
			d="M46.67 17.33 L54.04 9.96"
			stroke="currentColor"
			strokeWidth="3"
			strokeLinecap="round"
		/>
		<path
			d="M9.96 54.04 L17.33 46.67"
			stroke="currentColor"
			strokeWidth="3"
			strokeLinecap="round"
		/>
		<path
			d="M46.67 46.67 L54.04 54.04"
			stroke="currentColor"
			strokeWidth="3"
			strokeLinecap="round"
		/>
		<path
			d="M9.96 9.96 L17.33 17.33"
			stroke="currentColor"
			strokeWidth="3"
			strokeLinecap="round"
		/>
	</svg>
);

export const AIWorkforceIcon: React.FC<IconProps> = ({
	className = "",
	size = 24,
}) => (
	<svg
		width={size}
		height={size}
		viewBox="0 0 64 64"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		className={className}>
		{/* Person 1 */}
		<circle
			cx="20"
			cy="18"
			r="8"
			stroke="currentColor"
			strokeWidth="3"
			fill="none"
		/>
		<path
			d="M8 48 C8 38 13 32 20 32 C27 32 32 38 32 48"
			stroke="currentColor"
			strokeWidth="3"
			fill="none"
		/>

		{/* Person 2 */}
		<circle
			cx="44"
			cy="18"
			r="8"
			stroke="currentColor"
			strokeWidth="3"
			fill="none"
		/>
		<path
			d="M32 48 C32 38 37 32 44 32 C51 32 56 38 56 48"
			stroke="currentColor"
			strokeWidth="3"
			fill="none"
		/>

		{/* AI Gear in center */}
		<circle
			cx="32"
			cy="42"
			r="8"
			stroke="currentColor"
			strokeWidth="3"
			fill="none"
		/>
		<circle cx="32" cy="42" r="3" fill="currentColor" />
		<path
			d="M32 30 L32 34"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
		/>
		<path
			d="M32 54 L32 50"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
		/>
		<path
			d="M44 42 L40 42"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
		/>
		<path
			d="M20 42 L24 42"
			stroke="currentColor"
			strokeWidth="2"
			strokeLinecap="round"
		/>
	</svg>
);

export const MarketplaceIcon: React.FC<IconProps> = ({
	className = "",
	size = 24,
}) => (
	<svg
		width={size}
		height={size}
		viewBox="0 0 64 64"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		className={className}>
		{/* Store roof */}
		<path
			d="M8 24 L32 8 L56 24 L56 28 L8 28 Z"
			stroke="currentColor"
			strokeWidth="3"
			fill="none"
		/>

		{/* Store body */}
		<rect
			x="12"
			y="28"
			width="40"
			height="28"
			stroke="currentColor"
			strokeWidth="3"
			fill="none"
		/>

		{/* Store door */}
		<rect
			x="26"
			y="40"
			width="12"
			height="16"
			stroke="currentColor"
			strokeWidth="2"
			fill="none"
		/>
		<circle cx="35" cy="48" r="1" fill="currentColor" />

		{/* Store windows */}
		<rect
			x="16"
			y="34"
			width="8"
			height="8"
			stroke="currentColor"
			strokeWidth="2"
			fill="none"
		/>
		<rect
			x="40"
			y="34"
			width="8"
			height="8"
			stroke="currentColor"
			strokeWidth="2"
			fill="none"
		/>

		{/* Store sign */}
		<rect
			x="20"
			y="18"
			width="24"
			height="6"
			stroke="currentColor"
			strokeWidth="2"
			fill="currentColor"
		/>
	</svg>
);
