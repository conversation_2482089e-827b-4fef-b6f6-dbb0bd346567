// src/components/ui/Input.tsx
import React from "react";
import { cn } from "@/lib/utils";
import { InputProps } from "@/types/extension";

interface InputComponentProps extends InputProps {
	isDarkMode?: boolean;
}

const Input: React.FC<InputComponentProps> = ({
	placeholder,
	value,
	onChange,
	icon,
	className,
	isDarkMode = true,
	...props // Spread remaining props to pass through to input
}) => {
	const baseClasses =
		"border rounded-lg py-2 text-sm focus:outline-none transition-colors";
	const darkClasses =
		"bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500";
	const lightClasses =
		"bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500";

	return (
		<div className="relative">
			{icon && (
				<div
					className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${
						isDarkMode ? "text-gray-400" : "text-gray-500"
					}`}>
					{icon}
				</div>
			)}
			<input
				placeholder={placeholder}
				value={value}
				onChange={onChange} // Pass the full event object
				className={cn(
					baseClasses,
					isDarkMode ? darkClasses : lightClasses,
					icon ? "pl-10 pr-4" : "px-4",
					className
				)}
				{...props} // Spread remaining props (including type, name, id, etc.)
			/>
		</div>
	);
};

export default Input;
