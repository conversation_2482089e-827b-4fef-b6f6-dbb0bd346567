"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { AuthDialog } from "@/components/auth/auth-dialog";
import { AuthDrawer } from "@/components/auth/auth-drawer";
import { useAuth } from "@/hooks/use-auth";
import Link from "next/link";
import { LayoutDashboard } from "lucide-react";

interface CreateAccountButtonProps {
	/** Button text - defaults to "Create Free Account" */
	children?: React.ReactNode;
	/** Button size */
	size?: "sm" | "lg" | "default";
	/** Additional CSS classes */
	className?: string;
	/** Button variant */
	variant?: "default" | "outline" | "ghost";
	/** Custom redirect path for authenticated users */
	authenticatedRedirect?: string;
	/** Whether to show as full width button */
	fullWidth?: boolean;
}

export const CreateAccountButton: React.FC<CreateAccountButtonProps> = ({
	size = "lg",
	className = "",
	variant = "default",
	authenticatedRedirect = "/aep-solutions/platforms",
	fullWidth = false,
}) => {
	const { isAuthenticated } = useAuth();
	const [authDialogOpen, setAuthDialogOpen] = useState(false);
	const [authDrawerOpen, setAuthDrawerOpen] = useState(false);
	const [authMode, setAuthMode] = useState<"login" | "signup">("signup");

	const handleClick = () => {
		if (isAuthenticated) {
			window.location.href = authenticatedRedirect;
		} else {
			const isMobile = window.innerWidth < 768;

			if (isMobile) {
				setAuthMode("signup");
				setAuthDrawerOpen(true);
			} else {
				setAuthMode("signup");
				setAuthDialogOpen(true);
			}
		}
	};

	const baseClasses =
		variant === "default"
			? "bg-green-500 text-slate-50 hover:bg-green-600 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30 transition-all duration-300"
			: "";

	const buttonClasses = `${baseClasses} ${
		fullWidth ? "w-full" : ""
	} ${className}`;

	return (
		<>
			{isAuthenticated ? (
				<Link href={authenticatedRedirect}>
					<Button
						size={size}
						variant={variant}
						className={buttonClasses}>
						<LayoutDashboard className="h-4 w-4 mr-2" />
						Go to AEP
					</Button>
				</Link>
			) : (
				<Button
					onClick={handleClick}
					size={size}
					variant={variant}
					className={`${buttonClasses} cursor-pointer`}>
					Create Free Account
				</Button>
			)}

			<AuthDialog
				isOpen={authDialogOpen}
				onClose={() => setAuthDialogOpen(false)}
				mode={authMode}
				onSwitchMode={setAuthMode}
			/>

			<AuthDrawer
				isOpen={authDrawerOpen}
				onClose={() => setAuthDrawerOpen(false)}
				mode={authMode}
				onSwitchMode={setAuthMode}
			/>
		</>
	);
};
