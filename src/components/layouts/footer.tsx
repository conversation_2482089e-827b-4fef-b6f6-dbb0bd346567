"use client";

import React from "react";
import { Mail } from "lucide-react";
import type { FooterSection, SocialLink } from "@/types";
import Image from "next/image";
import Link from "next/link";

const footerSections: FooterSection[] = [
	{
		title: "Product",
		links: [
			{ name: "Features", href: "#features" },
			{ name: "Demo", href: "#demo" },
		],
	},
	{
		title: "Support",
		links: [{ name: "Contact Support", href: "/contact" }],
	},
];

const socialLinks: SocialLink[] = [
	{
		name: "Email",
		href: "mailto:<EMAIL>",
		icon: <Mail className="w-5 h-5" />,
		text: "<EMAIL>",
	},
];

export const Footer: React.FC = () => {
	return (
		<footer className="bg-slate-800 border-t border-slate-700">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
				{/* Top Section */}
				<div className="flex flex-col lg:flex-row  gap-12 mb-12">
					{/* Brand */}
					<div className="flex-1 max-w-md">
						<div className="flex items-center gap-3 mb-4">
							<Image
								src="/logos/logo-green.svg"
								alt="ClickBuy logo"
								width={40}
								height={40}
								className="w-10 h-auto sm:w-12"
								priority
							/>
							<h3 className="text-2xl font-bold text-green-500">
								ClickBuy
							</h3>
						</div>
						<p className="text-slate-400 mb-6 leading-relaxed">
							The world&apos;s first Agentic E-commerce Platform
							(AEP). Transform your business with AI agents that
							work 24/7 to cut costs, find better deals, and scale
							your sales.
						</p>

						{/* Social links */}
						<div className="flex flex-wrap items-center gap-4">
							{socialLinks.map((social) => (
								<a
									key={social.name}
									href={social.href}
									className="flex items-center gap-2 text-slate-400 hover:text-green-500 transition-colors duration-200"
									aria-label={social.name}>
									{social.icon}
									{social.text && (
										<span className="text-sm">
											{social.text}
										</span>
									)}
								</a>
							))}
						</div>
					</div>

					{/* Footer Links */}
					<div className="flex flex-wrap gap-12">
						{footerSections.map((section) => (
							<div key={section.title} className="min-w-[120px]">
								<h4 className="text-lg font-semibold text-slate-50 mb-4">
									{section.title}
								</h4>
								<ul className="space-y-3">
									{section.links.map((link) => (
										<li key={link.name}>
											{link.name === "Contact Support" ? (
												<Link
													href={link.href}
													className="text-slate-400 hover:text-green-500 transition-colors duration-200">
													{link.name}
												</Link>
											) : (
												<a
													href={link.href}
													target="_blank"
													className="text-slate-400 hover:text-green-500 transition-colors duration-200">
													{link.name}
												</a>
											)}
										</li>
									))}
								</ul>
							</div>
						))}
					</div>
				</div>

				{/* Bottom Section */}
				<div className="pt-8 border-t border-slate-700 flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
					<p className="text-slate-400 text-sm">
						© 2025 ClickBuy. All rights reserved.
					</p>
					<div className="flex space-x-8 text-sm text-slate-400">
						<Link
							href="/legal?showPrivacy=1"
							className="text-slate-400 hover:text-green-400 transition-colors duration-300 text-sm font-medium">
							Privacy Policy
						</Link>
						<Link
							href="/legal?showTerms=1"
							className="text-slate-400 hover:text-green-400 transition-colors duration-300 text-sm font-medium">
							Terms of Service
						</Link>
					</div>
				</div>
			</div>
		</footer>
	);
};
