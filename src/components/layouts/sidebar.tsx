// src/components/layout/Sidebar.tsx
import React, { useState, useEffect } from "react";
import Image from "next/image";
import {
	ChevronLeft,
	ChevronRight,
	X,
	ChevronDown,
	ChevronUp,
} from "lucide-react";
import { SidebarItemProps, SidebarSection } from "@/types/extension";
import { COMPANY_INFO } from "@/constants";

interface SidebarItemComponentProps extends SidebarItemProps {
	isCollapsed: boolean;
	isDarkMode: boolean;
}

interface DropdownSectionProps {
	title: string;
	items: SidebarItemProps[];
	isCollapsed: boolean;
	isDarkMode: boolean;
	isOpen: boolean;
	onToggle: () => void;
}

const SidebarItem: React.FC<SidebarItemComponentProps> = ({
	icon: Icon,
	label,
	active,
	badge,
	onClick,
	isCollapsed,
	isDarkMode,
}) => {
	return (
		<div
			className={`flex items-center rounded-lg cursor-pointer transition-all duration-200 relative group ${
				isCollapsed ? "justify-center px-2 py-3" : "gap-3 px-4 py-2"
			} ${
				active
					? "bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] hover:from-[#12C2E9] hover:to-[#19D86C] hover:scale-105 active:scale-95 font-semibold shadow-sm"
					: isDarkMode
					? "text-gray-400 hover:text-white hover:bg-gray-800"
					: "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
			}`}
			onClick={onClick}>
			<div
				className={`flex items-center justify-center ${
					isCollapsed ? "w-6 h-6" : ""
				}`}>
				{Icon && (
					<div
						className={`flex items-center justify-center ${
							isCollapsed ? "w-6 h-6" : ""
						}`}>
						<Icon size={18} />
					</div>
				)}
			</div>

			{!isCollapsed && (
				<>
					<span className="text-sm font-medium">{label}</span>
					{badge && (
						<span
							className={`ml-auto text-xs px-2 py-1 rounded-full font-medium ${
								active
									? "bg-white/20 text-[#111215]"
									: isDarkMode
									? "bg-gray-700 text-gray-300"
									: "bg-gray-200 text-gray-600"
							}`}>
							{badge}
						</span>
					)}
				</>
			)}

			{/* Tooltip for collapsed state */}
			{isCollapsed && (
				<div
					className={`absolute left-full ml-3 px-3 py-2 border text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50 ${
						isDarkMode
							? "bg-gray-800 border-gray-600 text-white"
							: "bg-white border-gray-300 text-gray-900 shadow-xl"
					}`}>
					{label}
					{badge && (
						<span className="ml-1 text-[#19D86C]">({badge})</span>
					)}
				</div>
			)}
		</div>
	);
};

const DropdownSection: React.FC<DropdownSectionProps> = ({
	title,
	items,
	isCollapsed,
	isDarkMode,
	isOpen,
	onToggle,
}) => {
	const mutedTextColor = isDarkMode ? "text-gray-400" : "text-gray-600";

	if (isCollapsed) {
		// Show all items without dropdown when collapsed
		return (
			<div className="space-y-1">
				{items.map((item) => (
					<SidebarItem
						key={item.label}
						icon={item.icon}
						label={item.label}
						active={item.active}
						badge={item.badge}
						onClick={item.onClick}
						isCollapsed={isCollapsed}
						isDarkMode={isDarkMode}
					/>
				))}
			</div>
		);
	}

	return (
		<div>
			{/* Dropdown Header */}
			<button
				onClick={onToggle}
				className={`w-full flex items-center justify-between px-2 py-2 text-xs ${mutedTextColor} uppercase tracking-wider font-semibold hover:text-[#19D86C] transition-colors`}>
				<span>{title}</span>
				{isOpen ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
			</button>

			{/* Dropdown Items */}
			{isOpen && (
				<div className="space-y-1 mt-2">
					{items.map((item) => (
						<SidebarItem
							key={item.label}
							icon={item.icon}
							label={item.label}
							active={item.active}
							badge={item.badge}
							onClick={item.onClick}
							isCollapsed={isCollapsed}
							isDarkMode={isDarkMode}
						/>
					))}
				</div>
			)}
		</div>
	);
};

interface SidebarProps {
	isCollapsed?: boolean;
	onToggleCollapse?: () => void;
	isDarkMode?: boolean;
	isMobileMenuOpen?: boolean;
	onCloseMobileMenu?: () => void;
	customSections?: SidebarSection[];
	showUserSection?: boolean;
	showFooter?: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({
	isCollapsed = false,
	onToggleCollapse,
	isDarkMode = true,
	isMobileMenuOpen = false,
	onCloseMobileMenu,
	customSections,
	showFooter = true,
}) => {
	const [openDropdowns, setOpenDropdowns] = useState<Record<string, boolean>>(
		{
			General: true,
			"AI Deal Sourcing": false,
			"Digital Workforce": false,
			"Cross Selling": false,
			"Systemise Fulfilment": false,
		}
	);

	// Handle URL parameters to open appropriate dropdowns
	useEffect(() => {
		if (typeof window !== "undefined") {
			const urlParams = new URLSearchParams(window.location.search);
			const section = urlParams.get("section");

			if (section) {
				setOpenDropdowns((prev) => ({
					...prev,
					[section]: true,
				}));
			}
		}
	}, []);

	const bgColor = isDarkMode ? "bg-black" : "bg-white";
	const borderColor = isDarkMode ? "border-gray-800" : "border-gray-200";
	const mutedTextColor = isDarkMode ? "text-gray-400" : "text-gray-600";
	const logoTextColor = isDarkMode ? "text-white" : "text-gray-900";

	const toggleDropdown = (sectionTitle: string) => {
		setOpenDropdowns((prev) => ({
			...prev,
			[sectionTitle]: !prev[sectionTitle],
		}));
	};

	// Use custom sections if provided, otherwise use default sections
	const sectionsToRender = customSections || [];

	// Group sections by dropdown categories
	const dropdownSections = [
		{
			title: "General",
			items:
				sectionsToRender.find((section) => section.title === "General")
					?.items || [],
		},
		{
			title: "AI Deal Sourcing",
			items:
				sectionsToRender.find(
					(section) => section.title === "AI Deal Sourcing"
				)?.items || [],
		},
		{
			title: "Digital Workforce",
			items:
				sectionsToRender.find(
					(section) => section.title === "Digital Workforce"
				)?.items || [],
		},
		{
			title: "Cross Selling",
			items:
				sectionsToRender.find(
					(section) => section.title === "Cross Selling"
				)?.items || [],
		},
		{
			title: "Systemise Fulfilment",
			items:
				sectionsToRender.find(
					(section) => section.title === "Systemise Fulfilment"
				)?.items || [],
		},
	];

	return (
		<>
			{/* Mobile Overlay */}
			{isMobileMenuOpen && (
				<div
					className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
					onClick={onCloseMobileMenu}
				/>
			)}

			{/* Sidebar */}
			<div
				className={`
        ${isCollapsed ? "w-16" : "w-64"}
        ${bgColor} border-r ${borderColor}
        flex flex-col transition-all duration-300

        /* Mobile styles */
        lg:relative lg:translate-x-0
        ${
			isMobileMenuOpen
				? "fixed inset-y-0 left-0 z-50 translate-x-0"
				: "fixed inset-y-0 left-0 z-50 -translate-x-full lg:translate-x-0"
		}
      `}>
				{/* Logo */}
				<div
					className={`border-b ${borderColor} relative ${
						isCollapsed ? "p-3" : "p-4"
					}`}>
					<div
						className={`flex items-center ${
							isCollapsed ? "justify-center" : "gap-3"
						}`}>
						{/* Logo Image */}
						<div
							className={`${
								isCollapsed ? "w-8 h-8" : "w-10 h-10"
							} relative`}>
							<Image
								src={
									isDarkMode
										? "/logos/logo-w.png"
										: "/logos/logo-b.png"
								}
								alt={`${COMPANY_INFO.name} Logo`}
								width={400}
								height={377}
								className="object-contain"
								priority
							/>
						</div>
						{!isCollapsed && (
							<span
								className={`font-bold text-lg ${logoTextColor}`}>
								{COMPANY_INFO.name}
							</span>
						)}
					</div>

					{/* Collapse Toggle Button - Hide on mobile */}
					{onToggleCollapse && (
						<button
							onClick={onToggleCollapse}
							className={`hidden lg:flex absolute -right-3 top-1/2 transform -translate-y-1/2 w-6 h-6 border rounded-full items-center justify-center transition-colors z-10 ${
								isDarkMode
									? "bg-gray-900 border-gray-700 text-gray-400 hover:text-white hover:bg-gray-800 hover:border-gray-600"
									: "bg-white border-gray-300 text-gray-600 hover:text-gray-900 hover:bg-gray-50 hover:border-gray-400 shadow-sm"
							}`}>
							{isCollapsed ? (
								<ChevronRight size={14} />
							) : (
								<ChevronLeft size={14} />
							)}
						</button>
					)}

					{/* Mobile Close Button */}
					{onCloseMobileMenu && (
						<button
							onClick={onCloseMobileMenu}
							className={`lg:hidden absolute top-4 right-4 w-8 h-8 border rounded-full flex items-center justify-center transition-colors ${
								isDarkMode
									? "bg-gray-900 border-gray-700 text-gray-400 hover:text-white hover:bg-gray-800"
									: "bg-white border-gray-300 text-gray-600 hover:text-gray-900 hover:bg-gray-50"
							}`}>
							<X size={16} />
						</button>
					)}
				</div>

				{/* Navigation with Dropdowns */}
				<div className="flex-1 p-3 overflow-y-auto">
					<div className="space-y-4">
						{dropdownSections.map((section, index) => (
							<div key={section.title}>
								<DropdownSection
									title={section.title}
									items={section.items}
									isCollapsed={isCollapsed}
									isDarkMode={isDarkMode}
									isOpen={openDropdowns[section.title]}
									onToggle={() =>
										toggleDropdown(section.title)
									}
								/>

								{/* Horizontal line between sections (except last one) */}
								{index < dropdownSections.length - 1 &&
									!isCollapsed && (
										<div
											className={`border-t ${borderColor} my-4`}
										/>
									)}
							</div>
						))}
					</div>
				</div>

				{/* Footer - Hide when collapsed or on mobile or when showFooter is false */}
				{!isCollapsed && showFooter && (
					<div
						className={`p-4 border-t ${borderColor} hidden lg:block`}>
						<div className={`text-xs ${mutedTextColor} space-y-1`}>
							<div className="font-medium">
								Powered by {COMPANY_INFO.poweredBy}
							</div>
							<div>{COMPANY_INFO.copyright}</div>
						</div>
					</div>
				)}
			</div>
		</>
	);
};

export default Sidebar;
