"use client";

import React, { useState, useEffect, useImper<PERSON><PERSON><PERSON><PERSON>, forwardRef } from "react";
import { Bo<PERSON>, Plus } from "lucide-react";
import { DigitalWorkforceAPI, AgentResponse } from "@/lib/api/digital-workforce-api";
import EmptyPageComponent from "../empty-page";
import { Button } from "../ui/button";

interface AIAgentsProps {
	isDarkMode: boolean;
	onCreateAgent: () => void;
}

export interface AIAgentsRef {
	refresh: () => void;
}

const AIAgents = forwardRef<AIAgentsRef, AIAgentsProps>(({
	isDarkMode,
	onCreateAgent,
}, ref) => {
	const [agents, setAgents] = useState<AgentResponse[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string>("");

	useEffect(() => {
		loadAgents();
	}, []);

	const loadAgents = async () => {
		setLoading(true);
		setError("");
		try {
			const agentList = await DigitalWorkforceAPI.getAgents({ limit: 50 });
			setAgents(agentList);
		} catch {
			setError("Unable to load agents. Please try again later.");
		} finally {
			setLoading(false);
		}
	};

	const handleSearchAgents = (searchValue: string) => {
		if (!searchValue.trim()) {
			loadAgents();
			return;
		}

		const filtered = agents.filter(agent =>
			agent.agentName.toLowerCase().includes(searchValue.toLowerCase()) ||
			agent.specialization.toLowerCase().includes(searchValue.toLowerCase()) ||
			agent.problemDescription.toLowerCase().includes(searchValue.toLowerCase())
		);
		setAgents(filtered);
	};

	useImperativeHandle(ref, () => ({
		refresh: loadAgents
	}));

	return (
		<div className={`min-h-screen p-10 ${
            isDarkMode
                ? "bg-gradient-to-br from-slate-900 to-slate-800"
                : "bg-gradient-to-br from-gray-50 to-gray-100"
        }`}>
			{/* Error Display */}
			{error && (
				<div className="mb-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
					<p className="text-red-400 text-sm">{error}</p>
				</div>
			)}

			{/* Show agents or empty state */}
			{agents.length > 0 ? (
				<div className="space-y-4">
					{/* Header */}
					<div className="flex items-center justify-between">
						<div>
							<h2 className={`text-2xl font-bold ${isDarkMode ? "text-white" : "text-gray-900"}`}>
								Suggested AI Agents ({agents.length})
							</h2>
							<p className={`text-sm ${isDarkMode ? "text-gray-400" : "text-gray-600"}`}>
								Manage your intelligent automation agents
							</p>
						</div>
						<Button
							onClick={onCreateAgent}
							className="flex items-center gap-2 px-4 py-2 bg-[#10A37F] text-white rounded-lg hover:bg-[#0d8f5f] transition-colors">
							<Plus className="w-4 h-4" />
							Suggest AI Agent
						</Button>
					</div>

					{/* Search
					<div className="relative">
						<input
							type="text"
							placeholder="Search agents..."
							onChange={(e) => handleSearchAgents(e.target.value)}
							className={`w-full px-4 py-2 rounded-lg border ${
								isDarkMode
									? "bg-gray-800 border-gray-700 text-white placeholder-gray-400"
									: "bg-white border-gray-300 text-black placeholder-gray-500"
							}`}
						/>
					</div> */}

					{/* Agents Grid */}
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-16">
						{agents.map((agent) => (
							<div
								key={agent.id}
								className={`p-4 rounded-lg border ${
									isDarkMode
										? "bg-gray-800 border-gray-700"
										: "bg-white border-gray-200"
								} hover:border-[#10A37F]/50 transition-colors cursor-pointer`}>
								<div className="flex items-start justify-between mb-3">
									<div className="flex items-center gap-2">
										<Bot className="w-5 h-5 text-[#10A37F]" />
										<h3 className={`font-medium ${isDarkMode ? "text-white" : "text-gray-900"}`}>
											{agent.agentName}
										</h3>
									</div>
									<span className={`px-2 py-1 text-xs rounded-full ${
										agent.specialization === "pricing" ? "bg-green-500/10 text-green-400" :
										agent.specialization === "sourcing" ? "bg-blue-500/10 text-blue-400" :
										"bg-purple-500/10 text-purple-400"
									}`}>
										{agent.specialization}
									</span>
								</div>
								<p className={`text-sm mb-3 ${isDarkMode ? "text-gray-400" : "text-gray-600"}`}>
									{agent.problemDescription.slice(0, 100)}
									{agent.problemDescription.length > 100 && "..."}
								</p>
								<div className="flex items-center justify-between text-xs">
									<span className={`${isDarkMode ? "text-gray-500" : "text-gray-400"}`}>
										Created {new Date(agent.createdAt).toLocaleDateString()}
									</span>
									<span className={`px-2 py-1 rounded-full ${
										agent.status === "active" ? "bg-green-500/10 text-green-400" : "bg-gray-500/10 text-gray-400"
									}`}>
										{agent.status}
									</span>
								</div>
							</div>
						))}
					</div>
				</div>
			) : loading ? (
				<div className="flex items-center justify-center py-12">
					<div className="w-6 h-6 border-2 border-[#10A37F] border-t-transparent rounded-full animate-spin" />
				</div>
			) : (
				<EmptyPageComponent
					isDarkMode={isDarkMode}
					title="AI Agents"
					description="Create and manage your intelligent automation agents"
					icon={<Bot className="w-4 h-4 text-white" />}
					primaryAction={{
						label: "Create Agent",
						onClick: onCreateAgent,
						icon: <Plus className="w-4 h-4" />,
						variant: "primary",
					}}
					showSearch={false}
					showFilters={false}
					showExport={false}
					searchPlaceholder="Search agents..."
					onSearch={handleSearchAgents}
					customEmptyIcon={
						<Bot className="w-12 h-12 text-[#10A37F]" />
					}
					emptyTitle="No AI agents created yet"
					emptyDescription="Create your first AI agent to automate tasks like pricing analysis, product sourcing, or customer service. Your agents will help streamline your workflow and improve efficiency."
					emptyActionLabel="Create Your First Agent"
					onEmptyAction={onCreateAgent}
				/>
			)}
		</div>
	);
});

AIAgents.displayName = "AIAgents";

export default AIAgents;