/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState, useEffect } from "react";
import {
	Sparkles,
	User,
	Target,
	Settings,
	Mail,
	Info,
	ArrowRight,
	Package,
	Shield,
	Calculator,
	ShoppingCart,
	TrendingUp,
	Search,
	AlertCircle,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/hooks/use-auth";
import {
	DigitalWorkforceAPI,
	CreateAgentRequest,
} from "@/lib/api/digital-workforce-api";
import { ApiError } from "@/types/auth";

interface AgentData {
	agentName: string;
	specialization: string;
	problemDescription: string;
	agentSteps: string;
	expectedOutput: string;
	escalationEmail: string;
}

interface CreateAgentProps {
	onSubmit?: (data: AgentData) => void;
	onCancel?: () => void;
	isDarkMode?: boolean;
	user?: any; // Add user prop
}

const CreateAgentComponent: React.FC<CreateAgentProps> = ({
	onSubmit,
	onCancel,
	isDarkMode = true,
}) => {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [selectedSpecialization, setSelectedSpecialization] =
		useState<string>("");
	const [error, setError] = useState<string>("");
	const { user } = useAuth();

	// Defensive check for malformed user object
	const safeUser =
		user &&
		typeof user === "object" &&
		!Array.isArray(user) &&
		!("type" in user)
			? user
			: null;

	const form = useForm<AgentData>({
		defaultValues: {
			agentName: "",
			specialization: "",
			problemDescription: "",
			agentSteps: "",
			expectedOutput: "",
			escalationEmail: safeUser?.email || "",
		},
	});

	useEffect(() => {
		if (safeUser?.email) {
			form.setValue("escalationEmail", safeUser.email);
		}
	}, [safeUser?.email, form]);

	const specializations = [
		{
			id: "deal_sourcing",
			title: "Deal Sourcing",
			description:
				"Identifies and evaluates profitable business opportunities and deals",
			icon: Search,
			color: "bg-green-500",
			features: [
				"Opportunity identification",
				"Deal analysis",
				"Market research",
				"Profit assessment",
			],
		},
		{
			id: "cross_selling",
			title: "Cross Selling",
			description:
				"Maximizes revenue through strategic product recommendations",
			icon: TrendingUp,
			color: "bg-blue-500",
			features: [
				"Product recommendations",
				"Customer analytics",
				"Sales optimization",
				"Revenue growth",
			],
		},
		{
			id: "procurement",
			title: "Procurement",
			description:
				"An AI-powered procurement assistant to support purchasing decisions, manage fulfilment, and automate replenishment tasks.",
			icon: ShoppingCart,
			color: "bg-purple-500",
			features: [
				"Vendor management",
				"Purchase optimization",
				"Contract negotiation",
				"Cost reduction",
			],
		},
		{
			id: "inventory",
			title: "Inventory",
			description:
				"AI tools to assist with repricing, restocking, and inventory management across multiple suppliers and channels",
			icon: Package,
			color: "bg-orange-500",
			features: [
				"Stock monitoring",
				"Demand forecasting",
				"Reorder management",
				"Warehouse optimization",
			],
		},
		{
			id: "compliance_customer_service",
			title: "Compliance & Customer Service",
			description:
				"AI-assisted case handling, policy monitoring, and intelligent responses to customer queries across your marketplaces.",
			icon: Shield,
			color: "bg-red-500",
			features: [
				"Regulatory compliance",
				"Customer support",
				"Quality assurance",
				"Risk management",
			],
		},
		{
			id: "finance",
			title: "Finance",
			description:
				"AI-driven financial automation for tracking performance, generating reports, and delivering real-time ROI and profitability insights",
			icon: Calculator,
			color: "bg-indigo-500",
			features: [
				"Financial analysis",
				"Budget management",
				"Cash flow monitoring",
				"Financial reporting",
			],
		},
	];


	const handleSubmit = async (data: AgentData) => {
		setError("");

		setIsSubmitting(true);

		try {
			// Create agent via API directly - no frontend validation needed
			const agent = await DigitalWorkforceAPI.createAgent(
				data as CreateAgentRequest
			);

			// Store success data
			localStorage.setItem("lastCreatedAgent", JSON.stringify(agent));

			if (onSubmit) {
				onSubmit(data);
			}
		} catch (error) {
			const apiError = error as ApiError;
			setError(
				apiError.detail || "Failed to create agent. Please try again."
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleSpecializationSelect = (id: string) => {
		setSelectedSpecialization(id);
		form.setValue("specialization", id);
	};

	// Clear API error when user starts typing
	const clearError = () => {
		if (error) {
			setError("");
		}
	};

	return (
		<div
			className={`${isDarkMode ? "bg-gray-900" : "bg-white"} border ${
				isDarkMode ? "border-gray-700" : "border-gray-200"
			} rounded-lg overflow-hidden`}>
			<div className="p-6 border-b border-gray-700">
				<div className="flex items-center justify-between">
					<div>
						<h3
							className={`text-xl font-bold ${
								isDarkMode ? "text-white" : "text-gray-900"
							}`}>
							Suggest an AI Agent to Build
						</h3>
						<p
							className={`text-sm mt-1 ${
								isDarkMode ? "text-gray-400" : "text-gray-600"
							}`}>
							You request it. We research it. We build what&apos;s in
							demand. You only pay when you use it
						</p>
					</div>
				</div>
			</div>

			<div className="p-6">
				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(handleSubmit)}
						className="space-y-8">
						{/* API Error Display */}
						{error && (
							<div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
								<div className="flex items-start gap-3">
									<AlertCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
									<p className="text-red-400 text-sm">
										{error}
									</p>
								</div>
							</div>
						)}

						{/* Agent Identity Section */}
						<div className="space-y-6">
							<div className="flex items-center gap-3">
								<div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
									<User className="w-4 h-4 text-white" />
								</div>
								<div>
									<h3
										className={`font-semibold ${
											isDarkMode
												? "text-white"
												: "text-gray-900"
										}`}>
										Agent Identity
									</h3>
									<p
										className={`text-sm ${
											isDarkMode
												? "text-gray-400"
												: "text-gray-600"
										}`}>
										Give your agent a name and purpose
									</p>
								</div>
							</div>

							<FormField
								control={form.control}
								name="agentName"
								rules={{
									required: "Agent name is required",
									minLength: {
										value: 2,
										message:
											"Agent name must be at least 2 characters",
									},
								}}
								render={({ field, fieldState }) => (
									<FormItem>
										<FormLabel
											className={`text-sm font-medium ${
												isDarkMode
													? "text-gray-300"
													: "text-gray-700"
											}`}>
											Agent Name *
										</FormLabel>
										<FormControl>
											<Input
												placeholder="Customer Success Assistant"
												{...field}
												onChange={(e) => {
													field.onChange(e);
													clearError();
												}}
												className={`${
													fieldState.error
														? "border-red-500 focus:border-red-500"
														: ""
												} ${
													isDarkMode
														? "bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:border-[#10A37F]"
														: "bg-white border-gray-300 text-black placeholder-gray-500 focus:border-[#10A37F]"
												}`}
											/>
										</FormControl>
										<FormDescription
											className={`text-xs ${
												isDarkMode
													? "text-gray-400"
													: "text-gray-600"
											}`}>
											This name appears to visitors when
											they interact with your agent
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* Agent Specialization Section */}
						<div className="space-y-6">
							<div className="flex items-center gap-3">
								<div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
									<Target className="w-4 h-4 text-white" />
								</div>
								<div>
									<h3
										className={`font-semibold ${
											isDarkMode
												? "text-white"
												: "text-gray-900"
										}`}>
										AI Agent Department *
									</h3>
									<p
										className={`text-sm ${
											isDarkMode
												? "text-gray-400"
												: "text-gray-600"
										}`}>
										Choose your agent&apos;s area of
										expertise
									</p>
								</div>
							</div>

							<FormField
								control={form.control}
								name="specialization"
								rules={{
									required: "Please select a specialization",
								}}
								render={({ field, fieldState }) => (
									<FormItem>
										<FormControl>
											<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
												{specializations.map((spec) => {
													const IconComponent =
														spec.icon;
													return (
														<button
															key={spec.id}
															type="button"
															onClick={() => {
																handleSpecializationSelect(
																	spec.id
																);
																field.onChange(
																	spec.id
																);
															}}
															className={`p-4 rounded-lg border-2 text-left transition-all relative ${
																selectedSpecialization ===
																spec.id
																	? "border-[#10A37F] bg-[#10A37F]/10"
																	: isDarkMode
																	? "border-gray-700 hover:border-gray-600 bg-gray-800"
																	: "border-gray-200 hover:border-gray-300 bg-white"
															}`}>
															<div className="flex items-center gap-3 mb-3">
																<div
																	className={`w-10 h-10 rounded-full ${spec.color} flex items-center justify-center`}>
																	<IconComponent className="w-5 h-5 text-white" />
																</div>
																<div>
																	<h4
																		className={`font-medium ${
																			isDarkMode
																				? "text-white"
																				: "text-gray-900"
																		}`}>
																		{
																			spec.title
																		}
																	</h4>
																</div>
															</div>
															<p
																className={`text-sm mb-3 ${
																	isDarkMode
																		? "text-gray-400"
																		: "text-gray-600"
																}`}>
																{
																	spec.description
																}
															</p>
															<div className="flex flex-wrap gap-1">
																{spec.features.map(
																	(
																		feature,
																		index
																	) => (
																		<Badge
																			key={
																				index
																			}
																			variant="secondary"
																			className={`text-xs ${
																				isDarkMode
																					? "bg-gray-700 text-gray-300"
																					: "bg-gray-100 text-gray-700"
																			}`}>
																			{
																				feature
																			}
																		</Badge>
																	)
																)}
															</div>
														</button>
													);
												})}
											</div>
										</FormControl>
										{fieldState.error && (
											<p className="text-red-400 text-sm mt-2">
												{fieldState.error.message}
											</p>
										)}
									</FormItem>
								)}
							/>
						</div>

						{/* Agent Configuration Section */}
						<div className="space-y-6">
							<div className="flex items-center gap-3">
								<div className="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center">
									<Settings className="w-4 h-4 text-white" />
								</div>
								<div>
									<h3
										className={`font-semibold ${
											isDarkMode
												? "text-white"
												: "text-gray-900"
										}`}>
										Agent Configuration
									</h3>
									<p
										className={`text-sm ${
											isDarkMode
												? "text-gray-400"
												: "text-gray-600"
										}`}>
										Define how your agent operates
									</p>
								</div>
							</div>

							<div className="grid grid-cols-1 md:grid-cols-1 gap-6">
								<FormField
									control={form.control}
									name="problemDescription"
									rules={{
										required:
											"Problem description is required",
										minLength: {
											value: 2,
											message:
												"Problem description must be at least 2 characters",
										},
									}}
									render={({ field, fieldState }) => (
										<FormItem>
											<FormLabel
												className={`text-sm font-medium ${
													isDarkMode
														? "text-gray-300"
														: "text-gray-700"
												}`}>
												Problem the Agent is Trying to
												Fix *
											</FormLabel>
											<FormControl>
												<Textarea
													placeholder="e.g., Manually checking competitor prices is time-consuming and leads to missed opportunities."
													{...field}
													onChange={(e) => {
														field.onChange(e);
														clearError();
													}}
													rows={3}
													className={`${
														fieldState.error
															? "border-red-500 focus:border-red-500"
															: ""
													} resize-none ${
														isDarkMode
															? "bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:border-[#10A37F]"
															: "bg-white border-gray-300 text-black placeholder-gray-500 focus:border-[#10A37F]"
													}`}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="agentSteps"
									rules={{
										required: "Agent steps are required",
										minLength: {
											value: 2,
											message:
												"Agent steps must be at least 2 characters",
										},
									}}
									render={({ field, fieldState }) => (
										<FormItem>
											<FormLabel
												className={`text-sm font-medium ${
													isDarkMode
														? "text-gray-300"
														: "text-gray-700"
												}`}>
												Steps the agent needs to take /
												Tools (software, website) the
												agent needs to operate within *
											</FormLabel>
											<FormControl>
												<Textarea
													placeholder="List the steps the agent should follow and any software/APIs it needs to use. e.g., 1. Get a list of our product SKUs. 2. Visit competitor websites A, B, and C. 3. Scrape the price for each SKU. 4. Log prices in a Google Sheet."
													{...field}
													onChange={(e) => {
														field.onChange(e);
														clearError();
													}}
													rows={4}
													className={`${
														fieldState.error
															? "border-red-500 focus:border-red-500"
															: ""
													} resize-none ${
														isDarkMode
															? "bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:border-[#10A37F]"
															: "bg-white border-gray-300 text-black placeholder-gray-500 focus:border-[#10A37F]"
													}`}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="expectedOutput"
									rules={{
										required: "Expected output is required",
										minLength: {
											value: 2,
											message:
												"Expected output must be at least 2 characters",
										},
									}}
									render={({ field, fieldState }) => (
										<FormItem>
											<FormLabel
												className={`text-sm font-medium ${
													isDarkMode
														? "text-gray-300"
														: "text-gray-700"
												}`}>
												Output *
											</FormLabel>
											<FormControl>
												<Textarea
													placeholder="Propose goals of what it should be outputting as well as the format of the output."
													{...field}
													onChange={(e) => {
														field.onChange(e);
														clearError();
													}}
													rows={3}
													className={`${
														fieldState.error
															? "border-red-500 focus:border-red-500"
															: ""
													} resize-none ${
														isDarkMode
															? "bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:border-[#10A37F]"
															: "bg-white border-gray-300 text-black placeholder-gray-500 focus:border-[#10A37F]"
													}`}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</div>

						{/* Support Configuration Section */}
						<div className="space-y-6">
							<div className="flex items-center gap-3">
								<div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
									<Mail className="w-4 h-4 text-white" />
								</div>
								<div>
									<h3
										className={`font-semibold ${
											isDarkMode
												? "text-white"
												: "text-gray-900"
										}`}>
										Support Configuration
									</h3>
									<p
										className={`text-sm ${
											isDarkMode
												? "text-gray-400"
												: "text-gray-600"
										}`}>
										Setup escalation and notifications
									</p>
								</div>
							</div>

							<FormField
								control={form.control}
								name="escalationEmail"
								rules={{
									required: "Escalation email is required",
									pattern: {
										value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
										message: "Invalid email address",
									},
								}}
								render={({ field, fieldState }) => (
									<FormItem>
										<FormLabel
											className={`text-sm font-medium ${
												isDarkMode
													? "text-gray-300"
													: "text-gray-700"
											}`}>
											Escalation Email *
										</FormLabel>
										<FormControl>
											<Input
												placeholder="<EMAIL>"
												{...field}
												disabled={true}
												readOnly={true}
												className={`${
													fieldState.error
														? "border-red-500 focus:border-red-500"
														: ""
												} cursor-not-allowed opacity-75 ${
													isDarkMode
														? "bg-gray-800 border-gray-700 text-white placeholder-gray-400"
														: "bg-gray-50 border-gray-300 text-black placeholder-gray-500"
												}`}
											/>
										</FormControl>
										<FormDescription
											className={`text-xs flex items-start gap-2 ${
												isDarkMode
													? "text-gray-400"
													: "text-gray-600"
											}`}>
											<Info className="w-3 h-3 mt-0.5 flex-shrink-0" />
											When your agent encounters complex
											queries, it will notify this email
											for human handoff
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* Submit Section */}
						<div className="flex items-center justify-between pt-6 border-t border-gray-700">
							<div className="flex items-center gap-2">
								{onCancel && (
									<Button
										type="button"
										variant="outline"
										onClick={onCancel}
										disabled={isSubmitting}
										className={`${
											isDarkMode
												? "border-gray-600 text-gray-300 hover:bg-gray-800"
												: "border-gray-300 text-gray-700 hover:bg-gray-50"
										}`}>
										Cancel
									</Button>
								)}
							</div>

							<Button
								type="submit"
								disabled={isSubmitting}
								className="bg-[#10A37F] text-white hover:bg-[#0d8f5f] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed">
								{isSubmitting ? (
									<div className="flex items-center gap-2">
										<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
										Creating Agent...
									</div>
								) : (
									<div className="flex items-center gap-2">
										<Sparkles className="w-4 h-4" />
										Suggest AI Agent
										<ArrowRight className="w-4 h-4" />
									</div>
								)}
							</Button>
						</div>

						<div className="text-center pt-4">
							<p
								className={`text-sm ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								}`}>
								Your agent will be ready in seconds and can be
								embedded on any website
							</p>
						</div>
					</form>
				</Form>
			</div>
		</div>
	);
};

export default CreateAgentComponent;
