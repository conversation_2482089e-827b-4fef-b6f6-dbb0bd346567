/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { Home, Search, Bell, Sun, Moon, Menu } from "lucide-react";
import Button from "@/components/Button";

interface ExtensionsHeaderProps {
	isDarkMode: boolean;
	user: any;
	extensionsData: any;
	onBackToHome: () => void;
	onToggleTheme: () => void;
	onToggleMobileMenu: () => void;
	onLogout?: () => void;
}

const Header: React.FC<ExtensionsHeaderProps> = ({
	isDarkMode,
	user,
	extensionsData,
	onToggleTheme,
	onToggleMobileMenu,
}) => {
	const unreadNotifications =
		extensionsData?.notifications?.filter((n: any) => !n.isRead).length ||
		0;

	// Enhanced handlers
	const handleBackToHome = () => {
		window.location.href = "/";
	};

	return (
		<div
			className={`border-b transition-all duration-300 ${
				isDarkMode
					? "bg-black border-gray-800 shadow-lg"
					: "bg-white border-gray-200 shadow-sm"
			}`}>
			{/* Mobile Header */}
			<div className="block lg:hidden">
				<div className="flex items-center justify-between px-4 py-3">
					<div className="flex items-center gap-3">
						<Button
							variant="ghost"
							size="sm"
							onClick={onToggleMobileMenu}
							className={`p-2 transition-colors ${
								isDarkMode
									? "text-gray-300 hover:text-white hover:bg-gray-800"
									: "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
							}`}>
							<Menu size={20} />
						</Button>
						<div>
							<div
								className={`font-semibold text-base ${
									isDarkMode ? "text-white" : "text-gray-900"
								}`}>
								Extensions Portal
							</div>
							<div
								className={`text-sm ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								}`}>
								{extensionsData?.profile?.name || "Dashboard"}
							</div>
						</div>
					</div>

					<div className="flex items-center gap-2">
						<Button
							variant="ghost"
							size="sm"
							onClick={handleBackToHome}
							className={`p-2 transition-colors ${
								isDarkMode
									? "text-gray-300 hover:text-white hover:bg-gray-800"
									: "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
							}`}>
							<Home size={18} />
						</Button>

						{unreadNotifications > 0 && (
							<Button
								variant="ghost"
								size="sm"
								className={`p-2 relative transition-colors ${
									isDarkMode
										? "text-gray-300 hover:text-white hover:bg-gray-800"
										: "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
								}`}>
								<Bell size={18} />
								<span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-xs text-white font-medium">
									{unreadNotifications}
								</span>
							</Button>
						)}
					</div>
				</div>
			</div>

			{/* Desktop Header */}
			<div className="hidden lg:block">
				<div className="flex items-center justify-between px-6 py-4">
					{/* Left Section */}
					<div className="flex items-center gap-6">
						<div className="flex flex-col">
							<div
								className={`text-sm font-medium uppercase ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								}`}>
								AI Digital Workforce
							</div>
							<div className="flex items-center gap-3">
								<h1
									className={`text-xl font-bold uppercase ${
										isDarkMode
											? "text-white"
											: "text-gray-900"
                                        }`}>
                                    welcome back {" "}
									{(() => {
										const match =
											user?.email?.match(/^[a-zA-Z]+/);
										return match ? match[0] : "";
									})()}
								</h1>
							</div>
						</div>
					</div>

					{/* Center Section - Search */}
					<div className="flex-1 max-w-md mx-8">
						<div className="relative">
							<Search
								className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-500"
								}`}
							/>
							<input
								type="text"
								placeholder="Search"
								className={`w-full pl-10 pr-4 py-2.5 rounded-lg border transition-colors focus:outline-none focus:ring-2 focus:ring-[#10A37F] ${
									isDarkMode
										? "bg-gray-900 border-gray-700 text-white placeholder-gray-400 focus:border-[#10A37F]"
										: "bg-gray-50 border-gray-300 text-gray-900 placeholder-gray-500 focus:border-[#10A37F] focus:bg-white"
								}`}
							/>
						</div>
					</div>

					{/* Right Section */}
					<div className="flex items-center gap-3">
						{/* Notifications */}
						{unreadNotifications > 0 && (
							<Button
								variant="ghost"
								size="sm"
								className={`relative p-2.5 transition-colors ${
									isDarkMode
										? "text-gray-300 hover:text-white hover:bg-gray-800"
										: "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
								}`}>
								<Bell size={18} />
								<span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-xs text-white font-medium">
									{unreadNotifications}
								</span>
							</Button>
						)}

						{/* Theme Toggle */}
						<Button
							variant="ghost"
							size="sm"
							onClick={onToggleTheme}
							className={`p-2.5 transition-colors ${
								isDarkMode
									? "text-gray-300 hover:text-white hover:bg-gray-800"
									: "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
							}`}>
							{isDarkMode ? (
								<Sun size={18} />
							) : (
								<Moon size={18} />
							)}
						</Button>

						{/* User Profile */}
						<div className="flex items-center gap-3 ml-2">
							<div
								className={`h-8 border-l ${
									isDarkMode
										? "border-gray-700"
										: "border-gray-300"
								}`}
							/>

							<Button
								variant="ghost"
								size="sm"
								onClick={handleBackToHome}
								className={`flex items-center gap-3 px-3 py-2 transition-colors ${
									isDarkMode
										? "text-gray-300 hover:text-white hover:bg-gray-800"
										: "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
								}`}>
								<div className="w-9 h-9 bg-gradient-to-r from-[#10A37F] to-[#2F81F7] rounded-lg flex items-center justify-center text-sm font-bold text-white shadow-sm">
									{user?.email?.[0]?.toUpperCase() || "U"}
								</div>

								<div className="flex flex-col">
									<div
										className={`text-sm font-semibold ${
											isDarkMode
												? "text-white"
												: "text-gray-900"
										}`}>
										{user?.email?.split("@")[0] || "User"}
									</div>
									<div
										className={`text-xs ${
											isDarkMode
												? "text-gray-400"
												: "text-gray-600"
										}`}>
										Active • v
										{extensionsData?.profile?.version ||
											"2.1.4"}
									</div>
								</div>

								<Home
									className={`w-4 h-4 ${
										isDarkMode
											? "text-gray-400"
											: "text-gray-500"
									}`}
								/>
							</Button>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default Header;
