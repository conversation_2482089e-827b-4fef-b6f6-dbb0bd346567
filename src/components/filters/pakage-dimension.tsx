"use client";

import React from "react";
import { Box } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FilterValues } from "@/constants/inspectoral-filter";

interface PackageDimensionProps {
	isDarkMode: boolean;
	filters: {
		packageLength_gte: string;
		packageLength_lte: string;
		packageWidth_gte: string;
		packageWidth_lte: string;
		packageHeight_gte: string;
		packageHeight_lte: string;
	};
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	onFilterChange: (key: keyof FilterValues, value: any) => void;
}

export const PackageDimension: React.FC<PackageDimensionProps> = ({
	isDarkMode,
	filters,
	onFilterChange,
}) => {
	return (
		<div className="space-y-4 col-span-full">
			<div className="flex items-center gap-2 group relative">
				<Box className="w-4 h-4 text-[#19D86C]" />
				<Label
					className={`text-sm font-medium ${
						isDarkMode ? "text-gray-300" : "text-gray-700"
					}`}>
					Package Dimension
				</Label>
				<div className="relative">
					<div
						className={`w-4 h-4 rounded-full border flex items-center justify-center text-xs cursor-help ${
							isDarkMode
								? "border-gray-500 text-gray-400"
								: "border-gray-400 text-gray-500"
						}`}>
						?
					</div>
					<div
						className={`absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 w-64 ${
							isDarkMode
								? "bg-gray-700 text-gray-200 border border-gray-600"
								: "bg-white text-gray-700 border border-gray-200"
						}`}>
						Filter products by their package dimensions in centimeters (length, width, and height).
						<div
							className={`absolute top-full left-1/2 transform -translate-x-1/2 w-2 h-2 rotate-45 ${
								isDarkMode
									? "bg-gray-700 border-b border-r border-gray-600"
									: "bg-white border-b border-r border-gray-200"
							}`}></div>
					</div>
				</div>
			</div>

			<div
				className={`p-4 rounded-lg border ${
					isDarkMode
						? "bg-gray-800/50 border-gray-700"
						: "bg-gray-50 border-gray-200"
				}`}>
				<div className="grid grid-cols-3 gap-6">
					{/* Length */}
					<div className="space-y-2">
						<Label
							className={`text-sm font-medium italic ${
								isDarkMode ? "text-gray-300" : "text-gray-700"
							}`}>
							Length (cm) (cm)
						</Label>
						<div className="grid grid-cols-2 gap-3">
							<div>
								<Label
									className={`text-xs ${
										isDarkMode
											? "text-gray-400"
											: "text-gray-600"
									} mb-1 block`}>
									From
								</Label>
								<Input
									type="number"
									placeholder="Min"
									value={filters.packageLength_gte}
									onChange={(e) =>
										onFilterChange(
											"packageLength_gte",
											e.target.value
										)
									}
									className={`${
										isDarkMode
											? "bg-gray-800 border-gray-700 text-white"
											: "bg-white border-gray-300 text-black"
									}`}
								/>
							</div>
							<div>
								<Label
									className={`text-xs ${
										isDarkMode
											? "text-gray-400"
											: "text-gray-600"
									} mb-1 block`}>
									To
								</Label>
								<Input
									type="number"
									placeholder="Max"
									value={filters.packageLength_lte}
									onChange={(e) =>
										onFilterChange(
											"packageLength_lte",
											e.target.value
										)
									}
									className={`${
										isDarkMode
											? "bg-gray-800 border-gray-700 text-white"
											: "bg-white border-gray-300 text-black"
									}`}
								/>
							</div>
						</div>
					</div>

					{/* Width */}
					<div className="space-y-2">
						<Label
							className={`text-sm font-medium italic ${
								isDarkMode ? "text-gray-300" : "text-gray-700"
							}`}>
							Width (cm) (cm)
						</Label>
						<div className="grid grid-cols-2 gap-3">
							<div>
								<Label
									className={`text-xs ${
										isDarkMode
											? "text-gray-400"
											: "text-gray-600"
									} mb-1 block`}>
									From
								</Label>
								<Input
									type="number"
									placeholder="Min"
									value={filters.packageWidth_gte}
									onChange={(e) =>
										onFilterChange(
											"packageWidth_gte",
											e.target.value
										)
									}
									className={`${
										isDarkMode
											? "bg-gray-800 border-gray-700 text-white"
											: "bg-white border-gray-300 text-black"
									}`}
								/>
							</div>
							<div>
								<Label
									className={`text-xs ${
										isDarkMode
											? "text-gray-400"
											: "text-gray-600"
									} mb-1 block`}>
									To
								</Label>
								<Input
									type="number"
									placeholder="Max"
									value={filters.packageWidth_lte}
									onChange={(e) =>
										onFilterChange(
											"packageWidth_lte",
											e.target.value
										)
									}
									className={`${
										isDarkMode
											? "bg-gray-800 border-gray-700 text-white"
											: "bg-white border-gray-300 text-black"
									}`}
								/>
							</div>
						</div>
					</div>

					{/* Height */}
					<div className="space-y-2">
						<Label
							className={`text-sm font-medium italic ${
								isDarkMode ? "text-gray-300" : "text-gray-700"
							}`}>
							Height (cm) (cm)
						</Label>
						<div className="grid grid-cols-2 gap-3">
							<div>
								<Label
									className={`text-xs ${
										isDarkMode
											? "text-gray-400"
											: "text-gray-600"
									} mb-1 block`}>
									From
								</Label>
								<Input
									type="number"
									placeholder="Min"
									value={filters.packageHeight_gte}
									onChange={(e) =>
										onFilterChange(
											"packageHeight_gte",
											e.target.value
										)
									}
									className={`${
										isDarkMode
											? "bg-gray-800 border-gray-700 text-white"
											: "bg-white border-gray-300 text-black"
									}`}
								/>
							</div>
							<div>
								<Label
									className={`text-xs ${
										isDarkMode
											? "text-gray-400"
											: "text-gray-600"
									} mb-1 block`}>
									To
								</Label>
								<Input
									type="number"
									placeholder="Max"
									value={filters.packageHeight_lte}
									onChange={(e) =>
										onFilterChange(
											"packageHeight_lte",
											e.target.value
										)
									}
									className={`${
										isDarkMode
											? "bg-gray-800 border-gray-700 text-white"
											: "bg-white border-gray-300 text-black"
									}`}
								/>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};