"use client";

import React from "react";
import { GitBranch } from "lucide-react";
import { Label } from "@/components/ui/label";
import { FilterValues } from "@/constants/inspectoral-filter";
import { Checkbox } from "../ui/checkbox";

interface VariationsProps {
	isDarkMode: boolean;
	filters: {
		hasParentASIN: boolean | null;
	};
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	onFilterChange: (key: keyof FilterValues, value: any) => void;
}

export const Variations: React.FC<VariationsProps> = ({
	isDarkMode,
	filters,
	onFilterChange,
}) => {

	return (
		<div className="space-y-4 col-span-full">
			<div className="flex items-center gap-2 group relative">
				<GitBranch className="w-4 h-4 text-[#19D86C]" />
				<Label
					className={`text-sm font-medium ${
						isDarkMode ? "text-gray-300" : "text-gray-700"
					}`}>
					Variations
				</Label>
				<div className="relative">
					<div
						className={`w-4 h-4 rounded-full border flex items-center justify-center text-xs cursor-help ${
							isDarkMode
								? "border-gray-500 text-gray-400"
								: "border-gray-400 text-gray-500"
						}`}>
						?
					</div>
					<div
						className={`absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 w-64 ${
							isDarkMode
								? "bg-gray-700 text-gray-200 border border-gray-600"
								: "bg-white text-gray-700 border border-gray-200"
						}`}>
						Filter products based on whether they have variations
						(parent ASIN) or are standalone products.
						<div
							className={`absolute top-full left-1/2 transform -translate-x-1/2 w-2 h-2 rotate-45 ${
								isDarkMode
									? "bg-gray-700 border-b border-r border-gray-600"
									: "bg-white border-b border-r border-gray-200"
							}`}></div>
					</div>
				</div>
			</div>

			<div
				className={`p-4 rounded-lg border ${
					isDarkMode
						? "bg-gray-800/50 border-gray-700"
						: "bg-gray-50 border-gray-200"
				}`}>
				<div className="flex items-center gap-6">
				<div className="flex gap-4">
	<div className="flex items-center space-x-2">
		<Checkbox
			id="hasParentASIN_true"
			checked={filters.hasParentASIN === true}
			onCheckedChange={(checked) =>
				onFilterChange("hasParentASIN", checked === true ? true : null)
			}
		/>
		<Label
			htmlFor="hasParentASIN_true"
			className={`text-sm ${
				isDarkMode
					? "text-gray-300"
					: "text-gray-700"
			}`}>
			Is Variation
		</Label>
	</div>
	<div className="flex items-center space-x-2">
		<Checkbox
			id="hasParentASIN_false"
			checked={filters.hasParentASIN === false}
			onCheckedChange={(checked) =>
				onFilterChange("hasParentASIN", checked === true ? false : null)
			}
		/>
		<Label
			htmlFor="hasParentASIN_false"
			className={`text-sm ${
				isDarkMode
					? "text-gray-300"
					: "text-gray-700"
			}`}>
			No Variations
		</Label>
	</div>
</div>
			</div>
            </div>
            </div>
	);
};
