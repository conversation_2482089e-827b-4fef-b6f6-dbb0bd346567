"use client";

import React from "react";
import { Package } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { FilterValues } from "@/constants/inspectoral-filter";

interface AvailabilityAmazonProps {
	isDarkMode: boolean;
	filters: {
		availabilityAmazon: number[];
	};
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	onFilterChange: (key: keyof FilterValues, value: any) => void;
}

export const AvailabilityAmazon: React.FC<AvailabilityAmazonProps> = ({
	isDarkMode,
	filters,
	onFilterChange,
}) => {
	const availabilityOptions = [
		{ value: -1, label: "no Amazon offer exists" },
		{ value: 0, label: "Amazon offer is in stock and shippable" },
		{ value: 1, label: "Amazon offer is a pre-order" },
		{ value: 2, label: "Amazon offer availability is 'unknown'" },
		{ value: 3, label: "Amazon offer is back-ordered" },
		{ value: 4, label: "Amazon offer shipping is delayed" },
	];

	const handleCheckboxChange = (value: number, checked: boolean) => {
		const currentValues = filters.availabilityAmazon || [];
		let newValues: number[];

		// If currently empty array (all selected) and user unchecks something
		if (currentValues.length === 0 && !checked) {
			// Start with all values except the one being unchecked
			newValues = availabilityOptions
				.map((option) => option.value)
				.filter((v) => v !== value);
		}
		// If currently empty array (all selected) and user checks something (shouldn't happen but just in case)
		else if (currentValues.length === 0 && checked) {
			// Keep all selected (return empty array)
			newValues = [];
		}
		// Normal case: some items are selected
		else {
			if (checked) {
				newValues = [...currentValues, value];
			} else {
				newValues = currentValues.filter((v) => v !== value);
			}
		}

		// If all options are selected, send empty array (nothing)
		if (newValues.length === availabilityOptions.length) {
			onFilterChange("availabilityAmazon", []);
		} else {
			onFilterChange("availabilityAmazon", newValues);
		}
	};

	const isChecked = (value: number) => {
		const currentValues = filters.availabilityAmazon || [];
		// If empty array (all selected), show all as checked
		if (currentValues.length === 0) {
			return true;
		}
		return currentValues.includes(value);
	};

	return (
		<div className="space-y-4 col-span-full">
			<div className="flex items-center gap-2 group relative">
				<Package className="w-4 h-4 text-[#19D86C]" />
				<Label
					className={`text-sm font-medium ${
						isDarkMode ? "text-gray-300" : "text-gray-700"
					}`}>
					Availability of the Amazon offer
				</Label>
				<div className="relative">
					<div
						className={`w-4 h-4 rounded-full border flex items-center justify-center text-xs cursor-help ${
							isDarkMode
								? "border-gray-500 text-gray-400"
								: "border-gray-400 text-gray-500"
						}`}>
						?
					</div>
					<div
						className={`absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 w-64 ${
							isDarkMode
								? "bg-gray-700 text-gray-200 border border-gray-600"
								: "bg-white text-gray-700 border border-gray-200"
						}`}>
						Availability of the Amazon offer - filter products by
						their current Amazon availability status.
						<div
							className={`absolute top-full left-1/2 transform -translate-x-1/2 w-2 h-2 rotate-45 ${
								isDarkMode
									? "bg-gray-700 border-b border-r border-gray-600"
									: "bg-white border-b border-r border-gray-200"
							}`}></div>
					</div>
				</div>
			</div>

			<div
				className={`p-4 rounded-lg border ${
					isDarkMode
						? "bg-gray-800/50 border-gray-700"
						: "bg-gray-50 border-gray-200"
				}`}>
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					{availabilityOptions.map((option) => (
						<div
							key={option.value}
							className="flex items-center space-x-2">
							<Checkbox
								id={`availability_${option.value}`}
								checked={isChecked(option.value)}
								onCheckedChange={(checked) =>
									handleCheckboxChange(
										option.value,
										checked as boolean
									)
								}
							/>
							<Label
								htmlFor={`availability_${option.value}`}
								className={`text-sm ${
									isDarkMode
										? "text-gray-300"
										: "text-gray-700"
								}`}>
								{option.label}
							</Label>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};
