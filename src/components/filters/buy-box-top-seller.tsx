// @/components/filters/buy-box-top-seller.tsx
"use client";

import React from "react";
import { Crown } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FilterValues } from "@/constants/inspectoral-filter";

interface BuyBoxTopSellerProps {
	isDarkMode: boolean;
	filters: {
		buyBoxStatsTopSeller30_gte: string;
		buyBoxStatsTopSeller30_lte: string;
		buyBoxStatsTopSeller90_gte: string;
		buyBoxStatsTopSeller90_lte: string;
	};
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	onFilterChange: (key: keyof FilterValues, value: any) => void;
}

export const BuyBoxTopSeller: React.FC<BuyBoxTopSellerProps> = ({
	isDarkMode,
	filters,
	onFilterChange,
}) => {
	return (
		<div className="space-y-4 col-span-full">
			<div className="flex items-center gap-2 group relative">
				<Crown className="w-4 h-4 text-[#19D86C]" />
				<Label
					className={`text-sm font-medium ${
						isDarkMode ? "text-gray-300" : "text-gray-700"
					}`}>
					Buy Box: % Top Seller
				</Label>
				<div className="relative">
					<div
						className={`w-4 h-4 rounded-full border flex items-center justify-center text-xs cursor-help ${
							isDarkMode
								? "border-gray-500 text-gray-400"
								: "border-gray-400 text-gray-500"
						}`}>
						?
					</div>
					<div
						className={`absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 w-64 ${
							isDarkMode
								? "bg-gray-700 text-gray-200 border border-gray-600"
								: "bg-white text-gray-700 border border-gray-200"
						}`}>
						Buy Box Share % from Seller with highest % won (Amazon
						incl.) - Filter products by the percentage of time the
						top seller holds the Buy Box over 30 and 90 day periods.
						<div
							className={`absolute top-full left-1/2 transform -translate-x-1/2 w-2 h-2 rotate-45 ${
								isDarkMode
									? "bg-gray-700 border-b border-r border-gray-600"
									: "bg-white border-b border-r border-gray-200"
							}`}></div>
					</div>
				</div>
			</div>

			<div
				className={`p-4 rounded-lg border ${
					isDarkMode
						? "bg-gray-800/50 border-gray-700"
						: "bg-gray-50 border-gray-200"
				}`}>
				<div className="grid grid-cols-2 gap-6">
					<div className="space-y-2">
						<Label
							className={`text-sm font-medium ${
								isDarkMode ? "text-gray-300" : "text-gray-700"
							}`}>
							30 days
						</Label>
						<div className="grid grid-cols-2 gap-3">
							<div>
								<Label
									className={`text-xs ${
										isDarkMode
											? "text-gray-400"
											: "text-gray-600"
									} mb-1 block`}>
									From
								</Label>
								<Input
									type="number"
									placeholder="Min %"
									value={filters.buyBoxStatsTopSeller30_gte}
									onChange={(e) =>
										onFilterChange(
											"buyBoxStatsTopSeller30_gte",
											e.target.value
										)
									}
									className={`${
										isDarkMode
											? "bg-gray-800 border-gray-700 text-white"
											: "bg-white border-gray-300 text-black"
									}`}
								/>
							</div>
							<div>
								<Label
									className={`text-xs ${
										isDarkMode
											? "text-gray-400"
											: "text-gray-600"
									} mb-1 block`}>
									To
								</Label>
								<Input
									type="number"
									placeholder="Max %"
									value={filters.buyBoxStatsTopSeller30_lte}
									onChange={(e) =>
										onFilterChange(
											"buyBoxStatsTopSeller30_lte",
											e.target.value
										)
									}
									className={`${
										isDarkMode
											? "bg-gray-800 border-gray-700 text-white"
											: "bg-white border-gray-300 text-black"
									}`}
								/>
							</div>
						</div>
					</div>

					<div className="space-y-2">
						<Label
							className={`text-sm font-medium ${
								isDarkMode ? "text-gray-300" : "text-gray-700"
							}`}>
							90 days
						</Label>
						<div className="grid grid-cols-2 gap-3">
							<div>
								<Label
									className={`text-xs ${
										isDarkMode
											? "text-gray-400"
											: "text-gray-600"
									} mb-1 block`}>
									From
								</Label>
								<Input
									type="number"
									placeholder="Min %"
									value={filters.buyBoxStatsTopSeller90_gte}
									onChange={(e) =>
										onFilterChange(
											"buyBoxStatsTopSeller90_gte",
											e.target.value
										)
									}
									className={`${
										isDarkMode
											? "bg-gray-800 border-gray-700 text-white"
											: "bg-white border-gray-300 text-black"
									}`}
								/>
							</div>
							<div>
								<Label
									className={`text-xs ${
										isDarkMode
											? "text-gray-400"
											: "text-gray-600"
									} mb-1 block`}>
									To
								</Label>
								<Input
									type="number"
									placeholder="Max %"
									value={filters.buyBoxStatsTopSeller90_lte}
									onChange={(e) =>
										onFilterChange(
											"buyBoxStatsTopSeller90_lte",
											e.target.value
										)
									}
									className={`${
										isDarkMode
											? "bg-gray-800 border-gray-700 text-white"
											: "bg-white border-gray-300 text-black"
									}`}
								/>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};
