"use client";

import React from "react";
import { AlertTriangle } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { FilterValues } from "@/constants/inspectoral-filter";

interface HazMatProps {
	isDarkMode: boolean;
	filters: {
		isHazMat: boolean | null;
		isHeatSensitive: boolean | null;
	};
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	onFilterChange: (key: keyof FilterValues, value: any) => void;
}

export const HazMat: React.FC<HazMatProps> = ({
	isDarkMode,
	filters,
	onFilterChange,
}) => {
	return (
		<div className="space-y-4 col-span-full">
			<div className="flex items-center gap-2 group relative">
				<AlertTriangle className="w-4 h-4 text-[#19D86C]" />
				<div className="relative">
					<div
						className={`w-4 h-4 rounded-full border flex items-center justify-center text-xs cursor-help ${
							isDarkMode
								? "border-gray-500 text-gray-400"
								: "border-gray-400 text-gray-500"
						}`}>
						?
					</div>
					<div
						className={`absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 w-80 ${
							isDarkMode
								? "bg-gray-700 text-gray-200 border border-gray-600"
								: "bg-white text-gray-700 border border-gray-200"
						}`}>
						Indicates whether the product is classified as hazardous
						material (HazMat) according to Amazon&apos;s safety
						regulations.
						<div
							className={`absolute top-full left-1/2 transform -translate-x-1/2 w-2 h-2 rotate-45 ${
								isDarkMode
									? "bg-gray-700 border-b border-r border-gray-600"
									: "bg-white border-b border-r border-gray-200"
							}`}></div>
					</div>
				</div>
			</div>

			<div
				className={`p-4 rounded-lg border ${
					isDarkMode
						? "bg-gray-800/50 border-gray-700"
						: "bg-gray-50 border-gray-200"
				}`}>
				<div className="grid grid-cols-2 gap-8">
					{/* Is HazMat */}
					<div className="space-y-3">
						<Label
							className={`text-sm font-medium italic ${
								isDarkMode ? "text-gray-300" : "text-gray-700"
							}`}>
							Is HazMat
						</Label>
						<div className="flex gap-4">
							<div className="flex items-center space-x-2">
								<Checkbox
									id="isHazMat_true"
									checked={filters.isHazMat === true}
									onCheckedChange={(checked) =>
										onFilterChange(
											"isHazMat",
											checked === true ? true : null
										)
									}
								/>
								<Label
									htmlFor="isHazMat_true"
									className={`text-sm ${
										isDarkMode
											? "text-gray-300"
											: "text-gray-700"
									}`}>
									Yes
								</Label>
							</div>
							<div className="flex items-center space-x-2">
								<Checkbox
									id="isHazMat_false"
									checked={filters.isHazMat === false}
									onCheckedChange={(checked) =>
										onFilterChange(
											"isHazMat",
											checked === true ? false : null
										)
									}
								/>
								<Label
									htmlFor="isHazMat_false"
									className={`text-sm ${
										isDarkMode
											? "text-gray-300"
											: "text-gray-700"
									}`}>
									No
								</Label>
							</div>
						</div>
					</div>

					{/* Is Heat Sensitive */}
					<div className="space-y-3">
						<Label
							className={`text-sm font-medium italic ${
								isDarkMode ? "text-gray-300" : "text-gray-700"
							}`}>
							Is heat sensitive
						</Label>
						<div className="flex gap-4">
							<div className="flex items-center space-x-2">
								<Checkbox
									id="isHeatSensitive_true"
									checked={filters.isHeatSensitive === true}
									onCheckedChange={(checked) =>
										onFilterChange(
											"isHeatSensitive",
											checked === true ? true : null
										)
									}
								/>
								<Label
									htmlFor="isHeatSensitive_true"
									className={`text-sm ${
										isDarkMode
											? "text-gray-300"
											: "text-gray-700"
									}`}>
									Yes
								</Label>
							</div>
							<div className="flex items-center space-x-2">
								<Checkbox
									id="isHeatSensitive_false"
									checked={filters.isHeatSensitive === false}
									onCheckedChange={(checked) =>
										onFilterChange(
											"isHeatSensitive",
											checked === true ? false : null
										)
									}
								/>
								<Label
									htmlFor="isHeatSensitive_false"
									className={`text-sm ${
										isDarkMode
											? "text-gray-300"
											: "text-gray-700"
									}`}>
									No
								</Label>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};
