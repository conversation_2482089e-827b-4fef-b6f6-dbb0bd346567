// @/components/filters/SellerIds.tsx
import React from "react";
import { Users } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FilterValues } from "@/constants/inspectoral-filter";

interface SellerIdsProps {
	isDarkMode: boolean;
	filters: {
		buyBoxSeller: string;
	};
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	onFilterChange: (key: keyof FilterValues, value: any) => void;
}

export const SellerIds: React.FC<SellerIdsProps> = ({
	isDarkMode,
	filters,
	onFilterChange,
}) => {
	return (
		<div className="space-y-4 col-span-full">
			<div className="flex items-center gap-2 group relative">
				<Users className="w-4 h-4 text-[#19D86C]" />
				<Label
					className={`text-sm font-medium ${
						isDarkMode ? "text-gray-300" : "text-gray-700"
					}`}>
					Seller IDs
				</Label>
				<div className="relative">
					<div
						className={`w-4 h-4 rounded-full border flex items-center justify-center text-xs cursor-help ${
							isDarkMode
								? "border-gray-500 text-gray-400"
								: "border-gray-400 text-gray-500"
						}`}>
						?
					</div>
					<div
						className={`absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 w-64 ${
							isDarkMode
								? "bg-gray-700 text-gray-200 border border-gray-600"
								: "bg-white text-gray-700 border border-gray-200"
						}`}>
						Enter comma-separated seller IDs to filter products by
						specific Buy Box sellers (e.g., esrewr, dfsfsf,
						seller123).
						<div
							className={`absolute top-full left-1/2 transform -translate-x-1/2 w-2 h-2 rotate-45 ${
								isDarkMode
									? "bg-gray-700 border-b border-r border-gray-600"
									: "bg-white border-b border-r border-gray-200"
							}`}></div>
					</div>
				</div>
			</div>

			<div
				className={`p-4 rounded-lg border ${
					isDarkMode
						? "bg-gray-800/50 border-gray-700"
						: "bg-gray-50 border-gray-200"
				}`}>
				<div className="flex items-center gap-2 mb-3">
					{/* <div className="w-3 h-3 rounded-full bg-blue-500"></div> */}
					<span
						className={`font-medium ${
							isDarkMode ? "text-gray-300" : "text-gray-700"
						}`}>
						Seller IDs:
					</span>
				</div>

				<div className="space-y-2">
					<Input
						type="text"
						placeholder="esrewr,dfsfsf,seller123"
						value={filters.buyBoxSeller}
						onChange={(e) =>
							onFilterChange("buyBoxSeller", e.target.value)
						}
						className={`${
							isDarkMode
								? "bg-gray-800 border-gray-700 text-white"
								: "bg-white border-gray-300 text-black"
						}`}
					/>
					<p
						className={`text-xs ${
							isDarkMode ? "text-gray-400" : "text-gray-600"
						}`}>
						Enter seller IDs separated by commas
					</p>
				</div>
			</div>
		</div>
	);
};
