/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import { Truck } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { FilterValues } from "@/constants/inspectoral-filter";

interface BuyBoxProps {
	isDarkMode: boolean;
	filters: {
		current_BUY_BOX_SHIPPING_gte: string;
		current_BUY_BOX_SHIPPING_lte: string;
		isOutOfStock: boolean | null;
	};
	onFilterChange: (key: keyof FilterValues, value: any) => void;
}

export const BuyBox: React.FC<BuyBoxProps> = ({
	isDarkMode,
	filters,
	onFilterChange,
}) => {
	const handleOutOfStockChange = (checked: boolean) => {
		if (checked) {
			onFilterChange("isOutOfStock", true);
			onFilterChange("current_BUY_BOX_SHIPPING_gte", "-1");
			onFilterChange("current_BUY_BOX_SHIPPING_lte", "-1");
		} else {
			onFilterChange("isOutOfStock", null);
			onFilterChange("current_BUY_BOX_SHIPPING_gte", "");
			onFilterChange("current_BUY_BOX_SHIPPING_lte", "");
		}
	};

	const isOutOfStock = filters.isOutOfStock === true;

	return (
		<div className="space-y-4 col-span-full">
			<div className="flex items-center gap-2 group relative">
				<Truck className="w-4 h-4 text-[#19D86C]" />
				<Label
					className={`text-sm font-medium ${
						isDarkMode ? "text-gray-300" : "text-gray-700"
					}`}>
					Price of the New Buy Box offer, shipping included
				</Label>
				<div className="relative">
					<div
						className={`w-4 h-4 rounded-full border flex items-center justify-center text-xs cursor-help ${
							isDarkMode
								? "border-gray-500 text-gray-400"
								: "border-gray-400 text-gray-500"
						}`}>
						?
					</div>
					<div
						className={`absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 w-64 ${
							isDarkMode
								? "bg-gray-700 text-gray-200 border border-gray-600"
								: "bg-white text-gray-700 border border-gray-200"
						}`}>
						Filter products by their Buy Box price including
						shipping costs. Select &quot;Out of stock&quot; to find products
						without available Buy Box offers.
						<div
							className={`absolute top-full left-1/2 transform -translate-x-1/2 w-2 h-2 rotate-45 ${
								isDarkMode
									? "bg-gray-700 border-b border-r border-gray-600"
									: "bg-white border-b border-r border-gray-200"
							}`}></div>
					</div>
				</div>
			</div>

			<div
				className={`p-4 rounded-lg border ${
					isDarkMode
						? "bg-gray-800/50 border-gray-700"
						: "bg-gray-50 border-gray-200"
				}`}>
				<div className="flex items-center justify-between mb-4">
					<div className="flex items-center gap-2">
						{/* <div className="w-3 h-3 rounded-full bg-pink-500"></div> */}
						<span
							className={`font-medium ${
								isDarkMode ? "text-gray-300" : "text-gray-700"
							}`}>
							Buy Box £
						</span>
						<div className="relative">
							<div
								className={`w-4 h-4 rounded-full border flex items-center justify-center text-xs cursor-help ${
									isDarkMode
										? "border-gray-500 text-gray-400"
										: "border-gray-400 text-gray-500"
								}`}>
								?
							</div>
						</div>
					</div>

					<div className="flex items-center space-x-2">
						<Checkbox
							id="out_of_stock"
							checked={isOutOfStock}
							onCheckedChange={handleOutOfStockChange}
						/>
						<Label
							htmlFor="out_of_stock"
							className={`text-sm ${
								isDarkMode ? "text-gray-300" : "text-gray-700"
							}`}>
							Out of stock
						</Label>
					</div>
				</div>

				<div className="space-y-2">
					<Label
						className={`text-sm font-medium ${
							isDarkMode ? "text-gray-300" : "text-gray-700"
						}`}>
						Current
					</Label>
					<div className="grid grid-cols-2 gap-3">
						<div>
							<Label
								className={`text-xs ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								} mb-1 block`}>
								From
							</Label>
							<Input
								type="number"
								placeholder="Min price"
								value={
									isOutOfStock
										? ""
										: filters.current_BUY_BOX_SHIPPING_gte
								}
								onChange={(e) =>
									onFilterChange(
										"current_BUY_BOX_SHIPPING_gte",
										e.target.value
									)
								}
								disabled={isOutOfStock}
								className={`${
									isDarkMode
										? "bg-gray-800 border-gray-700 text-white"
										: "bg-white border-gray-300 text-black"
								} ${
									isOutOfStock
										? "opacity-50 cursor-not-allowed"
										: ""
								}`}
							/>
						</div>
						<div>
							<Label
								className={`text-xs ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								} mb-1 block`}>
								To
							</Label>
							<Input
								type="number"
								placeholder="Max price"
								value={
									isOutOfStock
										? ""
										: filters.current_BUY_BOX_SHIPPING_lte
								}
								onChange={(e) =>
									onFilterChange(
										"current_BUY_BOX_SHIPPING_lte",
										e.target.value
									)
								}
								disabled={isOutOfStock}
								className={`${
									isDarkMode
										? "bg-gray-800 border-gray-700 text-white"
										: "bg-white border-gray-300 text-black"
								} ${
									isOutOfStock
										? "opacity-50 cursor-not-allowed"
										: ""
								}`}
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};
