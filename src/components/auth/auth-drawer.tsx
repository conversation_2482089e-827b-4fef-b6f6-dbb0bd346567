/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
	Drawer,
	DrawerContent,
	DrawerDescription,
	DrawerHeader,
	DrawerTitle,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useLogin, useRegister } from "@/hooks/use-auth";
import { Eye, EyeOff, Check, AlertCircle, CheckCircle } from "lucide-react";

interface AuthDrawerProps {
	isOpen: boolean;
	onClose: () => void;
	mode: "login" | "signup";
	onSwitchMode: (mode: "login" | "signup") => void;
}

export function AuthDrawer({
	isOpen,
	onClose,
	mode,
	onSwitchMode,
}: AuthDrawerProps) {
	const router = useRouter();
	const { login, isLoading: isLoggingIn, error: loginError } = useLogin();
	const {
		register,
		isLoading: isRegistering,
		error: registerError,
	} = useRegister();

	const [showPassword, setShowPassword] = useState(false);
	const [showConfirmPassword, setShowConfirmPassword] = useState(false);
	const [formData, setFormData] = useState({
		email: "",
		password: "",
		confirmPassword: "",
	});
	const [errors, setErrors] = useState<{ [key: string]: string }>({});
	const [successMessage, setSuccessMessage] = useState<string>("");

	const isLoading = isLoggingIn || isRegistering;
	const error = loginError || registerError;

	const resetForm = () => {
		setFormData({ email: "", password: "", confirmPassword: "" });
		setErrors({});
		setSuccessMessage("");
		setShowPassword(false);
		setShowConfirmPassword(false);
	};

	const handleClose = () => {
		resetForm();
		onClose();
	};

	const handleSwitchMode = (newMode: "login" | "signup") => {
		resetForm();
		onSwitchMode(newMode);
	};

	const validateForm = () => {
		const newErrors: { [key: string]: string } = {};

		if (!formData.email) {
			newErrors.email = "Email is required";
		} else if (!/\S+@\S+\.\S+/.test(formData.email)) {
			newErrors.email = "Email is invalid";
		}

		if (!formData.password) {
			newErrors.password = "Password is required";
		} else if (mode === "signup" && formData.password.length < 8) {
			newErrors.password = "Password must be at least 8 characters";
		} else if (
			mode === "signup" &&
			!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)
		) {
			newErrors.password =
				"Password must contain at least one uppercase letter, one lowercase letter, and one number";
		} else if (mode === "login" && formData.password.length < 6) {
			newErrors.password = "Password must be at least 6 characters";
		}

		if (mode === "signup") {
			if (!formData.confirmPassword) {
				newErrors.confirmPassword = "Please confirm your password";
			} else if (formData.password !== formData.confirmPassword) {
				newErrors.confirmPassword = "Passwords do not match";
			}
		}

		setErrors(newErrors);
		return Object.keys(newErrors).length === 0;
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!validateForm()) {
			return;
		}

		setSuccessMessage("");

		try {
			if (mode === "login") {
				login({
					username: formData.email,
					password: formData.password,
				});
				setSuccessMessage("Login successful! Redirecting...");
			} else {
				register({
					email: formData.email,
					password: formData.password,
				});
				setSuccessMessage(
					"Account created successfully! Please check your email to verify your account."
				);
			}

			// Redirect after success
			setTimeout(
				() => {
					handleClose();
					router.push("/aep-solutions/platforms");
				},
				mode === "login" ? 1500 : 3000
			);
		} catch (error: any) {
			console.error(`${mode} failed:`, error?.message || error);
		}
	};

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target;
		setFormData((prev) => ({ ...prev, [name]: value }));

		// Clear errors when user starts typing
		if (errors[name]) {
			setErrors((prev) => ({ ...prev, [name]: "" }));
		}
		if (successMessage) {
			setSuccessMessage("");
		}
	};

	const getPasswordStrength = () => {
		const password = formData.password;
		if (!password) return { strength: 0, label: "" };

		let strength = 0;
		const checks = [
			password.length >= 8,
			/[a-z]/.test(password),
			/[A-Z]/.test(password),
			/\d/.test(password),
			/[!@#$%^&*(),.?":{}|<>]/.test(password),
		];

		strength = checks.filter(Boolean).length;

		if (strength <= 2)
			return { strength, label: "Weak", color: "text-red-500" };
		if (strength <= 3)
			return { strength, label: "Fair", color: "text-yellow-500" };
		if (strength <= 4)
			return { strength, label: "Good", color: "text-blue-500" };
		return { strength, label: "Strong", color: "text-green-500" };
	};

	const passwordStrength = getPasswordStrength();

	return (
		<Drawer open={isOpen} onOpenChange={handleClose}>
			<DrawerContent className="bg-black border-gray-800 text-white max-h-[90vh]">
				<div className="mx-auto w-full max-w-sm">
					<DrawerHeader className="pb-4">
						<DrawerTitle className="text-2xl font-bold text-center">
							{mode === "login"
								? "Welcome back"
								: "Create your account"}
						</DrawerTitle>
						<DrawerDescription className="text-center text-gray-400">
							{mode === "login"
								? "Sign in to your ClickBuy Deals account"
								: "Sign up with your email to start using AEP Platform"}
						</DrawerDescription>
					</DrawerHeader>

					<div className="p-4 pb-8">
						<form onSubmit={handleSubmit} className="space-y-4">
							{/* API Error Message */}
							{error && (
								<div className="flex items-center gap-2 p-3 text-sm text-red-400 bg-red-950/50 border border-red-800 rounded-md">
									<AlertCircle size={16} />
									<span>
										{error.detail ||
											error.message ||
											String(error)}
									</span>
								</div>
							)}

							{/* Success Message */}
							{successMessage && (
								<div className="flex items-center gap-2 p-3 text-sm text-green-400 bg-green-950/50 border border-green-800 rounded-md">
									<CheckCircle size={16} />
									<span>{successMessage}</span>
								</div>
							)}

							<div className="space-y-2">
								<Label
									htmlFor="drawer-email"
									className="text-white font-medium">
									Email
								</Label>
								<Input
									id="drawer-email"
									name="email"
									type="email"
									placeholder="Enter your email"
									value={formData.email}
									onChange={handleInputChange}
									className={`bg-gray-900 border-gray-700 text-white placeholder-gray-500 focus:border-[#19D86C] focus:ring-[#19D86C] ${
										errors.email ? "border-red-500" : ""
									}`}
								/>
								{errors.email && (
									<p className="text-sm text-red-400">
										{errors.email}
									</p>
								)}
							</div>

							<div className="space-y-2">
								<Label
									htmlFor="drawer-password"
									className="text-white font-medium">
									Password
								</Label>
								<div className="relative">
									<Input
										id="drawer-password"
										name="password"
										type={
											showPassword ? "text" : "password"
										}
										placeholder={
											mode === "login"
												? "Enter your password"
												: "Create a password"
										}
										value={formData.password}
										onChange={handleInputChange}
										className={`bg-gray-900 border-gray-700 text-white placeholder-gray-500 focus:border-[#19D86C] focus:ring-[#19D86C] pr-10 ${
											errors.password
												? "border-red-500"
												: ""
										}`}
									/>
									<button
										type="button"
										onClick={() =>
											setShowPassword(!showPassword)
										}
										className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
										aria-label={
											showPassword
												? "Hide password"
												: "Show password"
										}>
										{showPassword ? (
											<EyeOff size={20} />
										) : (
											<Eye size={20} />
										)}
									</button>
								</div>
								{mode === "signup" && formData.password && (
									<div className="flex items-center gap-2">
										<div className="flex-1 bg-gray-700 rounded-full h-2">
											<div
												className={`h-2 rounded-full transition-all duration-300 ${
													passwordStrength.strength <=
													2
														? "bg-red-500"
														: passwordStrength.strength <=
														  3
														? "bg-yellow-500"
														: passwordStrength.strength <=
														  4
														? "bg-blue-500"
														: "bg-green-500"
												}`}
												style={{
													width: `${
														(passwordStrength.strength /
															5) *
														100
													}%`,
												}}
											/>
										</div>
										<span
											className={`text-sm ${passwordStrength.color}`}>
											{passwordStrength.label}
										</span>
									</div>
								)}
								{errors.password && (
									<p className="text-sm text-red-400">
										{errors.password}
									</p>
								)}
							</div>

							{mode === "signup" && (
								<div className="space-y-2">
									<Label
										htmlFor="drawer-confirmPassword"
										className="text-white font-medium">
										Confirm Password
									</Label>
									<div className="relative">
										<Input
											id="drawer-confirmPassword"
											name="confirmPassword"
											type={
												showConfirmPassword
													? "text"
													: "password"
											}
											placeholder="Confirm your password"
											value={formData.confirmPassword}
											onChange={handleInputChange}
											className={`bg-gray-900 border-gray-700 text-white placeholder-gray-500 focus:border-[#19D86C] focus:ring-[#19D86C] pr-10 ${
												errors.confirmPassword
													? "border-red-500"
													: ""
											}`}
										/>
										<button
											type="button"
											onClick={() =>
												setShowConfirmPassword(
													!showConfirmPassword
												)
											}
											className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
											aria-label={
												showConfirmPassword
													? "Hide confirm password"
													: "Show confirm password"
											}>
											{showConfirmPassword ? (
												<EyeOff size={20} />
											) : (
												<Eye size={20} />
											)}
										</button>
									</div>
									{formData.confirmPassword &&
										formData.password ===
											formData.confirmPassword && (
											<div className="flex items-center gap-1 text-green-400">
												<Check size={16} />
												<span className="text-sm">
													Passwords match
												</span>
											</div>
										)}
									{errors.confirmPassword && (
										<p className="text-sm text-red-400">
											{errors.confirmPassword}
										</p>
									)}
								</div>
							)}

							{mode === "login" && (
								<div className="flex items-center justify-between">
									<button
										type="button"
										className="text-sm text-gray-400 hover:text-white hover:underline transition-colors">
										Forgot password?
									</button>
								</div>
							)}

							<Button
								type="submit"
								className="w-full bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] font-bold hover:from-[#12C2E9] hover:to-[#19D86C] transition-all duration-200"
								disabled={isLoading}>
								{isLoading
									? mode === "login"
										? "Signing in..."
										: "Creating account..."
									: mode === "login"
									? "Sign In"
									: "Create Account"}
							</Button>
						</form>

						<div className="flex flex-col space-y-4 pt-4 border-t border-gray-800 mt-6">
							<div className="text-sm text-center text-gray-400">
								{mode === "login"
									? "Don't have an account?"
									: "Already have an account?"}{" "}
								<button
									onClick={() =>
										handleSwitchMode(
											mode === "login"
												? "signup"
												: "login"
										)
									}
									className="text-[#19D86C] hover:underline font-medium transition-colors">
									{mode === "login" ? "Sign up" : "Sign in"}
								</button>
							</div>
							{mode === "signup" && (
								<div className="text-xs text-center text-gray-500">
									By creating an account, you agree to our{" "}
									<button className="text-gray-400 hover:text-white hover:underline transition-colors">
										Terms of Service
									</button>{" "}
									and{" "}
									<button className="text-gray-400 hover:text-white hover:underline transition-colors">
										Privacy Policy
									</button>
								</div>
							)}
						</div>
					</div>
				</div>
			</DrawerContent>
		</Drawer>
	);
}
