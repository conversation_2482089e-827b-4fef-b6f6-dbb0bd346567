/* eslint-disable @typescript-eslint/no-explicit-any */
// components/ui/ToggleSwitch.tsx
import React from "react";
import {
	Info,
	Sliders,
	Search,
	AlertTriangle,
	Settings,
	Eye,
	Zap,
	Save,
	RefreshCw,
	CheckCircle,
} from "lucide-react";
import Button from "@/components/Button";

interface ToggleSwitchProps {
	checked: boolean;
	onChange: (checked: boolean) => void;
	disabled?: boolean;
}

export const ToggleSwitch: React.FC<ToggleSwitchProps> = ({
	checked,
	onChange,
	disabled = false,
}) => (
	<label className="relative inline-flex items-center cursor-pointer">
		<input
			type="checkbox"
			checked={checked}
			onChange={(e) => onChange(e.target.checked)}
			disabled={disabled}
			className="sr-only peer"
		/>
		<div className="w-11 h-6 bg-input peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-border after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gradient-brand disabled:opacity-50 disabled:cursor-not-allowed"></div>
	</label>
);

interface SettingsSectionProps {
	title: string;
	children: React.ReactNode;
	isDarkMode: boolean;
}

export const SettingsSection: React.FC<SettingsSectionProps> = ({
	title,
	children,
}) => (
	<div className="space-y-6">
		<h4 className="text-lg font-semibold text-foreground">{title}</h4>
		<div className="space-y-4">{children}</div>
	</div>
);

interface SettingItemProps {
	label: string;
	description: string;
	control: React.ReactNode;
	isDarkMode: boolean;
}

export const SettingItem: React.FC<SettingItemProps> = ({
	label,
	description,
	control,
}) => (
	<div className="flex items-center justify-between py-4 border-b border-border last:border-b-0">
		<div className="flex-1 pr-4">
			<label className="block text-sm font-medium text-foreground">
				{label}
			</label>
			<p className="text-sm text-muted-foreground mt-1">{description}</p>
		</div>
		<div className="flex-shrink-0">{control}</div>
	</div>
);

interface InputFieldProps {
	label: string;
	description?: string;
	type: "number" | "select";
	value: any;
	onChange: (value: any) => void;
	isDarkMode: boolean;
	options?: { value: string; label: string }[];
	min?: number;
	max?: number;
	step?: number;
	className?: string;
}

export const InputField: React.FC<InputFieldProps> = ({
	label,
	description,
	type,
	value,
	onChange,
	options,
	min,
	max,
	step,
	className = "w-32",
}) => (
	<div className="space-y-2">
		<label className="block text-sm font-medium text-foreground">
			{label}
		</label>
		{type === "number" ? (
			<input
				type="number"
				min={min}
				max={max}
				step={step}
				value={value}
				onChange={(e) =>
					onChange(
						type === "number"
							? parseFloat(e.target.value) || 0
							: e.target.value
					)
				}
				className={`input-brand ${className}`}
			/>
		) : (
			<select
				value={value}
				onChange={(e) => onChange(e.target.value)}
				className={`input-brand ${className}`}>
				{options?.map((option) => (
					<option key={option.value} value={option.value}>
						{option.label}
					</option>
				))}
			</select>
		)}
		{description && (
			<p className="text-xs text-muted-foreground">{description}</p>
		)}
	</div>
);

interface TabNavigationProps {
	activeTab: string;
	onTabChange: (tab: string) => void;
	isDarkMode: boolean;
}

export const TabNavigation: React.FC<TabNavigationProps> = ({
	activeTab,
	onTabChange,
}) => {
	const tabs = [
		{ id: "overview", label: "Overview", icon: <Info size={16} /> },
		{ id: "weights", label: "AI Weights", icon: <Sliders size={16} /> },
		{ id: "scanning", label: "Scanning", icon: <Search size={16} /> },
		{
			id: "notifications",
			label: "Notifications",
			icon: <AlertTriangle size={16} />,
		},
		{ id: "display", label: "Display", icon: <Settings size={16} /> },
	];

	return (
		<div className="border-b border-border bg-card">
			<div className="flex space-x-1 overflow-x-auto px-6">
				{tabs.map((tab) => (
					<button
						key={tab.id}
						onClick={() => onTabChange(tab.id)}
						className={`flex items-center gap-2 py-4 px-3 border-b-2 font-medium text-sm whitespace-nowrap transition-all duration-200 ${
							activeTab === tab.id
								? "border-brand-primary text-brand-primary bg-brand-primary/5"
								: "border-transparent text-muted-foreground hover:text-foreground hover:border-border"
						}`}>
						{tab.icon}
						{tab.label}
					</button>
				))}
			</div>
		</div>
	);
};

interface HeaderAction {
	label: string;
	icon: React.ReactNode;
	variant: "primary" | "secondary";
	onClick?: () => void;
}

interface AISettingsHeaderProps {
	type: "source-ora" | "inspect-ora";
	hasChanges?: boolean;
	isLoading?: boolean;
	onSave?: () => void;
	onCancel?: () => void;
	isDarkMode?: boolean;
}

export const AISettingsHeader: React.FC<AISettingsHeaderProps> = ({
	type,
	hasChanges,
	isLoading,
	onSave,
	onCancel,
}) => {
	// Header actions defined inside the component based on type
	const getHeaderActions = (): HeaderAction[] => {
		if (type === "source-ora") {
			return [
				{
					label: "Refresh",
					icon: <RefreshCw size={16} />,
					variant: "secondary" as const,
					onClick: () => console.log("Refreshing..."),
				},
				{
					label: "Find More Sellers",
					icon: <Search size={16} />,
					variant: "primary" as const,
					onClick: () => console.log("Finding more sellers..."),
				},
			];
		} else if (type === "inspect-ora") {
			return [
				{
					label: "Refresh",
					icon: <RefreshCw size={16} />,
					variant: "secondary" as const,
					onClick: () => console.log("Refreshing..."),
				},
				{
					label: "Scan New Products",
					icon: <Search size={16} />,
					variant: "primary" as const,
					onClick: () => console.log("Scanning new products..."),
				},
			];
		}
		return [];
	};

	const headerActions = getHeaderActions();
	const config = {
		"inspect-ora": {
			icon: <Eye size={20} className="text-brand-secondary" />,
			title: "Inspect-Ora AI",
			description: "Product inspection and analysis AI configuration",
			gradient: "bg-brand-secondary/10",
		},
		"source-ora": {
			icon: <Zap size={20} className="text-brand-primary" />,
			title: "Source-Ora AI",
			description: "Product sourcing and opportunity AI configuration",
			gradient: "bg-brand-primary/10",
		},
	};

	const currentConfig = config[type];

	return (
		<div className="p-6 border-b border-border bg-card">
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-4">
					<div
						className={`p-3 rounded-lg border border-border ${currentConfig.gradient}`}>
						{currentConfig.icon}
					</div>
					<div>
						<h1 className="text-2xl font-bold text-foreground">
							{currentConfig.title}
						</h1>
						<p className="text-sm text-muted-foreground mt-1">
							{currentConfig.description}
						</p>
					</div>
				</div>

				<div className="flex items-center gap-3">
					{/* Render header actions if provided */}
					{headerActions.length > 0 &&
						headerActions.map((action, index) => (
							<button
								key={index}
								onClick={action.onClick}
								className={`inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
									action.variant === "primary"
										? "bg-brand-primary text-white hover:bg-brand-primary/90"
										: "bg-secondary text-secondary-foreground border border-border hover:bg-muted"
								}`}>
								{action.icon}
								{action.label}
							</button>
						))}

					{/* Render save/cancel buttons if there are changes */}
					{hasChanges && (
						<>
							<Button
								variant="ghost"
								size="sm"
								onClick={onCancel}>
								Cancel
							</Button>
							<Button
								variant="primary"
								size="sm"
								onClick={onSave}
								disabled={isLoading}
								className="flex items-center gap-2">
								{isLoading ? (
									<RefreshCw
										size={16}
										className="animate-spin"
									/>
								) : (
									<Save size={16} />
								)}
								Save Changes
							</Button>
						</>
					)}
				</div>
			</div>
		</div>
	);
};

interface SaveBannerProps {
	hasChanges: boolean;
	isLoading: boolean;
	onSave: () => void;
	onDiscard: () => void;
	isDarkMode: boolean;
}

export const SaveBanner: React.FC<SaveBannerProps> = ({
	hasChanges,
	isLoading,
	onSave,
	onDiscard,
}) => {
	if (!hasChanges) return null;

	return (
		<div className="sticky bottom-0 z-10 p-4 border-t border-border bg-card/95 backdrop-blur-sm">
			<div className="flex items-center justify-between max-w-7xl mx-auto">
				<div className="flex items-center gap-3">
					<div className="w-2 h-2 bg-brand-primary rounded-full animate-pulse"></div>
					<div className="flex items-center gap-2">
						<CheckCircle size={16} className="text-brand-primary" />
						<span className="text-sm font-medium text-foreground">
							You have unsaved changes
						</span>
					</div>
				</div>

				<div className="flex items-center gap-3">
					<Button variant="ghost" size="sm" onClick={onDiscard}>
						Discard
					</Button>
					<Button
						variant="primary"
						size="sm"
						onClick={onSave}
						disabled={isLoading}
						className="flex items-center gap-2">
						{isLoading ? (
							<RefreshCw size={16} className="animate-spin" />
						) : (
							<Save size={16} />
						)}
						Save Changes
					</Button>
				</div>
			</div>
		</div>
	);
};
