/* eslint-disable @typescript-eslint/no-explicit-any */
// components/extensions/ai-agent/AISettingsContainer.tsx
import React, { useState, useEffect } from "react";
import { AIWeights, ExtensionSettings } from "@/types/extension";
import ComingSoonPlaceholder from "@/components/coming-soon";

// Import reusable components
import {
	AISettingsHeader,
	InputField,
	SaveBanner,
	SettingItem,
	SettingsSection,
	TabNavigation,
	ToggleSwitch,
} from "./ai-reusable-components";
import WeightConfigurationPanel from "./weight-config-panel";
import InspectOraOverview from "../inspectora-overview";
import SourceOraOverview from "../sourceora-overview";

interface AISettingsContainerProps {
	type: "inspect-ora" | "source-ora";
	settings: ExtensionSettings;
	onUpdateSettings: (settings: Partial<ExtensionSettings>) => Promise<void>;
	onUpdateWeights: (weights: AIWeights) => Promise<void>;
	isDarkMode?: boolean;
	onNavigateToSourceOra?: () => void;
}

const AISettingsContainer: React.FC<AISettingsContainerProps> = ({
	type,
	settings,
	onUpdateSettings,
	onUpdateWeights,
	isDarkMode = true,
}) => {
	const [weights, setWeights] = useState<AIWeights>(settings.aiWeights);
	const [tempSettings, setTempSettings] = useState(settings);
	const [activeTab, setActiveTab] = useState<
		"overview" | "weights" | "scanning" | "notifications" | "display"
	>("overview");
	const [isLoading, setIsLoading] = useState(false);
	const [hasChanges, setHasChanges] = useState(false);

	const defaultWeights: AIWeights = {
		profitMargin: 25,
		salesVelocity: 20,
		competitionLevel: 15,
		priceStability: 10,
		seasonality: 8,
		bsrTrend: 7,
		reviewScore: 5,
		brandRecognition: 4,
		returnRate: 3,
		fbaFees: 3,
	};

	useEffect(() => {
		const hasWeightChanges =
			JSON.stringify(weights) !== JSON.stringify(settings.aiWeights);
		const hasSettingChanges =
			JSON.stringify(tempSettings) !== JSON.stringify(settings);
		setHasChanges(hasWeightChanges || hasSettingChanges);
	}, [weights, tempSettings, settings]);

	const handleWeightChange = (key: keyof AIWeights, value: number) => {
		setWeights((prev) => ({ ...prev, [key]: value }));
	};

	const handleSettingChange = (
		section: keyof ExtensionSettings,
		key: string,
		value: any
	) => {
		setTempSettings((prev) => ({
			...prev,
			[section]: {
				...prev[section],
				[key]: value,
			},
		}));
	};

	const resetToDefaults = () => {
		setWeights(defaultWeights);
	};

	const saveChanges = async () => {
		setIsLoading(true);
		try {
			await onUpdateWeights(weights);
			await onUpdateSettings(tempSettings);
			setHasChanges(false);
		} catch (error) {
			console.error("Failed to save settings:", error);
		} finally {
			setIsLoading(false);
		}
	};

	const cancelChanges = () => {
		setWeights(settings.aiWeights);
		setTempSettings(settings);
	};

	const handleTabChange = (tab: string) => {
		setActiveTab(
			tab as
				| "overview"
				| "weights"
				| "scanning"
				| "notifications"
				| "display"
		);
	};

	const renderTabContent = () => {
		switch (activeTab) {
			case "overview":
				if (type === "inspect-ora") {
					return (
						<InspectOraOverview
							isDarkMode={isDarkMode}
							onNavigateToSourceOra={() => {
								// You can add navigation logic here if needed
								console.log("Navigate to Source-Ora");
							}}
						/>
					);
				} else {
					return <SourceOraOverview isDarkMode={isDarkMode} />;
				}

			case "weights":
				return (
					<WeightConfigurationPanel
						weights={weights}
						onWeightChange={handleWeightChange}
						onResetToDefaults={resetToDefaults}
						isDarkMode={isDarkMode}
					/>
				);

			case "scanning":
				return (
					<div className="space-y-6">
						<SettingsSection
							title="Automatic Scanning"
							isDarkMode={isDarkMode}>
							<div className="space-y-4">
								<SettingItem
									label="Enable Auto-Scan"
									description="Automatically scan products when browsing Amazon"
									control={
										<ToggleSwitch
											checked={
												tempSettings.scanning.autoScan
											}
											onChange={(checked) =>
												handleSettingChange(
													"scanning",
													"autoScan",
													checked
												)
											}
										/>
									}
									isDarkMode={isDarkMode}
								/>

								<InputField
									label="Scan Interval (minutes)"
									description="How often to check for new products to scan"
									type="number"
									value={tempSettings.scanning.scanInterval}
									onChange={(value) =>
										handleSettingChange(
											"scanning",
											"scanInterval",
											value
										)
									}
									isDarkMode={isDarkMode}
									min={1}
									max={60}
								/>

								<InputField
									label="Batch Size"
									description="Number of products to analyze at once"
									type="number"
									value={tempSettings.scanning.batchSize}
									onChange={(value) =>
										handleSettingChange(
											"scanning",
											"batchSize",
											value
										)
									}
									isDarkMode={isDarkMode}
									min={10}
									max={100}
								/>
							</div>
						</SettingsSection>

						<SettingsSection
							title="Alert Thresholds"
							isDarkMode={isDarkMode}>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<InputField
									label="Minimum Profit ($)"
									type="number"
									value={
										tempSettings.scanning.alertThresholds
											.minProfit
									}
									onChange={(value) =>
										handleSettingChange(
											"scanning",
											"alertThresholds",
											{
												...tempSettings.scanning
													.alertThresholds,
												minProfit: value,
											}
										)
									}
									isDarkMode={isDarkMode}
									step={0.01}
									className="w-full"
								/>

								<InputField
									label="Minimum ROI (%)"
									type="number"
									value={
										tempSettings.scanning.alertThresholds
											.minROI
									}
									onChange={(value) =>
										handleSettingChange(
											"scanning",
											"alertThresholds",
											{
												...tempSettings.scanning
													.alertThresholds,
												minROI: value,
											}
										)
									}
									isDarkMode={isDarkMode}
									className="w-full"
								/>

								<InputField
									label="Maximum BSR"
									type="number"
									value={
										tempSettings.scanning.alertThresholds
											.maxBSR
									}
									onChange={(value) =>
										handleSettingChange(
											"scanning",
											"alertThresholds",
											{
												...tempSettings.scanning
													.alertThresholds,
												maxBSR: value,
											}
										)
									}
									isDarkMode={isDarkMode}
									className="w-full"
								/>

								<InputField
									label="Minimum Rating"
									type="number"
									value={
										tempSettings.scanning.alertThresholds
											.minRating
									}
									onChange={(value) =>
										handleSettingChange(
											"scanning",
											"alertThresholds",
											{
												...tempSettings.scanning
													.alertThresholds,
												minRating: value,
											}
										)
									}
									isDarkMode={isDarkMode}
									min={1}
									max={5}
									step={0.1}
									className="w-full"
								/>
							</div>
						</SettingsSection>
					</div>
				);

			case "notifications":
				return (
					<SettingsSection
						title="Notification Preferences"
						isDarkMode={isDarkMode}>
						<div className="space-y-4">
							{[
								{
									key: "priceAlerts",
									label: "Price Alerts",
									description:
										"Notify when product prices change significantly",
								},
								{
									key: "profitAlerts",
									label: "Profit Alerts",
									description:
										"Alert when high-profit opportunities are detected",
								},
								{
									key: "competitorAlerts",
									label: "Competitor Alerts",
									description:
										"Notify about competitor price changes and new sellers",
								},
								{
									key: "lowTokenWarning",
									label: "Low Token Warning",
									description:
										"Warn when approaching token usage limits",
								},
								{
									key: "emailNotifications",
									label: "Email Notifications",
									description:
										"Send important alerts to your email",
								},
							].map((item) => (
								<SettingItem
									key={item.key}
									label={item.label}
									description={item.description}
									control={
										<ToggleSwitch
											checked={
												tempSettings.notifications[
													item.key as keyof typeof tempSettings.notifications
												]
											}
											onChange={(checked) =>
												handleSettingChange(
													"notifications",
													item.key,
													checked
												)
											}
										/>
									}
									isDarkMode={isDarkMode}
								/>
							))}
						</div>
					</SettingsSection>
				);

			case "display":
				return (
					<SettingsSection
						title="Display Settings"
						isDarkMode={isDarkMode}>
						<div className="space-y-4">
							<InputField
								label="Default Currency"
								type="select"
								value={tempSettings.display.defaultCurrency}
								onChange={(value) =>
									handleSettingChange(
										"display",
										"defaultCurrency",
										value
									)
								}
								isDarkMode={isDarkMode}
								options={[
									{ value: "USD", label: "USD ($)" },
									{ value: "GBP", label: "GBP (£)" },
									{ value: "EUR", label: "EUR (€)" },
								]}
							/>

							{[
								{
									key: "showProfitability",
									label: "Show Profitability",
									description:
										"Display profit calculations on product pages",
								},
								{
									key: "showAIInsights",
									label: "Show AI Insights",
									description:
										"Display AI recommendations and analysis",
								},
								{
									key: "compactMode",
									label: "Compact Mode",
									description:
										"Use a more compact display layout",
								},
								{
									key: "colorCoding",
									label: "Color Coding",
									description:
										"Use colors to indicate profit levels and recommendations",
								},
							].map((item) => (
								<SettingItem
									key={item.key}
									label={item.label}
									description={item.description}
									control={
										<ToggleSwitch
											checked={
												!!tempSettings.display[
													item.key as keyof typeof tempSettings.display
												]
											}
											onChange={(checked) =>
												handleSettingChange(
													"display",
													item.key,
													checked
												)
											}
										/>
									}
									isDarkMode={isDarkMode}
								/>
							))}
						</div>
					</SettingsSection>
				);

			default:
				return (
					<ComingSoonPlaceholder
						icon="🤖"
						title={`${
							type === "inspect-ora"
								? "Inspect-Ora"
								: "Source-Ora"
						} AI Overview`}
						description="Comprehensive AI overview and insights coming soon"
						isDarkMode={isDarkMode}
					/>
				);
		}
	};

	return (
		<div className="card-brand overflow-hidden">
			<AISettingsHeader
				type={type}
				hasChanges={hasChanges}
				isLoading={isLoading}
				onSave={saveChanges}
				onCancel={cancelChanges}
				isDarkMode={isDarkMode}
			/>

			<TabNavigation
				activeTab={activeTab}
				onTabChange={handleTabChange}
				isDarkMode={isDarkMode}
			/>

			<div className="p-6 bg-background">{renderTabContent()}</div>

			<SaveBanner
				hasChanges={hasChanges}
				isLoading={isLoading}
				onSave={saveChanges}
				onDiscard={cancelChanges}
				isDarkMode={isDarkMode}
			/>
		</div>
	);
};

export default AISettingsContainer;
