import React from "react";
import { AIWeights, ExtensionSettings } from "@/types/extension";
import AISettingsContainer from "./ai-settings-container";

interface InspectOraAISettingsProps {
	settings: ExtensionSettings;
	onUpdateSettings: (settings: Partial<ExtensionSettings>) => Promise<void>;
	onUpdateWeights: (weights: AIWeights) => Promise<void>;
	isDarkMode?: boolean;
	onNavigateToSourceOra?: () => void;
}

const InspectOraAISettings: React.FC<InspectOraAISettingsProps> = ({
	settings,
	onUpdateSettings,
	onUpdateWeights,
	isDarkMode = true,
}) => {
	return (
		<AISettingsContainer
			type="inspect-ora"
			settings={settings}
			onUpdateSettings={onUpdateSettings}
			onUpdateWeights={onUpdateWeights}
			isDarkMode={isDarkMode}
		/>
	);
};

export default InspectOraAISettings;
