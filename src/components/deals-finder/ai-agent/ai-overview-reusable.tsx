/* eslint-disable @typescript-eslint/no-explicit-any */
// components/ui/PageHeader.tsx
import React, { useState } from "react";
import Button from "@/components/Button";

import {
	useReactTable,
	getCoreRowModel,
	getFilteredRowModel,
	getSortedRowModel,
	getPaginationRowModel,
	flexRender,
	ColumnDef,
} from "@tanstack/react-table";
import Image from "next/image";

interface ActionButton {
	label: string;
	icon: React.ReactNode;
	variant?: "primary" | "secondary" | "ghost";
	onClick?: () => void;
	disabled?: boolean;
}

interface PageHeaderProps {
	icon: React.ReactNode;
	title: string;
	description: string;
	actions?: ActionButton[];
	isDarkMode?: boolean;
}

export const PageHeader: React.FC<PageHeaderProps> = ({
	icon,
	title,
	description,
	actions = [],
}) => {
	return (
		<div className="flex items-center justify-between">
			<div className="flex items-center gap-3">
				<div className="bg-gradient-brand-subtle p-2 rounded-lg">
					{icon}
				</div>
				<div>
					<h2 className="text-2xl font-bold text-foreground">
						{title}
					</h2>
					<p className="text-sm text-muted-foreground">
						{description}
					</p>
				</div>
			</div>
			<div className="flex items-center gap-2">
				{actions.map((action, index) => (
					<Button
						key={index}
						variant={action.variant || "secondary"}
						size="sm"
						onClick={action.onClick}
						disabled={action.disabled}
						className="flex items-center gap-2">
						{action.icon}
						{action.label}
					</Button>
				))}
			</div>
		</div>
	);
};

interface StatCardProps {
	label: string;
	value: string | number;
	icon: React.ReactNode;
	color?: "blue" | "red" | "green" | "yellow" | "purple" | "orange";
	isDarkMode?: boolean;
}

export const StatCard: React.FC<StatCardProps> = ({
	label,
	value,
	icon,
	color = "blue",
}) => {
	const colorClasses = {
		blue: "text-brand-secondary",
		red: "text-red-500",
		green: "text-brand-primary",
		yellow: "text-yellow-500",
		purple: "text-purple-500",
		orange: "text-orange-500",
	};

	return (
		<div className="card-brand p-4">
			<div className="flex items-center gap-2 mb-2">
				<span className={colorClasses[color]}>{icon}</span>
				<span className="text-sm text-muted-foreground">{label}</span>
			</div>
			<div className="text-2xl font-bold text-foreground">{value}</div>
		</div>
	);
};

interface StatsGridProps {
	children: React.ReactNode;
	columns?: 1 | 2 | 3 | 4;
}

export const StatsGrid: React.FC<StatsGridProps> = ({
	children,
	columns = 4,
}) => {
	const gridClasses = {
		1: "grid-cols-1",
		2: "grid-cols-1 md:grid-cols-2",
		3: "grid-cols-1 md:grid-cols-3",
		4: "grid-cols-1 md:grid-cols-4",
	};

	return (
		<div className={`grid ${gridClasses[columns]} gap-4`}>{children}</div>
	);
};

interface StatusBadgeProps {
	status: "completed" | "analyzing" | "flagged" | "pending";
	size?: "sm" | "md";
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
	status,
	size = "sm",
}) => {
	const statusColors = {
		completed:
			"text-brand-primary bg-brand-primary/10 border-brand-primary/20",
		analyzing:
			"text-brand-secondary bg-brand-secondary/10 border-brand-secondary/20",
		flagged: "text-red-500 bg-red-500/10 border-red-500/20",
		pending: "text-muted-foreground bg-muted border-border",
	};

	const sizeClasses = {
		sm: "px-2.5 py-0.5 text-xs",
		md: "px-3 py-1 text-sm",
	};

	return (
		<span
			className={`inline-flex items-center rounded-full font-medium border ${statusColors[status]} ${sizeClasses[size]}`}>
			{status.charAt(0).toUpperCase() + status.slice(1)}
		</span>
	);
};

interface ProductImageProps {
	src: string;
	alt: string;
	asin: string;
	size?: "sm" | "md" | "lg";
	className?: string;
}

export const ProductImage: React.FC<ProductImageProps> = ({
	src,
	alt,
	asin,
	size = "sm",
	className = "",
}) => {
	const [imageError, setImageError] = useState(false);

	const sizeMap = {
		sm: { sizeClass: "w-12 h-12", width: 48, height: 48 },
		md: { sizeClass: "w-16 h-16", width: 64, height: 64 },
		lg: { sizeClass: "w-24 h-24", width: 96, height: 96 },
	};

	const { sizeClass, width, height } = sizeMap[size];

	const handleImageError = () => {
		setImageError(true);
	};

	return (
		<Image
			src={
				imageError
					? `https://via.placeholder.com/${width}x${height}?text=${asin.slice(
							0,
							2
					  )}`
					: src
			}
			alt={alt}
			width={width}
			height={height}
			className={`${sizeClass} rounded-lg object-cover border border-border ${className}`}
			onError={handleImageError}
		/>
	);
};

interface ActionItem {
	label: string;
	icon: React.ReactNode;
	onClick?: () => void;
	disabled?: boolean;
	variant?: "primary" | "secondary" | "ghost";
	color?: string;
	title?: string;
}

interface ActionButtonsProps {
	actions: ActionItem[];
	size?: "sm" | "md";
}

export const ActionButtons: React.FC<ActionButtonsProps> = ({
	actions,
	size = "sm",
}) => {
	return (
		<div className="flex items-center gap-2">
			{actions.map((action, index) => (
				<Button
					key={index}
					variant={action.variant || "ghost"}
					size={size}
					onClick={action.onClick}
					disabled={action.disabled}
					className={`${action.color || ""} ${
						action.disabled ? "disabled:opacity-50" : ""
					}`}
					title={action.title}>
					{action.icon}
					{action.label}
				</Button>
			))}
		</div>
	);
};

interface DataTableProps<T> {
	data: T[];
	columns: ColumnDef<T, any>[];
	title?: string;
	subtitle?: string;
	isDarkMode?: boolean;
	pageSize?: number;
}

export function DataTable<T>({
	data,
	columns,
	title,
	subtitle,
	pageSize = 10,
}: DataTableProps<T>) {
	const table = useReactTable({
		data,
		columns,
		getCoreRowModel: getCoreRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		initialState: {
			pagination: {
				pageSize,
			},
		},
	});

	return (
		<div className="card-brand overflow-hidden">
			{(title || subtitle) && (
				<div className="p-4 border-b border-border">
					{title && (
						<h3 className="font-semibold text-foreground">
							{title}
						</h3>
					)}
					{subtitle && (
						<p className="text-sm text-muted-foreground">
							{subtitle}
						</p>
					)}
				</div>
			)}

			<div className="overflow-x-auto">
				<table className="w-full">
					<thead className="bg-muted">
						{table.getHeaderGroups().map((headerGroup) => (
							<tr key={headerGroup.id}>
								{headerGroup.headers.map((header) => (
									<th
										key={header.id}
										className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider cursor-pointer hover:bg-opacity-80 text-muted-foreground"
										onClick={header.column.getToggleSortingHandler()}>
										{header.isPlaceholder
											? null
											: flexRender(
													header.column.columnDef
														.header,
													header.getContext()
											  )}
									</th>
								))}
							</tr>
						))}
					</thead>
					<tbody className="divide-y divide-border">
						{table.getRowModel().rows.map((row) => (
							<tr
								key={row.id}
								className="hover:bg-muted/50 transition-colors">
								{row.getVisibleCells().map((cell) => (
									<td key={cell.id} className="px-4 py-4">
										{flexRender(
											cell.column.columnDef.cell,
											cell.getContext()
										)}
									</td>
								))}
							</tr>
						))}
					</tbody>
				</table>
			</div>

			{/* Pagination */}
			<div className="px-4 py-3 border-t border-border">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<Button
							variant="ghost"
							size="sm"
							onClick={() => table.previousPage()}
							disabled={!table.getCanPreviousPage()}>
							Previous
						</Button>
						<Button
							variant="ghost"
							size="sm"
							onClick={() => table.nextPage()}
							disabled={!table.getCanNextPage()}>
							Next
						</Button>
					</div>
					<span className="text-sm text-muted-foreground">
						Page {table.getState().pagination.pageIndex + 1} of{" "}
						{table.getPageCount()}
					</span>
				</div>
			</div>
		</div>
	);
}
