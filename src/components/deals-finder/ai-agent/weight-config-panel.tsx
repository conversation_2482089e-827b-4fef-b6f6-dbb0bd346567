import React from "react";
import {
	RotateCcw,
	TrendingUp,
	DollarSign,
	Users,
	BarChart3,
	Star,
	Package,
	Calendar,
	Shield,
	RefreshCw,
} from "lucide-react";
import { AIWeights } from "@/types/extension";
import Button from "@/components/Button";

interface WeightConfigurationPanelProps {
	weights: AIWeights;
	onWeightChange: (key: keyof AIWeights, value: number) => void;
	onResetToDefaults: () => void;
	isDarkMode: boolean;
}

const WeightConfigurationPanel: React.FC<WeightConfigurationPanelProps> = ({
	weights,
	onWeightChange,
	onResetToDefaults,
}) => {
	const getTotalWeight = () => {
		return Object.values(weights).reduce((sum, weight) => sum + weight, 0);
	};

	const getWeightPercentage = (weight: number) => {
		const total = getTotalWeight();
		return total > 0 ? (weight / total) * 100 : 0;
	};

	const weightConfigs = [
		{
			key: "profitMargin" as keyof AIWeights,
			label: "Profit Margin",
			description:
				"How much the product's profit margin influences the AI recommendation",
			icon: <DollarSign size={16} className="text-brand-primary" />,
			color: "text-brand-primary",
		},
		{
			key: "salesVelocity" as keyof AIWeights,
			label: "Sales Velocity",
			description:
				"Product demand and sales frequency based on BSR and category performance",
			icon: <TrendingUp size={16} className="text-brand-secondary" />,
			color: "text-brand-secondary",
		},
		{
			key: "competitionLevel" as keyof AIWeights,
			label: "Competition Level",
			description: "Number of sellers and market saturation analysis",
			icon: <Users size={16} className="text-red-500" />,
			color: "text-red-500",
		},
		{
			key: "priceStability" as keyof AIWeights,
			label: "Price Stability",
			description: "How stable the product price has been over time",
			icon: <BarChart3 size={16} className="text-purple-500" />,
			color: "text-purple-500",
		},
		{
			key: "seasonality" as keyof AIWeights,
			label: "Seasonality",
			description: "Seasonal demand patterns and timing considerations",
			icon: <Calendar size={16} className="text-orange-500" />,
			color: "text-orange-500",
		},
		{
			key: "bsrTrend" as keyof AIWeights,
			label: "BSR Trend",
			description: "Best Seller Rank movement and trends over time",
			icon: <TrendingUp size={16} className="text-indigo-500" />,
			color: "text-indigo-500",
		},
		{
			key: "reviewScore" as keyof AIWeights,
			label: "Review Score",
			description: "Product rating and review quality indicators",
			icon: <Star size={16} className="text-yellow-500" />,
			color: "text-yellow-500",
		},
		{
			key: "brandRecognition" as keyof AIWeights,
			label: "Brand Recognition",
			description: "Brand strength and market presence influence",
			icon: <Shield size={16} className="text-pink-500" />,
			color: "text-pink-500",
		},
		{
			key: "returnRate" as keyof AIWeights,
			label: "Return Rate",
			description:
				"Historical return and refund patterns for similar products",
			icon: <RefreshCw size={16} className="text-muted-foreground" />,
			color: "text-muted-foreground",
		},
		{
			key: "fbaFees" as keyof AIWeights,
			label: "FBA Fees",
			description: "Amazon fulfillment fees and cost considerations",
			icon: <Package size={16} className="text-teal-500" />,
			color: "text-teal-500",
		},
	];

	return (
		<div className="space-y-6">
			{/* Weight Summary */}
			<div className="card-brand p-4">
				<div className="flex items-center justify-between mb-3">
					<h4 className="font-semibold text-foreground">
						Weight Distribution
					</h4>
					<div className="text-sm text-muted-foreground">
						Total: {getTotalWeight()}%
					</div>
				</div>

				<div className="grid grid-cols-2 md:grid-cols-5 gap-2 mb-4">
					{Object.entries(weights)
						.sort(([, a], [, b]) => b - a)
						.slice(0, 5)
						.map(([key, value]) => {
							const config = weightConfigs.find(
								(w) => w.key === key
							);
							return (
								<div key={key} className="text-center">
									<div
										className={`text-lg font-bold ${
											config?.color ||
											"text-muted-foreground"
										}`}>
										{value}%
									</div>
									<div className="text-xs text-muted-foreground">
										{config?.label || key}
									</div>
								</div>
							);
						})}
				</div>

				<div className="flex items-center gap-2">
					<Button
						variant="secondary"
						size="sm"
						onClick={onResetToDefaults}
						className="flex items-center gap-2">
						<RotateCcw size={14} />
						Reset to Defaults
					</Button>
					<div className="text-xs text-muted-foreground">
						Resets all weights to recommended values
					</div>
				</div>
			</div>

			{/* Weight Controls */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{weightConfigs.map((config) => (
					<div key={config.key} className="card-brand p-4">
						<div className="flex items-start justify-between mb-3">
							<div className="flex items-center gap-2">
								{config.icon}
								<h5 className="font-medium text-foreground">
									{config.label}
								</h5>
							</div>
							<div className="text-right">
								<div
									className={`text-lg font-bold ${config.color}`}>
									{weights[config.key]}%
								</div>
								<div className="text-xs text-muted-foreground">
									{getWeightPercentage(
										weights[config.key]
									).toFixed(1)}
									% of total
								</div>
							</div>
						</div>

						<p className="text-sm mb-3 text-muted-foreground">
							{config.description}
						</p>

						<div className="space-y-2">
							<input
								type="range"
								min="0"
								max="50"
								value={weights[config.key]}
								onChange={(e) =>
									onWeightChange(
										config.key,
										parseInt(e.target.value)
									)
								}
								className="w-full h-2 bg-input rounded-lg appearance-none cursor-pointer slider
                  [&::-webkit-slider-track]:bg-input [&::-webkit-slider-track]:rounded-lg [&::-webkit-slider-track]:h-2
                  [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:h-4 [&::-webkit-slider-thumb]:w-4
                  [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:bg-brand-primary
                  [&::-webkit-slider-thumb]:cursor-pointer [&::-webkit-slider-thumb]:shadow-sm
                  [&::-moz-range-track]:bg-input [&::-moz-range-track]:rounded-lg [&::-moz-range-track]:h-2
                  [&::-moz-range-track]:border-0
                  [&::-moz-range-thumb]:appearance-none [&::-moz-range-thumb]:h-4 [&::-moz-range-thumb]:w-4
                  [&::-moz-range-thumb]:rounded-full [&::-moz-range-thumb]:bg-brand-primary
                  [&::-moz-range-thumb]:cursor-pointer [&::-moz-range-thumb]:border-0"
							/>
							<div className="flex justify-between text-xs text-muted-foreground">
								<span>0%</span>
								<span>25%</span>
								<span>50%</span>
							</div>
						</div>
					</div>
				))}
			</div>
		</div>
	);
};

export default WeightConfigurationPanel;
