import React, { useState, useRef, useEffect } from "react";
import { Send, Mic, MicOff } from "lucide-react";

interface ChatMessage {
	id: string;
	message: string;
	sentTime: string;
	sender: string;
	direction: "incoming" | "outgoing";
	type: "text" | "audio";
	audioUrl?: string;
	isRead?: boolean;
}

interface ProfessionalChatInterfaceProps {
	messages: ChatMessage[];
	onSendMessage: (message: string) => void;
	onAudioRecorded?: (blob: Blob) => void;
	isTyping?: boolean;
	isDarkMode?: boolean;
	height?: string;
	placeholder?: string;
	showAudioRecorder?: boolean;
	aiName?: string;
	userAvatar?: string;
	aiAvatar?: string;
	className?: string;
}

const ProfessionalChatInterface: React.FC<ProfessionalChatInterfaceProps> = ({
	messages,
	onSendMessage,
	onAudioRecorded,
	isTyping = false,
	height = "h-96",
	placeholder = "Type your message...",
	showAudioRecorder = true,
	userAvatar,
	aiAvatar,
	className = "",
}) => {
	const [newMessage, setNewMessage] = useState("");
	const [isRecording, setIsRecording] = useState(false);
	const [recordingTime, setRecordingTime] = useState(0);
	const [audioPermission, setAudioPermission] = useState<boolean | null>(
		null
	);

	const mediaRecorderRef = useRef<MediaRecorder | null>(null);
	const audioChunksRef = useRef<Blob[]>([]);
	const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);
	const messagesEndRef = useRef<HTMLDivElement>(null);
	const textareaRef = useRef<HTMLTextAreaElement>(null);

	// Auto-scroll to bottom when new messages arrive
	useEffect(() => {
		messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
	}, [messages, isTyping]);

	// Auto-resize textarea
	useEffect(() => {
		if (textareaRef.current) {
			textareaRef.current.style.height = "auto";
			textareaRef.current.style.height = `${Math.min(
				textareaRef.current.scrollHeight,
				120
			)}px`;
		}
	}, [newMessage]);

	// Request audio permission on mount
	useEffect(() => {
		if (showAudioRecorder) {
			navigator.mediaDevices
				.getUserMedia({ audio: true })
				.then(() => setAudioPermission(true))
				.catch(() => setAudioPermission(false));
		}
	}, [showAudioRecorder]);

	const startRecording = async () => {
		try {
			const stream = await navigator.mediaDevices.getUserMedia({
				audio: true,
			});
			const mediaRecorder = new MediaRecorder(stream, {
				mimeType: "audio/webm;codecs=opus",
			});

			mediaRecorderRef.current = mediaRecorder;
			audioChunksRef.current = [];

			mediaRecorder.ondataavailable = (event) => {
				if (event.data.size > 0) {
					audioChunksRef.current.push(event.data);
				}
			};

			mediaRecorder.onstop = () => {
				const audioBlob = new Blob(audioChunksRef.current, {
					type: "audio/webm",
				});
				if (onAudioRecorded) {
					onAudioRecorded(audioBlob);
				}
				stream.getTracks().forEach((track) => track.stop());
			};

			mediaRecorder.start(100);
			setIsRecording(true);
			setRecordingTime(0);

			// Start timer
			recordingTimerRef.current = setInterval(() => {
				setRecordingTime((prev) => prev + 1);
			}, 1000);
		} catch (error) {
			console.error("Error starting recording:", error);
			setAudioPermission(false);
		}
	};

	const stopRecording = () => {
		if (mediaRecorderRef.current && isRecording) {
			mediaRecorderRef.current.stop();
			setIsRecording(false);
			setRecordingTime(0);

			if (recordingTimerRef.current) {
				clearInterval(recordingTimerRef.current);
				recordingTimerRef.current = null;
			}
		}
	};

	const handleSendMessage = () => {
		if (newMessage.trim()) {
			onSendMessage(newMessage.trim());
			setNewMessage("");
		}
	};

	const handleKeyPress = (e: React.KeyboardEvent) => {
		if (e.key === "Enter" && !e.shiftKey) {
			e.preventDefault();
			handleSendMessage();
		}
	};

	const formatTime = (seconds: number) => {
		const mins = Math.floor(seconds / 60);
		const secs = seconds % 60;
		return `${mins}:${secs.toString().padStart(2, "0")}`;
	};

	const getAvatar = (direction: "incoming" | "outgoing") => {
		if (direction === "outgoing") {
			return (
				userAvatar || (
					<div className="w-8 h-8 bg-gradient-brand rounded-full flex items-center justify-center text-white text-sm font-medium">
						U
					</div>
				)
			);
		} else {
			return (
				aiAvatar || (
					<div className="w-8 h-8 bg-gradient-brand-reverse rounded-full flex items-center justify-center text-white text-sm font-medium">
						AI
					</div>
				)
			);
		}
	};

	return (
		<div
			className={`flex flex-col ${height} bg-card border border-border rounded-lg overflow-hidden ${className}`}>
			{/* Messages Area */}
			<div className="flex-1 overflow-y-auto p-3 space-y-3 bg-background">
				{messages.map((message) => (
					<div
						key={message.id}
						className={`flex gap-2 ${
							message.direction === "outgoing"
								? "flex-row-reverse"
								: "flex-row"
						}`}>
						{/* Avatar */}
						<div className="flex-shrink-0">
							{getAvatar(message.direction)}
						</div>

						{/* Message Bubble */}
						<div
							className={`flex flex-col max-w-xs lg:max-w-sm ${
								message.direction === "outgoing"
									? "items-end"
									: "items-start"
							}`}>
							{/* Message Content */}
							<div
								className={`relative px-3 py-2 rounded-2xl shadow-sm ${
									message.direction === "outgoing"
										? "bg-gradient-brand text-white rounded-br-md font-medium"
										: "bg-card border border-border rounded-bl-md text-foreground"
								}`}>
								{message.type === "audio" &&
								message.audioUrl ? (
									<div className="flex items-center gap-2 min-w-[180px]">
										<button
											onClick={() => {
												const audio = new Audio(
													message.audioUrl
												);
												audio.play();
											}}
											className="p-1.5 bg-white/20 rounded-full hover:bg-white/30 transition-colors">
											<div className="w-3 h-3 border-l-2 border-white ml-0.5"></div>
										</button>

										{/* Audio Waveform */}
										<div className="flex-1 flex items-center gap-0.5 h-6">
											{[...Array(10)].map((_, i) => (
												<div
													key={i}
													className="w-0.5 bg-current opacity-60 rounded-full animate-pulse"
													style={{
														height: `${
															Math.random() * 16 +
															8
														}px`,
														animationDelay: `${
															i * 100
														}ms`,
													}}></div>
											))}
										</div>

										<span className="text-xs opacity-70">
											0:
											{Math.floor(Math.random() * 30 + 5)}
										</span>
									</div>
								) : (
									<p className="text-sm leading-relaxed whitespace-pre-wrap break-words">
										{message.message}
									</p>
								)}
							</div>

							{/* Timestamp */}
							<p className="text-xs mt-1 px-1 text-muted-foreground">
								{message.sentTime}
								{message.direction === "outgoing" && (
									<span className="ml-1">
										{message.isRead ? "✓✓" : "✓"}
									</span>
								)}
							</p>
						</div>
					</div>
				))}

				{/* Typing Indicator */}
				{isTyping && (
					<div className="flex gap-2">
						<div className="flex-shrink-0">
							{getAvatar("incoming")}
						</div>
						<div className="px-3 py-2 rounded-2xl rounded-bl-md bg-card border border-border">
							<div className="flex space-x-1">
								<div
									className="w-2 h-2 rounded-full animate-pulse bg-muted-foreground"
									style={{ animationDelay: "0ms" }}></div>
								<div
									className="w-2 h-2 rounded-full animate-pulse bg-muted-foreground"
									style={{ animationDelay: "150ms" }}></div>
								<div
									className="w-2 h-2 rounded-full animate-pulse bg-muted-foreground"
									style={{ animationDelay: "300ms" }}></div>
							</div>
						</div>
					</div>
				)}

				<div ref={messagesEndRef} />
			</div>

			{/* Recording Indicator */}
			{isRecording && (
				<div className="px-3 py-2 border-t border-border flex items-center justify-center gap-2 bg-destructive/10">
					<div className="w-2 h-2 bg-destructive rounded-full animate-pulse"></div>
					<span className="text-sm font-medium text-destructive">
						Recording {formatTime(recordingTime)}
					</span>
				</div>
			)}

			{/* Message Input */}
			<div className="p-3 border-t border-border bg-card">
				<div className="flex items-end gap-2 p-2 rounded-lg border border-input bg-background transition-all duration-200 focus-within:border-brand-primary">
					{/* Text Input */}
					<div className="flex-1">
						<textarea
							ref={textareaRef}
							value={newMessage}
							onChange={(e) => setNewMessage(e.target.value)}
							onKeyPress={handleKeyPress}
							placeholder={placeholder}
							rows={1}
							className="w-full resize-none outline-none bg-transparent text-foreground placeholder-muted-foreground text-sm"
							style={{ minHeight: "20px", maxHeight: "80px" }}
						/>
					</div>

					{/* Send/Audio Button */}
					{newMessage.trim() ? (
						<button
							onClick={handleSendMessage}
							className="p-2 bg-brand-primary text-white rounded-lg hover:bg-brand-primary/90 transition-colors">
							<Send size={16} />
						</button>
					) : showAudioRecorder && audioPermission ? (
						<div className="relative">
							<button
								onMouseDown={startRecording}
								onMouseUp={stopRecording}
								onMouseLeave={stopRecording}
								onTouchStart={startRecording}
								onTouchEnd={stopRecording}
								className={`p-2 rounded-lg transition-all duration-200 ${
									isRecording
										? "bg-destructive text-white transform scale-110"
										: "bg-brand-primary text-white hover:bg-brand-primary/90"
								}`}
								title={
									isRecording
										? "Release to send"
										: "Hold to record"
								}>
								{isRecording ? (
									<MicOff size={16} />
								) : (
									<Mic size={16} />
								)}
							</button>

							{/* Recording Indicator */}
							{isRecording && (
								<div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-destructive text-white px-2 py-1 rounded text-xs font-medium">
									{formatTime(recordingTime)}
								</div>
							)}
						</div>
					) : (
						<button
							disabled
							className="p-2 bg-muted text-muted-foreground rounded-lg cursor-not-allowed"
							title="Audio recording not available">
							<Mic size={16} />
						</button>
					)}
				</div>

				{/* Helper Text */}
				<p className="text-xs mt-1 text-center text-muted-foreground">
					Press Enter to send
					{showAudioRecorder &&
						audioPermission &&
						" • Hold mic to record"}
				</p>
			</div>
		</div>
	);
};

export default ProfessionalChatInterface;
