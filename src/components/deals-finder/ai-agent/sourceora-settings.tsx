import React from "react";
import { AIWeights, ExtensionSettings } from "@/types/extension";
import AISettingsContainer from "./ai-settings-container";

interface SourceOraAISettingsProps {
	settings: ExtensionSettings;
	onUpdateSettings: (settings: Partial<ExtensionSettings>) => Promise<void>;
	onUpdateWeights: (weights: AIWeights) => Promise<void>;
	isDarkMode?: boolean;
}

const SourceOraAISettings: React.FC<SourceOraAISettingsProps> = ({
	settings,
	onUpdateSettings,
	onUpdateWeights,
	isDarkMode = true,
}) => {
	return (
		<AISettingsContainer
			type="source-ora"
			settings={settings}
			onUpdateSettings={onUpdateSettings}
			onUpdateWeights={onUpdateWeights}
			isDarkMode={isDarkMode}
		/>
	);
};

export default SourceOraAISettings;
