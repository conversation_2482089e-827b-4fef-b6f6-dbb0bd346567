"use client";

import React from "react";
import { Clock, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTheme } from "@/hooks/useTheme";

interface ComingSoonProps {
  isOpen: boolean;
  onClose: () => void;
}

const ComingSoon: React.FC<ComingSoonProps> = ({ isOpen, onClose }) => {
  const { isDarkMode } = useTheme();

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm ${
      isDarkMode ? 'bg-black/70' : 'bg-white/70'
    }`}>
      <div className={`relative w-full max-w-md mx-4 p-8 rounded-2xl border shadow-2xl ${
        isDarkMode
          ? 'bg-gray-800 border-gray-700'
          : 'bg-white border-gray-200'
      }`}>
        <button
          onClick={onClose}
          className={`absolute cursor-pointer top-4 right-4 p-2 rounded-lg transition-colors ${
            isDarkMode
              ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200'
              : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
          }`}
        >
          <X size={20} />
        </button>

        <div className="flex justify-center mb-6">
          <div className="p-4 rounded-full bg-brand-primary/20">
            <Clock size={32} className="text-brand-primary" />
          </div>
        </div>

        <h2 className={`text-2xl font-bold text-center mb-4 ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          Coming Soon!
        </h2>

        <p className={`text-center mb-8 ${
          isDarkMode ? 'text-gray-400' : 'text-gray-600'
        }`}>
          We're working hard to bring you this feature. Stay tuned for updates!
        </p>

        <div className="flex justify-center">
          <Button
            onClick={onClose}
            className="bg-brand-primary cursor-pointer text-white px-6 py-2 rounded-lg font-medium transition-all duration-200 focus:ring-2 focus:ring-brand-primary/50"
          >
            Got it
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ComingSoon;
