"use client";
import { useEffect, useState } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../ui/accordion";
import AISummarizer from "./AISummarizer";
import WarningAndRestrictions from "./warning-and-restrictions";
import Offers from "./offers";
import ProfitCalculator from "./profit-calculator";
import PriceHistory from "./price-history";
import AverageData from "./average-data-section";
import BuyBoxHistory from "./buy-box-history";
import ClickbuyAds from "./clickbuy-ads";

const AccordionData = ({ isDarkMode = true, activeAccordion = null }: {
  isDarkMode: boolean;
  activeAccordion?: string[] | null;
}) => {
  const [selectedAccordion, setSelectedAccordion] = useState<string[]>(activeAccordion !== null ? activeAccordion :  [
    "ai-summarizer",
    "warnings",
    "offers",
    "calculator-charts",
    "clickbuy-ads",
    "price-history",
    "average-data",
    "buybox-history",
  ]);
  const [mounted, setMounted] = useState(false);

  return (
    <div className="mt-1">
      <Accordion
        type="multiple"
        defaultValue={selectedAccordion}
        className="space-y-1"
      >
        <AccordionItem
          value="ai-summarizer"
          className={`border ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-black bg-white'} overflow-hidden shadow`}
          onClick={() => {
            if (selectedAccordion.includes("ai-summarizer")) {
              setSelectedAccordion(
                selectedAccordion.filter((item) => item !== "ai-summarizer"),
              );
            } else {
              setSelectedAccordion([...selectedAccordion, "ai-summarizer"]);
            }
          }}
        >
          <AccordionTrigger className={`rounded-none text-green-500 px-2 py-1 hover:no-underline [&[data-state=open]>svg]:rotate-180 ${isDarkMode ? 'text-white border-white bg-gray-900' : 'text-black border-black bg-gray-300'}`}>
            <div className="w-full flex items-center justify-center">
              <span className="text-sm font-bold">PRODUCT ANALYSIS</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-0 pb-0">
            <AISummarizer isDarkMode={isDarkMode} />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem
          value="warnings"
          className={`border ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-black bg-white'} overflow-hidden shadow`}
          onClick={() => {
            if (selectedAccordion.includes("warnings")) {
              setSelectedAccordion(
                selectedAccordion.filter((item) => item !== "warnings"),
              );
            } else {
              setSelectedAccordion([...selectedAccordion, "warnings"]);
            }
          }}
        >
          <AccordionTrigger className={`bg-black text-green-500 px-2 py-1 rounded-none hover:no-underline [&[data-state=open]>svg]:rotate-180 ${isDarkMode ? 'text-white border-white bg-gray-900' : 'text-black border-black bg-gray-300'}`}>
            <div className="w-full flex items-center justify-center">
              <span className="text-sm font-bold">WARNINGS & RESTRICTIONS</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="">
            <WarningAndRestrictions isDarkMode={isDarkMode} />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem
          value="offers"
          className={`border ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-black bg-white'} overflow-hidden shadow`}
          onClick={() => {
            if (selectedAccordion.includes("offers")) {
              setSelectedAccordion(
                selectedAccordion.filter((item) => item !== "offers"),
              );
            } else {
              setSelectedAccordion([...selectedAccordion, "offers"]);
            }
          }}
        >
          <AccordionTrigger className={`bg-black text-green-500 px-2 py-1 rounded-none hover:no-underline [&[data-state=open]>svg]:rotate-180 ${isDarkMode ? 'text-white border-white bg-gray-900' : 'text-black border-black bg-gray-300'}`}>
            <div className="w-full flex items-center justify-center">
              <span className="text-sm font-bold">OFFERS</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="">
            <Offers isDarkMode={isDarkMode} />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem
          value="calculator-charts"
          className={`border ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-black bg-white'} overflow-hidden shadow`}
          onClick={() => {
            if (selectedAccordion.includes("calculator-charts")) {
              setSelectedAccordion(
                selectedAccordion.filter(
                  (item) => item !== "calculator-charts",
                ),
              );
            } else {
              setSelectedAccordion([...selectedAccordion, "calculator-charts"]);
            }
          }}
        >
          <AccordionTrigger className={`bg-black text-green-500 px-2 py-1 rounded-none hover:no-underline [&[data-state=open]>svg]:rotate-180 ${isDarkMode ? 'text-white border-white bg-gray-900' : 'text-black border-black bg-gray-300'}`}>
            <div className="w-full flex items-center justify-center">
              <span className="text-sm font-bold">PROFIT CALCULATOR</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="py-0 pb-2 pt-0">
            <ProfitCalculator isDarkMode={isDarkMode} />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem
          value="price-history"
          className={`border ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-black bg-white'} overflow-hidden shadow`}
          onClick={() => {
            if (selectedAccordion.includes("price-history")) {
              setSelectedAccordion(
                selectedAccordion.filter((item) => item !== "price-history"),
              );
            } else {
              setSelectedAccordion([...selectedAccordion, "price-history"]);
            }
          }}
        >
          <AccordionTrigger className={`bg-black text-green-500 px-2 py-1 rounded-none hover:no-underline [&[data-state=open]>svg]:rotate-180 ${isDarkMode ? 'text-white border-white bg-gray-900' : 'text-black border-black bg-gray-300'}`}>
            <div className="w-full flex items-center justify-center">
              <span className="text-sm font-bold">PRICE HISTORY</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="py-0">
            <PriceHistory isDarkMode={isDarkMode} />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem
          value="average-data"
          className={`border ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-black bg-white'} overflow-hidden shadow`}
          onClick={() => {
            if (selectedAccordion.includes("average-data")) {
              setSelectedAccordion(
                selectedAccordion.filter((item) => item !== "average-data"),
              );
            } else {
              setSelectedAccordion([...selectedAccordion, "average-data"]);
            }
          }}
        >
          <AccordionTrigger className={`bg-black text-green-500 px-2 py-1 rounded-none hover:no-underline [&[data-state=open]>svg]:rotate-180 ${isDarkMode ? 'text-white border-white bg-gray-900' : 'text-black border-black bg-gray-300'}`}>
            <div className="w-full flex items-center justify-center">
              <span className="text-sm font-bold">AVERAGE DATA</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="py-0 pt-0">
            <div className="">
              <AverageData isDarkMode={isDarkMode} />
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem
          value="buybox-history"
          className={`border border-black overflow-hidden bg-white shadow ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-black bg-white'}`}
          onClick={() => {
            if (selectedAccordion.includes("buybox-history")) {
              setSelectedAccordion(
                selectedAccordion.filter((item) => item !== "buybox-history"),
              );
            } else {
              setSelectedAccordion([...selectedAccordion, "buybox-history"]);
            }
          }}
        >
          <AccordionTrigger className={`bg-black text-green-500 px-2 py-1 rounded-none hover:no-underline [&[data-state=open]>svg]:rotate-180 ${isDarkMode ? 'text-white border-white bg-gray-900' : 'text-black border-black bg-gray-300'}`}>
            <div className="w-full flex items-center justify-center">
              <span className="text-sm font-bold">BUY BOX HISTORY</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="py-0  pt-0">
            <div className="">
              <BuyBoxHistory isDarkMode={isDarkMode} />
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem
          value="clickbuy-ads"
          className={`mx-1 border ${isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-black bg-white'} overflow-hidden shadow`}
          onClick={() => {
            if (selectedAccordion.includes("clickbuy-ads")) {
              setSelectedAccordion(
                selectedAccordion.filter((item) => item !== "clickbuy-ads"),
              );
            } else {
              setSelectedAccordion([...selectedAccordion, "clickbuy-ads"]);
            }
          }}
        >
          <AccordionTrigger className="bg-black text-green-500 px-2 py-1 rounded-none hover:no-underline [&[data-state=open]>svg]:rotate-180">
            <div className="w-full flex items-center justify-center">
              <span className="text-sm font-bold">CLICKBUY DEALS</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="p-0 pb-0">
            <ClickbuyAds isDarkMode={isDarkMode} />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default AccordionData;
