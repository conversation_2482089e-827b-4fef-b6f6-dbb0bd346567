/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useState, useEffect } from "react";
import {
	Search,
	Filter,
	RefreshCw,
	Settings,
	Package,
	RotateCcw,
	AlertTriangle,
	Loader2,
} from "lucide-react";
import { useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
// import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
// import { Checkbox } from "@/components/ui/checkbox";
import { SalesRank } from "@/components/filters/sales-rank";
import InspectoraResult from "./inspectora-result";

import { useInspectoraFilters } from "@/hooks/use-inspectora-data";
import { FILTER_CONFIGS, FilterValues, INSPECTORA_TABS, InspectoraPageProps, TabId } from "@/constants/inspectoral-filter";
import { searchProducts } from "@/lib/api/inspectora-api";
import { BuyBox } from "../filters/buy-box";
import { EbayNew } from "../filters/ebay-buy-box";
import { NewOfferCount } from "../filters/new-offer-count";
import { SellerIds } from "../filters/seller-ids";
import { BuyBoxAmazon } from "../filters/buy-box-amazon";
import { BuyBoxTopSeller } from "@/components/filters/buy-box-top-seller";
import { BuyBoxWinnerCount } from "@/components/filters/buy-box-winner-count";
import { BuyBoxStandardDeviation } from "@/components/filters/buy-box-standard-deviation";
import { Variations } from "../filters/variations";
import { BoughtInPastMonth } from "../filters/brought-in-past-month";
import { Rating } from "../filters/rating";
import { RatingCount } from "../filters/rating-count";
import { AvailabilityAmazon } from "../filters/availability-amazon";
import { PackageDimension } from "../filters/pakage-dimension";
import { PackageWeight } from "../filters/package-weight";
import { HazMat } from "../filters/hazmat-heatsensitive";
import { useInspectoraAsins } from "@/hooks/use-inspectora-asins";

// Navigation configuration

const InspectoraPage: React.FC<InspectoraPageProps> = ({
	isDarkMode,
	section,
}) => {
	const searchParams = useSearchParams();
	const asin = searchParams.get("asin");
	const country = searchParams.get("country");
	const {mutate, isPending, error, data, currentTab: activeTab , setCurrentTab: setActiveTab } = useInspectoraAsins()

	console.log('data from the mutate', data)




	const [filters, setFilters] = useState<FilterValues>({
		marketplace_id: "uk",
		brand: "",
		current_SALES_gte: "",
		current_SALES_lte: "",
		isLowest_SALES: null,
		current_BUY_BOX_SHIPPING_gte: "",
		current_BUY_BOX_SHIPPING_lte: "",
		isOutOfStock: null,
		isEbayOutOfStock: null,
		current_EBAY_NEW_gte: "",
		current_EBAY_NEW_lte: "",
		current_COUNT_NEW_gte: "",
		current_COUNT_NEW_lte: "",
		buyBoxSeller: "",
		buyBoxStatsAmazon30_gte: "",
		buyBoxStatsAmazon30_lte: "",
		buyBoxStatsAmazon90_gte: "",
		buyBoxStatsAmazon90_lte: "",
		buyBoxStatsTopSeller30_gte: "",
		buyBoxStatsTopSeller30_lte: "",
		buyBoxStatsTopSeller90_gte: "",
		buyBoxStatsTopSeller90_lte: "",
		buyBoxStatsSellerCount30_gte: "",
		buyBoxStatsSellerCount30_lte: "",
		buyBoxStatsSellerCount90_gte: "",
		buyBoxStatsSellerCount90_lte: "",
		buyBoxStandardDeviation30_gte: "",
		buyBoxStandardDeviation30_lte: "",
		buyBoxStandardDeviation90_gte: "",
		buyBoxStandardDeviation90_lte: "",
		buyBoxStandardDeviation365_gte: "",
		buyBoxStandardDeviation365_lte: "",
		hasParentASIN: null,
		monthlySold_gte: "",
		monthlySold_lte: "",
		current_RATING_gte: "",
		current_RATING_lte: "",
		current_COUNT_REVIEWS_gte: "",
		current_COUNT_REVIEWS_lte: "",
		availabilityAmazon: [],
		packageLength_gte: "",
		packageLength_lte: "",
		packageWidth_gte: "",
		packageWidth_lte: "",
		packageHeight_gte: "",
		packageHeight_lte: "",
		packageWeight_gte: "",
		packageWeight_lte: "",
		isHazMat: null,
		isHeatSensitive: null,
	});

	// const [isSearching, setIsSearching] = useState(false);
	const [hasSearched, setHasSearched] = useState(false);
	const [resultCount, setResultCount] = useState(0);
	const [cachedAsins, setCachedAsins] = useState<string[]>([]);
	const [searchError, setSearchError] = useState<string | null>(null);

	console.log('')

	// Use the custom hook for API data (now with memoized filters)
	const {
		filters: apiFilters,
		isLoading: isLoadingInspectoraData,
		error: inspectoraError,
		isSuccess: isInspectoraSuccess,
		hasData: hasInspectoraData,
	} = useInspectoraFilters(asin, country);

	// Auto-populate filters when API data is loaded (now safe with memoized apiFilters)
	useEffect(() => {
		if (isInspectoraSuccess && apiFilters) {
			setFilters(apiFilters);
		}
	}, [isInspectoraSuccess, apiFilters]);

	useEffect(() => {
		if (section && INSPECTORA_TABS.some((tab) => tab.id === section)) {
			setActiveTab(section as TabId);
		}
	}, [section]);

	// Handle filter changes
	const handleFilterChange = React.useCallback(
		(key: keyof FilterValues, value: any) => {
			setFilters((prev) => ({
				...prev,
				[key]: value,
			}));
		},
		[]
	);

	// Handle range filter changes
	// const handleRangeChange = React.useCallback(
	// 	(key: string, type: "min" | "max", value: string) => {
	// 		setFilters((prev) => ({
	// 			...prev,
	// 			[key]: {
	// 				...(prev[key as keyof FilterValues] as {
	// 					min: string;
	// 					max: string;
	// 				}),
	// 				[type]: value,
	// 			},
	// 		}));
	// 	},
	// 	[]
	// );

	// Reset all filters - updated to use "item" instead of "package"
	const resetFilters = React.useCallback(() => {
		setFilters({
			marketplace_id: "uk",
			brand: "",
			current_SALES_gte: "",
			current_SALES_lte: "",
			isLowest_SALES: null,
			current_BUY_BOX_SHIPPING_gte: "",
			current_BUY_BOX_SHIPPING_lte: "",
			isOutOfStock: null,
			isEbayOutOfStock: null,
			current_EBAY_NEW_gte: "",
			current_EBAY_NEW_lte: "",
			current_COUNT_NEW_gte: "",
			current_COUNT_NEW_lte: "",
			buyBoxSeller: "",
			buyBoxStatsAmazon30_gte: "",
			buyBoxStatsAmazon30_lte: "",
			buyBoxStatsAmazon90_gte: "",
			buyBoxStatsAmazon90_lte: "",
			buyBoxStatsTopSeller30_gte: "",
			buyBoxStatsTopSeller30_lte: "",
			buyBoxStatsTopSeller90_gte: "",
			buyBoxStatsTopSeller90_lte: "",
			buyBoxStatsSellerCount30_gte: "",
			buyBoxStatsSellerCount30_lte: "",
			buyBoxStatsSellerCount90_gte: "",
			buyBoxStatsSellerCount90_lte: "",
			buyBoxStandardDeviation30_gte: "",
			buyBoxStandardDeviation30_lte: "",
			buyBoxStandardDeviation90_gte: "",
			buyBoxStandardDeviation90_lte: "",
			buyBoxStandardDeviation365_gte: "",
			buyBoxStandardDeviation365_lte: "",
			hasParentASIN: null,
			monthlySold_gte: "",
			monthlySold_lte: "",
			current_RATING_gte: "",
			current_RATING_lte: "",
			current_COUNT_REVIEWS_gte: "",
			current_COUNT_REVIEWS_lte: "",
			availabilityAmazon: [],
			packageLength_gte: "",
			packageLength_lte: "",
			packageWidth_gte: "",
			packageWidth_lte: "",
			packageHeight_gte: "",
			packageHeight_lte: "",
			packageWeight_gte: "",
			packageWeight_lte: "",
			isHazMat: null,
			isHeatSensitive: null,
		});
		setHasSearched(false);
		setResultCount(0);
		// setSearchResults([]);
		setCachedAsins([]);
		setSearchError(null);
		// Clear cached data
		localStorage.removeItem("inspectora_search_asins");
		localStorage.removeItem("inspectora_search_domain");
	}, []);

	// Real search function using the new APIs
	const handleSearch = async () => {
		

		mutate(filters)

		// try {

		// 	// Step 1: Search for ASINs
		// 	const searchResponse = await searchProducts(filters);

		// 	if (
		// 		!searchResponse.success ||
		// 		!Array.isArray(searchResponse.data)
		// 	) {
		// 		throw new Error("Search failed or returned invalid data");
		// 	}
			
		// 	console.log("Search response:", searchResponse);

		// 	const asins = searchResponse.data;
		// 	const totalCount = asins.length; // Frontend calculates total

		// 	console.log(`✅ Search found ${totalCount} products`);

		// 	// Step 2: Cache the ASIN list
		// 	setCachedAsins(asins);
		// 	localStorage.setItem(
		// 		"inspectora_search_asins",
		// 		JSON.stringify(asins)
		// 	);
		// 	localStorage.setItem(
		// 		"inspectora_search_domain",
		// 		filters.marketplace_id
		// 	);

		// 	// Step 3: Set result count and clear previous results
		// 	setResultCount(totalCount);
		// 	setSearchResults([]);
		// 	setHasSearched(true);

		// 	// Auto-switch to results tab
		// 	setActiveTab("result");
		// } catch (error) {
		// 	console.error("❌ Search failed:", error);
		// 	setSearchError(
		// 		error instanceof Error ? error.message : "Search failed"
		// 	);
		// 	setResultCount(0);
		// 	setSearchResults([]);
		// 	setCachedAsins([]);
		// } finally {
		// 	setIsSearching(false);
		// }
	};

	const hasActiveFilters = React.useCallback(() => {
		return Object.entries(filters).some(([, value]) => {
			if (
				typeof value === "object" &&
				value !== null &&
				"min" in value &&
				"max" in value
			) {
				const range = value as { min: string; max: string };
				return range.min || range.max;
			}
			return value !== null && value !== "";
		});
	}, [filters]);

	const renderFilter = React.useCallback(
		(key: string, config: any) => {
			const Icon = config.icon;

			switch (config.type) {
				case "sales_rank_component":
					return (
						<SalesRank
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								current_SALES_gte: filters.current_SALES_gte,
								current_SALES_lte: filters.current_SALES_lte,
								isLowest_SALES: filters.isLowest_SALES,
							}}
							onFilterChange={handleFilterChange}
						/>
					);

				case "buy_box_component":
					return (
						<BuyBox
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								current_BUY_BOX_SHIPPING_gte:
									filters.current_BUY_BOX_SHIPPING_gte,
								current_BUY_BOX_SHIPPING_lte:
									filters.current_BUY_BOX_SHIPPING_lte,
								isOutOfStock: filters.isOutOfStock,
							}}
							onFilterChange={handleFilterChange}
						/>
					);

				case "ebay_new_component":
					return (
						<EbayNew
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								current_EBAY_NEW_gte:
									filters.current_EBAY_NEW_gte,
								current_EBAY_NEW_lte:
									filters.current_EBAY_NEW_lte,
								isEbayOutOfStock: filters.isEbayOutOfStock,
							}}
							onFilterChange={handleFilterChange}
						/>
					);
				case "new_offer_count_component":
					return (
						<NewOfferCount
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								current_COUNT_NEW_gte:
									filters.current_COUNT_NEW_gte,
								current_COUNT_NEW_lte:
									filters.current_COUNT_NEW_lte,
							}}
							onFilterChange={handleFilterChange}
						/>
					);
				case "seller_ids_component":
					return (
						<SellerIds
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								buyBoxSeller: filters.buyBoxSeller,
							}}
							onFilterChange={handleFilterChange}
						/>
					);
				case "buy_box_amazon_component":
					return (
						<BuyBoxAmazon
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								buyBoxStatsAmazon30_gte:
									filters.buyBoxStatsAmazon30_gte,
								buyBoxStatsAmazon30_lte:
									filters.buyBoxStatsAmazon30_lte,
								buyBoxStatsAmazon90_gte:
									filters.buyBoxStatsAmazon90_gte,
								buyBoxStatsAmazon90_lte:
									filters.buyBoxStatsAmazon90_lte,
							}}
							onFilterChange={handleFilterChange}
						/>
					);
				case "buy_box_top_seller_component":
					return (
						<BuyBoxTopSeller
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								buyBoxStatsTopSeller30_gte:
									filters.buyBoxStatsTopSeller30_gte,
								buyBoxStatsTopSeller30_lte:
									filters.buyBoxStatsTopSeller30_lte,
								buyBoxStatsTopSeller90_gte:
									filters.buyBoxStatsTopSeller90_gte,
								buyBoxStatsTopSeller90_lte:
									filters.buyBoxStatsTopSeller90_lte,
							}}
							onFilterChange={handleFilterChange}
						/>
					);
				case "buy_box_winner_count_component":
					return (
						<BuyBoxWinnerCount
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								buyBoxStatsSellerCount30_gte:
									filters.buyBoxStatsSellerCount30_gte,
								buyBoxStatsSellerCount30_lte:
									filters.buyBoxStatsSellerCount30_lte,
								buyBoxStatsSellerCount90_gte:
									filters.buyBoxStatsSellerCount90_gte,
								buyBoxStatsSellerCount90_lte:
									filters.buyBoxStatsSellerCount90_lte,
							}}
							onFilterChange={handleFilterChange}
						/>
					);
				case "buy_box_standard_deviation_component":
					return (
						<BuyBoxStandardDeviation
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								buyBoxStandardDeviation30_gte:
									filters.buyBoxStandardDeviation30_gte,
								buyBoxStandardDeviation30_lte:
									filters.buyBoxStandardDeviation30_lte,
								buyBoxStandardDeviation90_gte:
									filters.buyBoxStandardDeviation90_gte,
								buyBoxStandardDeviation90_lte:
									filters.buyBoxStandardDeviation90_lte,
								buyBoxStandardDeviation365_gte:
									filters.buyBoxStandardDeviation365_gte,
								buyBoxStandardDeviation365_lte:
									filters.buyBoxStandardDeviation365_lte,
							}}
							onFilterChange={handleFilterChange}
						/>
					);
				case "variations_component":
					return (
						<Variations
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								hasParentASIN: filters.hasParentASIN,
							}}
							onFilterChange={handleFilterChange}
						/>
					);
				case "bought_in_past_month_component":
					return (
						<BoughtInPastMonth
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								monthlySold_gte: filters.monthlySold_gte,
								monthlySold_lte: filters.monthlySold_lte,
							}}
							onFilterChange={handleFilterChange}
						/>
					);
				case "rating_component":
					return (
						<Rating
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								current_RATING_gte: filters.current_RATING_gte,
								current_RATING_lte: filters.current_RATING_lte,
							}}
							onFilterChange={handleFilterChange}
						/>
					);
				case "rating_count_component":
					return (
						<RatingCount
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								current_COUNT_REVIEWS_gte:
									filters.current_COUNT_REVIEWS_gte,
								current_COUNT_REVIEWS_lte:
									filters.current_COUNT_REVIEWS_lte,
							}}
							onFilterChange={handleFilterChange}
						/>
					);
				case "availability_amazon_component":
					return (
						<AvailabilityAmazon
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								availabilityAmazon: filters.availabilityAmazon,
							}}
							onFilterChange={handleFilterChange}
						/>
					);
				case "package_dimension_component":
					return (
						<PackageDimension
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								packageLength_gte: filters.packageLength_gte,
								packageLength_lte: filters.packageLength_lte,
								packageWidth_gte: filters.packageWidth_gte,
								packageWidth_lte: filters.packageWidth_lte,
								packageHeight_gte: filters.packageHeight_gte,
								packageHeight_lte: filters.packageHeight_lte,
							}}
							onFilterChange={handleFilterChange}
						/>
					);
				case "package_weight_component":
					return (
						<PackageWeight
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								packageWeight_gte: filters.packageWeight_gte,
								packageWeight_lte: filters.packageWeight_lte,
							}}
							onFilterChange={handleFilterChange}
						/>
					);
				case "hazmat_component":
					return (
						<HazMat
							key={key}
							isDarkMode={isDarkMode}
							filters={{
								isHazMat: filters.isHazMat,
								isHeatSensitive: filters.isHeatSensitive,
							}}
							onFilterChange={handleFilterChange}
						/>
					);
				case "select":
					return (
						<div key={key} className="space-y-2">
							<Label
								className={`text-sm font-medium flex items-center gap-2 ${
									isDarkMode
										? "text-gray-300"
										: "text-gray-700"
								}`}>
								<Icon className="w-4 h-4 text-[#19D86C]" />
								{config.label}
							</Label>
							<Select
								value={
									filters[key as keyof FilterValues] as string
								}
								onValueChange={(value) =>
									handleFilterChange(
										key as keyof FilterValues,
										value
									)
								}>
								<SelectTrigger
									className={`${
										isDarkMode
											? "bg-gray-800 border-gray-700 text-white"
											: "bg-white border-gray-300 text-black"
									}`}>
									<SelectValue
										placeholder={`Select ${config.label.toLowerCase()}`}
									/>
								</SelectTrigger>
								<SelectContent
									className={
										isDarkMode
											? "bg-gray-800 border-gray-700"
											: "bg-white border-gray-300"
									}>
									{config.options.map((option: any) => (
										<SelectItem
											key={option.value}
											value={option.value}>
											{option.label}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>
					);


				default:
					return null;
			}
		},
		[filters, isDarkMode, handleFilterChange]
	);

	const renderTabContent = () => {
		switch (activeTab) {
			case "filter":
				return (
					<div
						className={`p-6 rounded-lg border ${
							isDarkMode
								? "bg-gray-800 border-gray-700"
								: "bg-white border-gray-200"
						}`}>
						{/* Loading/Error States for ASIN pre-filling */}
						{asin && country && (
							<div className="mb-6">
								{isLoadingInspectoraData && (
									<div
										className={`p-4 rounded-lg border ${
											isDarkMode
												? "bg-blue-500/10 border-blue-500/20"
												: "bg-blue-50 border-blue-200"
										}`}>
										<div className="flex items-center gap-3">
											<Loader2 className="w-5 h-5 text-blue-500 animate-spin" />
											<div>
												<h4 className="text-blue-500 font-medium">
													Loading Product Data
												</h4>
												<p
													className={`text-sm ${
														isDarkMode
															? "text-gray-300"
															: "text-gray-700"
													}`}>
													Fetching data for ASIN:{" "}
													{asin} in{" "}
													{country.toUpperCase()}
												</p>
											</div>
										</div>
									</div>
								)}

								{inspectoraError && (
									<div
										className={`p-4 rounded-lg border ${
											isDarkMode
												? "bg-red-500/10 border-red-500/20"
												: "bg-red-50 border-red-200"
										}`}>
										<div className="flex items-start gap-3">
											<AlertTriangle className="w-5 h-5 text-red-500 mt-0.5" />
											<div>
												<h4 className="text-red-500 font-medium mb-1">
													Failed to Load Product Data
												</h4>
												<p
													className={`text-sm ${
														isDarkMode
															? "text-gray-300"
															: "text-gray-700"
													}`}>
													Unable to fetch data for
													ASIN: {asin}. You can still
													use the filters manually.
												</p>
											</div>
										</div>
									</div>
								)}

								{isInspectoraSuccess && hasInspectoraData && (
									<div
										className={`p-4 rounded-lg border ${
											isDarkMode
												? "bg-green-500/10 border-green-500/20"
												: "bg-green-50 border-green-200"
										}`}>
										<div className="flex items-start gap-3">
											<Package className="w-5 h-5 text-green-500 mt-0.5" />
											<div>
												<h4 className="text-green-500 font-medium mb-1">
													Product Data Loaded
												</h4>
												<p
													className={`text-sm ${
														isDarkMode
															? "text-gray-300"
															: "text-gray-700"
													}`}>
													Filters have been pre-filled
													with data from ASIN: {asin}.
													You can modify them as
													needed.
												</p>
											</div>
										</div>
									</div>
								)}
							</div>
						)}

						<div className="flex items-center gap-2 mb-6">
							<Settings className="w-5 h-5 text-[#19D86C]" />
							<h2
								className={`text-lg font-semibold ${
									isDarkMode ? "text-white" : "text-gray-900"
								}`}>
								Search Filters
								{asin && country && (
									<span
										className={`ml-2 text-sm font-normal ${
											isDarkMode
												? "text-gray-400"
												: "text-gray-600"
										}`}>
										(Pre-filled from ASIN: {asin})
									</span>
								)}
							</h2>
						</div>

						{/* Filter Grid */}
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
							{Array.from(FILTER_CONFIGS.entries()).map(
								([key, config]) => renderFilter(key, config)
							)}
						</div>

						{/* Action Buttons */}
						<div className="flex items-center justify-between pt-4 border-t border-gray-600">
							<div className="flex items-center gap-2 text-sm">
								<Filter className="w-4 h-4 text-[#19D86C]" />
								<span
									className={
										isDarkMode
											? "text-gray-400"
											: "text-gray-600"
									}>
									{hasActiveFilters()
										? "Filters applied"
										: "No filters applied"}
								</span>
							</div>

							<div className="flex items-center gap-2">
								<Button
									variant="outline"
									size="sm"
									onClick={resetFilters}
									disabled={!hasActiveFilters()}
									className={`${
										isDarkMode
											? "border-gray-600 text-gray-300"
											: "border-gray-300 text-gray-700"
									}`}>
									<RotateCcw className="w-4 h-4 mr-2" />
									Reset
								</Button>

								<Button
									onClick={handleSearch}
									disabled={
										isPending || !hasActiveFilters()
									}
									className="bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] hover:from-[#12C2E9] hover:to-[#19D86C] disabled:opacity-50 disabled:cursor-not-allowed">
									{isPending ? (
										<div className="flex items-center gap-2">
											<RefreshCw className="w-4 h-4 animate-spin" />
											Searching...
										</div>
									) : (
										<div className="flex items-center gap-2">
											<Search className="w-4 h-4" />
											Search Products
										</div>
									)}
								</Button>
							</div>
						</div>
					</div>
				);

			case "result":
				return (
					<InspectoraResult
						isDarkMode={isDarkMode}
						// searchResults={searchResults}
						resultCount={data ? data.length : 0}
						hasSearched={!isPending}
						isSearching={isPending}
						cachedAsins={data}
						searchError={searchError}
						// onProductsLoaded={setSearchResults}
					/>
				);

			default:
				return null;
		}
	};

	return (
		<div
			className={`min-h-screen p-8 ${
				isDarkMode
					? "bg-gradient-to-br from-slate-900 to-slate-800"
					: "bg-gradient-to-br from-gray-50 to-gray-100"
			}`}>
			{/* Header */}
			<header className={`mb-6 ${isDarkMode ? "" : ""}`}>
				<h1 className="text-2xl font-semibold">Inspectora</h1>
				<nav>
					<ul className="flex space-x-4 mt-2">
						{INSPECTORA_TABS.map((tab) => (
							<li key={tab.id}>
								<button
									onClick={() => {
										setActiveTab(tab.id);
										// Update URL
										if (typeof window !== "undefined") {
											const url = new URL(
												window.location.href
											);
											url.searchParams.set(
												"section",
												tab.id
											);
											window.history.pushState(
												{},
												"",
												url.toString()
											);
										}
									}}
									className={`pb-2 cursor-pointer transition-all ${
										activeTab === tab.id
											? "border-b-2 border-[#19D86C]"
											: `border-none ${
													isDarkMode
														? "text-gray-400 hover:text-gray-300"
														: "text-gray-600 hover:text-gray-500"
											  }`
									} ${
										isDarkMode
											? "text-white"
											: "text-gray-600"
									}`}>
									{tab.label}
								</button>
							</li>
						))}
					</ul>
				</nav>
			</header>

			<div className="">{renderTabContent()}</div>
		</div>
	);
};

export default InspectoraPage;
