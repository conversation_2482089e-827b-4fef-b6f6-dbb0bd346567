"use client";

import React from "react";
import { Clock, X, ExternalLink, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTheme } from "@/hooks/useTheme";
import { SourceoraFilter } from "./inspectora-result";
import { useSourceora } from "@/hooks/use-post-source";

interface ComingSoonProps {
  isOpen: boolean;
  onClose: () => void;
  sourceoraFilter: SourceoraFilter;
}

const SourceOraModal: React.FC<ComingSoonProps> = ({ isOpen, onClose, sourceoraFilter }) => {
  const { mutate, isPending, error, data } = useSourceora();
  
  console.log('sourceora filter', data);

  const { isDarkMode } = useTheme();

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm ${
      isDarkMode ? 'bg-black/70' : 'bg-white/70'
    }`}>
      <div className={`relative w-full max-w-3xl mx-4 p-8 rounded-2xl border shadow-2xl ${
        isDarkMode
          ? 'bg-gray-800 border-gray-700'
          : 'bg-white border-gray-200'
      }`}>
        <button
          onClick={onClose}
          className={`absolute cursor-pointer top-4 right-4 p-2 rounded-lg transition-colors ${
            isDarkMode
              ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200'
              : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
          }`}
        >
          <X size={20} />
        </button>

        <div className="space-y-6">
          <div className={`text-center ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            <h2 className="text-2xl font-bold mb-2">Source Results</h2>
            {isPending ? (
              <div className="flex items-center justify-center space-x-2">
                <Loader2 className="animate-spin" size={20} />
                <p>Searching for sources...</p>
              </div>
            ) : (
              <p className="text-sm opacity-70">{data?.data.search_summary}</p>
            )}
          </div>

          <div className="space-y-4">
            {isPending ? (
              <div className={`p-4 rounded-lg border text-center ${
                isDarkMode ? 'border-gray-700 bg-gray-900 text-gray-400' : 'border-gray-200 bg-gray-50 text-gray-600'
              }`}>
                <Loader2 className="animate-spin mx-auto mb-2" size={24} />
                <p>Loading source results...</p>
              </div>
            ) : data?.data.results.map((result, index) => (
              <div 
                key={index} 
                className={`p-4 rounded-lg border ${
                  isDarkMode ? 'border-gray-700 bg-gray-900' : 'border-gray-200 bg-gray-50'
                }`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {result.store_name}
                    </h3>
                    <div className={`mt-1 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      <p>Price: £{result.price}</p>
                      <p>Shipping: £{result.shipping_cost}</p>
                      <p>Total: £{result.total_cost}</p>
                      <p>Condition: {result.condition}</p>
                      <p>Availability: {result.availability}</p>
                    </div>
                  </div>
                  <a 
                    href={result.product_url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                      isDarkMode 
                        ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                        : 'bg-blue-50 hover:bg-blue-100 text-blue-600'
                    }`}
                  >
                    View <ExternalLink className="ml-2" size={16} />
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SourceOraModal;
