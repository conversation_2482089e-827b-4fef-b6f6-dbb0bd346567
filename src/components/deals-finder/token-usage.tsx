/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";
import React, { useState, useMemo } from "react";
import {
	Zap,
	TrendingUp,
	Calendar,
	AlertCircle,
	Clock,
	CreditCard,
	ArrowUp,
	BarChart3,
	RefreshCw,
	Loader2,
} from "lucide-react";
import Button from "@/components/Button";
import { useTokenData } from "@/hooks/use-token-data";
import { useRouter } from "next/navigation";

interface TokenUsageProps {
	isDarkMode?: boolean;
	onUpgrade?: () => void;
}

const TokenUsage: React.FC<TokenUsageProps> = ({
	isDarkMode = true,
	onUpgrade,
}) => {
	const router = useRouter();
	const [timeRange, setTimeRange] = useState<"7d" | "30d" | "90d">("30d");
	const { balance, usageHistory, isLoading, isError, error, refetch } =
		useTokenData();

	const handleUpgrade = () => {
		if (onUpgrade) {
			onUpgrade();
		} else {
			router.push(
				"/aep-solutions/platforms?tab=billing&section=pricing"
			);
		}
	};

	// Calculate usage percentage
	const usagePercentage = useMemo(() => {
		if (!balance || balance.current_tokens === 0) return 0;
		// Since we don't have a limit from the API, we'll estimate based on plan type
		const planLimits = {
			light: 750,
			basic: 750,
			standard: 750,
			ultimate: 750,
			free: 100,
			custom: 750,
		};
		const limit =
			planLimits[balance.plan_type as keyof typeof planLimits] || 750;
		return Math.min(
			100,
			Math.max(0, ((limit - balance.current_tokens) / limit) * 100)
		);
	}, [balance]);

	const isLowTokens = balance?.current_tokens
		? balance.current_tokens < 150
		: false;
	const isVeryLowTokens = balance?.current_tokens
		? balance.current_tokens < 50
		: false;

	// Filter usage history by time range
	const getFilteredHistory = () => {
		if (!usageHistory?.usage_history) return [];

		const days = timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90;
		const cutoffDate = new Date();
		cutoffDate.setDate(cutoffDate.getDate() - days);

		return usageHistory.usage_history.filter(
			(usage: any) => new Date(usage.created_at) >= cutoffDate
		);
	};

	const filteredHistory = getFilteredHistory();
	const totalUsageInPeriod = filteredHistory.reduce(
		(sum: any, usage: any) => sum + usage.tokens_used,
		0
	);
	const avgDailyUsage =
		totalUsageInPeriod /
		(timeRange === "7d" ? 7 : timeRange === "30d" ? 30 : 90);

	const getUsageColor = () => {
		if (balance?.is_expired) return "text-red-500";
		if (isVeryLowTokens) return "text-red-500";
		if (isLowTokens) return "text-yellow-500";
		return "text-[#10A37F]";
	};

	const getProgressBarColor = () => {
		if (balance?.is_expired) return "bg-red-500";
		if (isVeryLowTokens) return "bg-red-500";
		if (isLowTokens) return "bg-yellow-500";
		return "bg-[#10A37F]";
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat("en-GB", {
			style: "currency",
			currency: "GBP",
			minimumFractionDigits: 3,
		}).format(amount);
	};

	// Estimate cost per token (this should ideally come from your backend)
	const costPerToken = 0.002; // $0.002 per token as example

	const getServiceIcon = (serviceName: string) => {
		switch (serviceName.toLowerCase()) {
			case "selectoria":
				return <Zap size={14} className="text-[#10A37F]" />;
			case "price_history":
				return <BarChart3 size={14} className="text-[#2F81F7]" />;
			case "competitor_scan":
				return <TrendingUp size={14} className="text-[#10A37F]" />;
			default:
				return <RefreshCw size={14} className="text-gray-500" />;
		}
	};

	const formatServiceName = (serviceName: string) => {
		const nameMap: Record<string, string> = {
			selectoria: "Product Analysis",
			price_history: "Price History",
			competitor_scan: "Competitor Scan",
		};
		return (
			nameMap[serviceName.toLowerCase()] ||
			serviceName
				.replace(/_/g, " ")
				.replace(/\b\w/g, (l) => l.toUpperCase())
		);
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString("en-US", {
			month: "short",
			day: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		});
	};

	// Loading state
	if (isLoading) {
		return (
			<div
				className={`${isDarkMode ? "bg-gray-800" : "bg-white"} border ${
					isDarkMode ? "border-gray-700" : "border-gray-200"
				} rounded-lg p-8`}>
				<div className="flex items-center justify-center">
					<Loader2 className="animate-spin h-8 w-8 text-[#10A37F] mr-3" />
					<span
						className={
							isDarkMode ? "text-white" : "text-[#111215]"
						}>
						Loading token data...
					</span>
				</div>
			</div>
		);
	}

	// Error state
	if (isError) {
		return (
			<div
				className={`${isDarkMode ? "bg-gray-800" : "bg-white"} border ${
					isDarkMode ? "border-gray-700" : "border-gray-200"
				} rounded-lg p-8`}>
				<div className="text-center">
					<AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
					<h3
						className={`text-lg font-medium mb-2 ${
							isDarkMode ? "text-white" : "text-[#111215]"
						}`}>
						Failed to Load Token Data
					</h3>
					<p
						className={`text-sm mb-4 ${
							isDarkMode ? "text-gray-400" : "text-gray-600"
						}`}>
						{error?.message || "Unable to fetch token information"}
					</p>
					<Button
						onClick={() => refetch()}
						variant="primary"
						size="sm">
						<RefreshCw className="w-4 h-4 mr-2" />
						Retry
					</Button>
				</div>
			</div>
		);
	}

	if (!balance) {
		return (
			<div
				className={`${
					isDarkMode
						? "bg-gradient-to-br from-slate-900 to-slate-800"
						: "bg-gradient-to-br from-gray-50 to-gray-100"
				} rounded-lg p-8`}>
				<div className="text-center">
					<div
						className={`absolute inset-0 pointer-events-none ${
							isDarkMode
								? "bg-[radial-gradient(circle_at_25%_25%,rgba(34,197,94,0.1)_0%,transparent_50%),radial-gradient(circle_at_75%_75%,rgba(34,197,94,0.05)_0%,transparent_50%)]"
								: "bg-[radial-gradient(circle_at_25%_25%,rgba(34,197,94,0.05)_0%,transparent_50%),radial-gradient(circle_at_75%_75%,rgba(34,197,94,0.03)_0%,transparent_50%)]"
						}`}
					/>
					<div className="text-6xl mb-4">🪙</div>
					<h3
						className={`text-lg font-medium mb-2 ${
							isDarkMode ? "text-white" : "text-[#111215]"
						}`}>
						No Token Balance
					</h3>
					<p
						className={`text-sm mb-4 ${
							isDarkMode ? "text-gray-400" : "text-gray-600"
						}`}>
						Get started by purchasing your first token plan
					</p>
					<Button onClick={handleUpgrade} variant="primary" size="sm">
						<Zap className="w-4 h-4 mr-2" />
						Purchase Tokens
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div
			className={`${isDarkMode ? "bg-gray-800" : "bg-white"} border ${
				isDarkMode ? "border-gray-700" : "border-gray-200"
			} rounded-lg overflow-hidden`}>
			{/* Header */}
			<div
				className={`p-6 border-b ${
					isDarkMode ? "border-gray-700" : "border-gray-200"
				}`}>
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-3">
						<div
							className={`${
								isDarkMode
									? "bg-gradient-to-r from-[#10A37F]/10 to-[#2F81F7]/10"
									: "bg-gradient-to-r from-[#10A37F]/10 to-[#2F81F7]/10"
							} p-2 rounded-lg`}>
							<Zap size={20} className="text-[#10A37F]" />
						</div>
						<div>
							<h3
								className={`text-xl font-bold ${
									isDarkMode ? "text-white" : "text-[#111215]"
								}`}>
								Token Usage
							</h3>
							<p
								className={`text-sm ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								}`}>
								Track your API usage and manage your plan
							</p>
						</div>
					</div>

					{(isLowTokens || balance.is_expired) && (
						<Button
							variant="primary"
							size="sm"
							onClick={handleUpgrade}
							className="flex items-center gap-2">
							<ArrowUp size={16} />
							{balance.is_expired ? "Renew Plan" : "Upgrade Plan"}
						</Button>
					)}
				</div>
			</div>

			{/* Usage Overview */}
			<div className="p-6">
				{/* Main Usage Display */}
				<div className="mb-6">
					<div className="flex items-center justify-between mb-2">
						<span
							className={`text-sm font-medium ${
								isDarkMode ? "text-gray-300" : "text-gray-700"
							}`}>
							Current Balance
						</span>
						<span
							className={`text-sm ${
								isDarkMode ? "text-gray-400" : "text-gray-600"
							}`}>
							{balance.current_tokens.toLocaleString()} tokens
							remaining
						</span>
					</div>

					<div
						className={`w-full bg-gray-200 rounded-full h-3 ${
							isDarkMode ? "bg-gray-700" : "bg-gray-200"
						}`}>
						<div
							className={`h-3 rounded-full transition-all duration-300 ${getProgressBarColor()}`}
							style={{
								width: `${Math.max(
									5,
									Math.min(100 - usagePercentage, 100)
								)}%`,
							}}
						/>
					</div>

					<div className="flex items-center justify-between mt-2">
						<span
							className={`text-2xl font-bold ${getUsageColor()}`}>
							{balance.current_tokens.toLocaleString()} tokens
						</span>
						<div className="text-right">
							<div
								className={`text-sm ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								}`}>
								{balance.is_expired
									? "Expired"
									: `${balance.days_remaining} days left`}
							</div>
							<div
								className={`text-xs ${
									isDarkMode
										? "text-gray-500"
										: "text-gray-500"
								}`}>
								{balance.expires_at
									? new Date(
											balance.expires_at
									  ).toLocaleDateString()
									: "No expiry"}
							</div>
						</div>
					</div>
				</div>

				{/* Warning Messages */}
				{balance.is_expired && (
					<div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
						<div className="flex items-start gap-3">
							<AlertCircle
								size={20}
								className="text-red-500 flex-shrink-0 mt-0.5"
							/>
							<div>
								<h4 className="text-red-500 font-medium mb-1">
									Tokens Expired
								</h4>
								<p
									className={`text-sm ${
										isDarkMode
											? "text-gray-300"
											: "text-gray-700"
									}`}>
									Your tokens have expired. Purchase a new
									plan to continue using the service.
								</p>
							</div>
						</div>
					</div>
				)}

				{isVeryLowTokens && !balance.is_expired && (
					<div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
						<div className="flex items-start gap-3">
							<AlertCircle
								size={20}
								className="text-red-500 flex-shrink-0 mt-0.5"
							/>
							<div>
								<h4 className="text-red-500 font-medium mb-1">
									Critical: Very Low Tokens
								</h4>
								<p
									className={`text-sm ${
										isDarkMode
											? "text-gray-300"
											: "text-gray-700"
									}`}>
									You have only {balance.current_tokens}{" "}
									tokens remaining. Consider upgrading your
									plan to avoid service interruption.
								</p>
							</div>
						</div>
					</div>
				)}

				{isLowTokens && !isVeryLowTokens && !balance.is_expired && (
					<div className="mb-6 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
						<div className="flex items-start gap-3">
							<AlertCircle
								size={20}
								className="text-yellow-500 flex-shrink-0 mt-0.5"
							/>
							<div>
								<h4 className="text-yellow-500 font-medium mb-1">
									Warning: Low Tokens
								</h4>
								<p
									className={`text-sm ${
										isDarkMode
											? "text-gray-300"
											: "text-gray-700"
									}`}>
									You have {balance.current_tokens} tokens
									remaining. Consider monitoring usage or
									upgrading your plan.
								</p>
							</div>
						</div>
					</div>
				)}

				{/* Stats Grid */}
				<div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
					<div
						className={`p-4 border rounded-lg ${
							isDarkMode
								? "border-gray-600 bg-gray-750"
								: "border-gray-300 bg-gray-50"
						}`}>
						<div className="flex items-center gap-2 mb-2">
							<Zap size={16} className="text-[#2F81F7]" />
							<span
								className={`text-sm ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								}`}>
								Available
							</span>
						</div>
						<div
							className={`text-xl font-bold ${
								isDarkMode ? "text-white" : "text-[#111215]"
							}`}>
							{balance.current_tokens.toLocaleString()}
						</div>
						<div
							className={`text-xs ${
								isDarkMode ? "text-gray-500" : "text-gray-500"
							}`}>
							tokens left
						</div>
					</div>

					<div
						className={`p-4 border rounded-lg ${
							isDarkMode
								? "border-gray-600 bg-gray-750"
								: "border-gray-300 bg-gray-50"
						}`}>
						<div className="flex items-center gap-2 mb-2">
							<TrendingUp size={16} className="text-[#10A37F]" />
							<span
								className={`text-sm ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								}`}>
								Daily Avg
							</span>
						</div>
						<div
							className={`text-xl font-bold ${
								isDarkMode ? "text-white" : "text-[#111215]"
							}`}>
							{Math.round(avgDailyUsage)}
						</div>
						<div
							className={`text-xs ${
								isDarkMode ? "text-gray-500" : "text-gray-500"
							}`}>
							tokens/day
						</div>
					</div>

					<div
						className={`p-4 border rounded-lg ${
							isDarkMode
								? "border-gray-600 bg-gray-750"
								: "border-gray-300 bg-gray-50"
						}`}>
						<div className="flex items-center gap-2 mb-2">
							<CreditCard size={16} className="text-[#2F81F7]" />
							<span
								className={`text-sm ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								}`}>
								Est. Value
							</span>
						</div>
						<div
							className={`text-xl font-bold ${
								isDarkMode ? "text-white" : "text-[#111215]"
							}`}>
							{formatCurrency(
								balance.current_tokens * costPerToken
							)}
						</div>
						<div
							className={`text-xs ${
								isDarkMode ? "text-gray-500" : "text-gray-500"
							}`}>
							at {formatCurrency(costPerToken)}/token
						</div>
					</div>

					<div
						className={`p-4 border rounded-lg ${
							isDarkMode
								? "border-gray-600 bg-gray-750"
								: "border-gray-300 bg-gray-50"
						}`}>
						<div className="flex items-center gap-2 mb-2">
							<Calendar size={16} className="text-[#10A37F]" />
							<span
								className={`text-sm ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								}`}>
								Plan
							</span>
						</div>
						<div
							className={`text-xl font-bold ${
								isDarkMode ? "text-white" : "text-[#111215]"
							}`}>
							{balance.plan_type.charAt(0).toUpperCase() +
								balance.plan_type.slice(1)}
						</div>
						<div
							className={`text-xs ${
								isDarkMode ? "text-gray-500" : "text-gray-500"
							}`}>
							{balance.is_expired
								? "Expired"
								: `${balance.days_remaining} days left`}
						</div>
					</div>
				</div>

				{/* Usage History */}
				<div>
					<div className="flex items-center justify-between mb-4">
						<h4
							className={`font-semibold ${
								isDarkMode ? "text-white" : "text-[#111215]"
							}`}>
							Usage History
						</h4>
						<div className="flex items-center gap-2">
							<select
								value={timeRange}
								onChange={(e) =>
									setTimeRange(e.target.value as any)
								}
								className={`text-sm px-3 py-1 border rounded-md ${
									isDarkMode
										? "bg-gray-700 border-gray-600 text-white"
										: "bg-white border-gray-300 text-[#111215]"
								} focus:ring-2 focus:ring-[#10A37F] focus:border-[#10A37F]`}>
								<option value="7d">Last 7 days</option>
								<option value="30d">Last 30 days</option>
								<option value="90d">Last 90 days</option>
							</select>
						</div>
					</div>

					{/* Recent Usage List */}
					<div className="space-y-3">
						{filteredHistory
							.slice(0, 8)
							.map((usage: any, index: any) => (
								<div
									key={index}
									className={`flex items-center justify-between p-3 border rounded-lg ${
										isDarkMode
											? "border-gray-600 bg-gray-750"
											: "border-gray-300 bg-gray-50"
									}`}>
									<div className="flex items-center gap-3">
										<div className="p-2 rounded bg-gradient-to-r from-[#10A37F]/10 to-[#2F81F7]/10">
											{getServiceIcon(usage.service_name)}
										</div>
										<div>
											<div
												className={`text-sm font-medium ${
													isDarkMode
														? "text-white"
														: "text-[#111215]"
												}`}>
												{formatServiceName(
													usage.service_name
												)}
											</div>
											<div
												className={`text-xs ${
													isDarkMode
														? "text-gray-400"
														: "text-gray-600"
												}`}>
												{formatDate(usage.created_at)}
											</div>
										</div>
									</div>

									<div className="text-right">
										<div
											className={`text-sm font-medium ${
												isDarkMode
													? "text-white"
													: "text-[#111215]"
											}`}>
											-{usage.tokens_used} tokens
										</div>
										<div
											className={`text-xs ${
												isDarkMode
													? "text-gray-400"
													: "text-gray-600"
											}`}>
											{formatCurrency(
												usage.tokens_used * costPerToken
											)}
										</div>
									</div>
								</div>
							))}
					</div>

					{filteredHistory.length === 0 && (
						<div className="text-center py-8">
							<Clock
								size={32}
								className={`mx-auto mb-2 ${
									isDarkMode
										? "text-gray-600"
										: "text-gray-400"
								}`}
							/>
							<p
								className={`${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								}`}>
								No usage data for the selected period
							</p>
						</div>
					)}

					{filteredHistory.length > 8 && (
						<div className="text-center mt-4">
							<Button variant="ghost" size="sm">
								View All Usage History
							</Button>
						</div>
					)}
				</div>

				{/* Upgrade CTA */}
				{(balance.plan_type !== "ultimate" ||
					balance.is_expired ||
					isLowTokens) && (
					<div
						className={`mt-6 p-4 border rounded-lg ${
							isDarkMode
								? "border-[#10A37F]/20 bg-[#10A37F]/5"
								: "border-[#10A37F]/30 bg-[#10A37F]/10"
						}`}>
						<div className="flex items-center justify-between">
							<div>
								<h5
									className={`font-medium ${
										isDarkMode
											? "text-white"
											: "text-[#111215]"
									}`}>
									{balance.is_expired
										? "Tokens expired"
										: "Need more tokens?"}
								</h5>
								<p
									className={`text-sm ${
										isDarkMode
											? "text-gray-400"
											: "text-gray-600"
									}`}>
									{balance.is_expired
										? "Purchase a new plan to continue using our services"
										: "Upgrade to get more tokens and advanced features"}
								</p>
							</div>
							<Button
								variant="primary"
								size="sm"
								onClick={handleUpgrade}>
								<ArrowUp size={16} className="mr-2" />
								{balance.is_expired ? "Renew" : "Upgrade"}
							</Button>
						</div>
					</div>
				)}
			</div>
		</div>
	);
};

export default TokenUsage;
