/* eslint-disable @typescript-eslint/no-explicit-any */
import {
	Target,
	BarChart3,
	Zap,
	Brain,
	Sliders,
	CreditCard,
	Filter,
	Home,
	Briefcase,
	Bot,
	Sparkles,
	ShoppingCart,
	Chrome,
	Package,
} from "lucide-react";
import { SidebarSection } from "@/types/extension";

export const createExtensionsSidebarSections = (
	activeTab: string,
	setActiveTab: (tab: string) => void,
	tokenBalance: any,
	extensionsData: any
): SidebarSection[] => [
	{
		title: "General",
		items: [
			{
				icon: Home,
				label: "Home",
				active: activeTab === "home",
				onClick: () => setActiveTab("home"),
			},
			{
				icon: CreditCard,
				label: "Payment & Billing",
				active: activeTab === "billing",
				onClick: () => setActiveTab("billing"),
			},
			{
				icon: Zap,
				label: "Token Usage",
				active: activeTab === "tokens",
				onClick: () => setActiveTab("tokens"),
				badge: tokenBalance
					? `${tokenBalance.current_tokens}`
					: undefined,
			},
			{
				icon: Briefcase,
				label: "Personal Info",
				active: activeTab === "personal-info",
				onClick: () => setActiveTab("personal-info"),
				badge: extensionsData?.savedProducts?.length?.toString(),
			},
		],
	},
	{
		title: "AI Deal Sourcing",
		items: [
			{
				icon: Target,
				label: "CheckOra",
				active: activeTab === "analyzer",
				onClick: () => setActiveTab("analyzer"),
			},
			{
				icon: BarChart3,
				label: "Analytics",
				active: activeTab === "analytics",
				onClick: () => setActiveTab("analytics"),
			},
			{
				icon: Filter,
				label: "InspectOra",
				active: activeTab === "inspect-ora",
				onClick: () => setActiveTab("inspect-ora"),
			},
			{
				icon: Brain,
				label: "InspectOra AI",
				active: activeTab === "inspect-ora-ai",
				onClick: () => setActiveTab("inspect-ora-ai"),
			},
			{
				icon: Sliders,
				label: "SourceOra AI",
				active: activeTab === "source-ora",
				onClick: () => setActiveTab("source-ora"),
			},
			{
				icon: Chrome,
				label: "Download Extension",
				active: activeTab === "extension",
				onClick: () => setActiveTab("extension"),
			},
		],
	},
	{
		title: "Digital Workforce",
		items: [
			{
				icon: Bot,
				label: "Agents",
				active: activeTab === "ai-agent",
				onClick: () => setActiveTab("ai-agent"),
				badge: extensionsData?.savedProducts?.length?.toString(),
			},
			{
				icon: Sparkles,
				label: "Suggest Agents",
				active: activeTab === "request-agent",
				onClick: () => setActiveTab("request-agent"),
			},
		],
	},
	{
		title: "Cross Selling",
		items: [
			{
				icon: ShoppingCart,
				label: "ClickBuyDeals Store",
				active: activeTab === "clickbuy-store",
				onClick: () => setActiveTab("clickbuy-store"),
			},
		],
	},
	{
		title: "Systemise Fulfilment",
		items: [
			{
				icon: Package,
				label: "Fulfilment",
				active: activeTab === "systemise-fulfilment",
				onClick: () => setActiveTab("systemise-fulfilment"),
			},
		],
	},
];
