import { useProductContext } from "@/hooks/use-product-analysis";


const WarningAndRestrictions = ({ isDarkMode = true }) => {
  const { productData, isGated } = useProductContext();

  return (
    <div className={`grid grid-cols-3 gap-2 p-2 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
      <div className={`bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between ${isDarkMode ? 'bg-gray-900 border-gray-700' : 'bg-gray-100 border-gray-400'}`}>
        <span className={`text-[9.5px] font-bold whitespace-nowrap ${isDarkMode ? 'text-white' : 'text-black'}`}>
          AMZ in Buy Box:
        </span>
        <span
          className={`text-[9.5px] font-bold ${
            productData && productData.warnings?.amz_in_buy_box
              ? "text-red-600"
              : "text-green-600"
          }`}
        >
          {productData && productData.warnings?.amz_in_buy_box ? "Yes" : "No"}
        </span>
      </div>

      {/* Adult Product */}
      <div className={`bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between ${isDarkMode ? 'bg-gray-900 border-gray-700' : 'bg-gray-100 border-gray-400'}`}>
        <span className={`text-[9.5px] font-bold whitespace-nowrap ${isDarkMode ? 'text-white' : 'text-black'}`}>
          Adult Product:
        </span>
        <span
          className={`text-[9.5px] font-bold ${
            productData && productData.warnings?.adult_product
              ? "text-red-600"
              : "text-green-600"
          }`}
        >
          {productData && productData.warnings?.adult_product ? "Yes" : "No"}
        </span>
      </div>

      {/* Meltable Product */}
      <div className={`bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between ${isDarkMode ? 'bg-gray-900 border-gray-700' : 'bg-gray-100 border-gray-400'}`}>
        <span className={`text-[9.5px] font-bold whitespace-nowrap ${isDarkMode ? 'text-white' : 'text-black'}`}>
          Meltable Product:
        </span>
        <span
          className={`text-[9.5px] font-bold ${
            productData && productData.warnings?.meltable
              ? "text-red-600"
              : "text-green-600"
          }`}
        >
          {productData && productData.warnings?.meltable ? "Yes" : "No"}
        </span>
      </div>

      {/* Has Variations */}
      <div className={`bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between ${isDarkMode ? 'bg-gray-900 border-gray-700' : 'bg-gray-100 border-gray-400'}`}>
        <span className={`text-[9.5px] font-bold whitespace-nowrap ${isDarkMode ? 'text-white' : 'text-black'}`}>
          Has Variations:
        </span>
        <span
          className={`text-[9.5px] font-bold ${
            productData && productData.warnings?.variations?.is_variations
              ? "text-red-600"
              : "text-green-600"
          }`}
        >
          {productData && productData.warnings?.variations?.is_variations
            ? "Yes"
            : "No"}
        </span>
      </div>

      {/* Category Gated */}
      <div className={`bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between ${isDarkMode ? 'bg-gray-900 border-gray-700' : 'bg-gray-100 border-gray-400'}`}>
        <span className={`text-[9.5px] font-bold whitespace-nowrap ${isDarkMode ? 'text-white' : 'text-black'}`}>
          Category Gated:
        </span>
        <span
          className={`text-[9.5px] font-bold ${
            isGated ? "text-red-600" : "text-green-600"
          }`}
        >
          {isGated ? "Yes" : "No"}
        </span>
      </div>

      {/* Private Label */}
      <div className={`bg-gray-100 border border-gray-400 rounded py-[0.5px]  px-1 flex items-center justify-between ${isDarkMode ? 'bg-gray-900 border-gray-700' : 'bg-gray-100 border-gray-400'}`}>
        <span className={`text-[9.5px] font-bold whitespace-nowrap ${isDarkMode ? 'text-white' : 'text-black'}`}>
          Private Label:
        </span>
        <span className="text-[9.5px] font-bold text-green-600">No</span>
      </div>
    </div>
  );
};

export default WarningAndRestrictions;
