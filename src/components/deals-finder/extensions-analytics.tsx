/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from "react";
import {
	TrendingUp,
	BarChart3,
	Target,
	Zap,
	DollarSign,
	Package,
	Activity,
	PieChart,
} from "lucide-react";
import {
	LineChart,
	Line,
	XAxis,
	YAxis,
	CartesianGrid,
	Tooltip,
	ResponsiveContainer,
	BarChart,
	Bar,
	Pie<PERSON>hart as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	Cell,
} from "recharts";
import { ExtensionAnalytics } from "@/types/extension";

interface ExtensionsAnalyticsProps {
	analytics: ExtensionAnalytics;
	isDarkMode?: boolean;
}

const ExtensionsAnalytics: React.FC<ExtensionsAnalyticsProps> = ({
	analytics,
	isDarkMode = true,
}) => {
	const [timeRange, setTimeRange] = useState<"7d" | "30d" | "90d">("30d");
	const [activeChart, setActiveChart] = useState<
		"performance" | "categories" | "recommendations"
	>("performance");

	const colors = {
		primary: "#10A37F",
		secondary: "#2F81F7",
		accent: "#F59E0B",
		warning: "#EF4444",
		info: "#3B82F6",
	};

	const pieColors = ["#10A37F", "#2F81F7", "#F59E0B", "#EF4444", "#3B82F6"];

	const CustomTooltip = ({ active, payload, label }: any) => {
		if (active && payload && payload.length) {
			return (
				<div
					className={`${
						isDarkMode ? "bg-gray-700" : "bg-white"
					} border ${
						isDarkMode ? "border-gray-600" : "border-gray-200"
					} p-3 rounded-lg shadow-lg`}>
					<p
						className={`text-sm font-medium ${
							isDarkMode ? "text-white" : "text-[#111215]"
						}`}>
						{label}
					</p>
					{payload.map((entry: any, index: number) => (
						<p
							key={index}
							className="text-sm"
							style={{ color: entry.color }}>
							{entry.name}:{" "}
							{entry.name.includes("$") ||
							entry.name.includes("Profit")
								? `$${entry.value.toLocaleString()}`
								: entry.name.includes("%") ||
								  entry.name.includes("Accuracy")
								? `${entry.value.toFixed(1)}%`
								: entry.value.toLocaleString()}
						</p>
					))}
				</div>
			);
		}
		return null;
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat("en-GB", {
			style: "currency",
			currency: "GBP",
			minimumFractionDigits: 3,
		}).format(amount);
	};

	const getRecommendationData = () => {
		return analytics.aiUsage.topRecommendations.map((rec) => ({
			name: rec.type,
			value: rec.count,
			percentage: (
				(rec.count / analytics.aiUsage.totalAnalyses) *
				100
			).toFixed(1),
		}));
	};

	return (
		<div
			className={`${isDarkMode ? "bg-gray-800" : "bg-white"} border ${
				isDarkMode ? "border-gray-700" : "border-gray-200"
			} rounded-lg overflow-hidden min-h-screen`}>
			{/* Header */}
			<div className="p-6 border-b border-gray-700">
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-3">
						<div
							className={`${
								isDarkMode
									? "bg-gradient-to-r from-[#10A37F]/10 to-[#2F81F7]/10"
									: "bg-gradient-to-r from-[#10A37F]/10 to-[#2F81F7]/10"
							} p-2 rounded-lg`}>
							<BarChart3 size={20} className="text-[#10A37F]" />
						</div>
						<div>
							<h3
								className={`text-xl font-bold ${
									isDarkMode ? "text-white" : "text-[#111215]"
								}`}>
								Analytics Overview
							</h3>
							<p
								className={`text-sm ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								}`}>
								Track your extension performance and insights
							</p>
						</div>
					</div>

					<select
						value={timeRange}
						onChange={(e) => setTimeRange(e.target.value as any)}
						className={`px-3 py-2 border rounded-md text-sm ${
							isDarkMode
								? "bg-gray-700 border-gray-600 text-white"
								: "bg-white border-gray-300 text-[#111215]"
						} focus:ring-2 focus:ring-[#10A37F] focus:border-[#10A37F]`}>
						<option value="7d">Last 7 days</option>
						<option value="30d">Last 30 days</option>
						<option value="90d">Last 90 days</option>
					</select>
				</div>
			</div>

			{/* Key Metrics */}
			<div className="p-6 border-b border-gray-700">
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
					<div
						className={`p-4 border rounded-lg ${
							isDarkMode
								? "border-gray-600 bg-gray-750"
								: "border-gray-300 bg-gray-50"
						}`}>
						<div className="flex items-center gap-2 mb-2">
							<Package size={16} className="text-[#2F81F7]" />
							<span
								className={`text-sm ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								}`}>
								Total Products
							</span>
						</div>
						<div
							className={`text-2xl font-bold ${
								isDarkMode ? "text-white" : "text-[#111215]"
							}`}>
							{analytics.totalProducts.toLocaleString()}
						</div>
						<div
							className={`text-xs ${
								isDarkMode ? "text-gray-500" : "text-gray-500"
							}`}>
							{analytics.profitableProducts} profitable
						</div>
					</div>

					<div
						className={`p-4 border rounded-lg ${
							isDarkMode
								? "border-gray-600 bg-gray-750"
								: "border-gray-300 bg-gray-50"
						}`}>
						<div className="flex items-center gap-2 mb-2">
							<TrendingUp size={16} className="text-[#10A37F]" />
							<span
								className={`text-sm ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								}`}>
								Avg ROI
							</span>
						</div>
						<div className={`text-2xl font-bold text-[#10A37F]`}>
							{analytics.averageROI.toFixed(1)}%
						</div>
						<div
							className={`text-xs ${
								isDarkMode ? "text-gray-500" : "text-gray-500"
							}`}>
							across all products
						</div>
					</div>

					<div
						className={`p-4 border rounded-lg ${
							isDarkMode
								? "border-gray-600 bg-gray-750"
								: "border-gray-300 bg-gray-50"
						}`}>
						<div className="flex items-center gap-2 mb-2">
							<DollarSign size={16} className="text-[#2F81F7]" />
							<span
								className={`text-sm ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								}`}>
								Total Profit
							</span>
						</div>
						<div className={`text-2xl font-bold text-[#2F81F7]`}>
							{formatCurrency(analytics.totalProfit)}
						</div>
						<div
							className={`text-xs ${
								isDarkMode ? "text-gray-500" : "text-gray-500"
							}`}>
							potential profit
						</div>
					</div>

					<div
						className={`p-4 border rounded-lg ${
							isDarkMode
								? "border-gray-600 bg-gray-750"
								: "border-gray-300 bg-gray-50"
						}`}>
						<div className="flex items-center gap-2 mb-2">
							<Zap size={16} className="text-[#10A37F]" />
							<span
								className={`text-sm ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								}`}>
								AI Accuracy
							</span>
						</div>
						<div className={`text-2xl font-bold text-[#10A37F]`}>
							{analytics.aiUsage.accuracyRate.toFixed(1)}%
						</div>
						<div
							className={`text-xs ${
								isDarkMode ? "text-gray-500" : "text-gray-500"
							}`}>
							recommendation accuracy
						</div>
					</div>
				</div>
			</div>

			{/* Chart Section */}
			<div className="p-6">
				{/* Chart Navigation */}
				<div className="flex space-x-6 border-b border-gray-700 mb-6">
					{[
						{
							id: "performance",
							label: "Performance",
							icon: <Activity size={16} />,
						},
						{
							id: "categories",
							label: "Categories",
							icon: <PieChart size={16} />,
						},
						{
							id: "recommendations",
							label: "AI Recommendations",
							icon: <Target size={16} />,
						},
					].map((tab) => (
						<button
							key={tab.id}
							onClick={() => setActiveChart(tab.id as any)}
							className={`flex items-center gap-2 py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
								activeChart === tab.id
									? `border-[#10A37F] ${
											isDarkMode
												? "text-[#10A37F]"
												: "text-[#10A37F]"
									  }`
									: `border-transparent ${
											isDarkMode
												? "text-gray-400 hover:text-gray-200"
												: "text-gray-500 hover:text-gray-700"
									  }`
							}`}>
							{tab.icon}
							{tab.label}
						</button>
					))}
				</div>

				{/* Chart Content */}
				<div className="h-80">
					{activeChart === "performance" && (
						<ResponsiveContainer width="100%" height="100%">
							<LineChart data={analytics.performanceMetrics}>
								<CartesianGrid
									strokeDasharray="3 3"
									stroke={isDarkMode ? "#374151" : "#E5E7EB"}
								/>
								<XAxis
									dataKey="date"
									stroke={isDarkMode ? "#9CA3AF" : "#6B7280"}
									fontSize={12}
								/>
								<YAxis
									stroke={isDarkMode ? "#9CA3AF" : "#6B7280"}
									fontSize={12}
								/>
								<Tooltip content={<CustomTooltip />} />
								<Line
									type="monotone"
									dataKey="scans"
									stroke={colors.primary}
									strokeWidth={2}
									dot={{
										fill: colors.primary,
										strokeWidth: 2,
										r: 4,
									}}
									name="Scans"
								/>
								<Line
									type="monotone"
									dataKey="profits"
									stroke={colors.secondary}
									strokeWidth={2}
									dot={{
										fill: colors.secondary,
										strokeWidth: 2,
										r: 4,
									}}
									name="Profits ($)"
								/>
								<Line
									type="monotone"
									dataKey="accuracy"
									stroke={colors.accent}
									strokeWidth={2}
									dot={{
										fill: colors.accent,
										strokeWidth: 2,
										r: 4,
									}}
									name="Accuracy (%)"
								/>
							</LineChart>
						</ResponsiveContainer>
					)}

					{activeChart === "categories" && (
						<ResponsiveContainer width="100%" height="100%">
							<BarChart data={analytics.topCategories}>
								<CartesianGrid
									strokeDasharray="3 3"
									stroke={isDarkMode ? "#374151" : "#E5E7EB"}
								/>
								<XAxis
									dataKey="category"
									stroke={isDarkMode ? "#9CA3AF" : "#6B7280"}
									fontSize={12}
									angle={-45}
									textAnchor="end"
									height={80}
								/>
								<YAxis
									stroke={isDarkMode ? "#9CA3AF" : "#6B7280"}
									fontSize={12}
								/>
								<Tooltip content={<CustomTooltip />} />
								<Bar
									dataKey="products"
									fill={colors.primary}
									radius={[4, 4, 0, 0]}
									name="Products"
								/>
								<Bar
									dataKey="avgProfit"
									fill={colors.secondary}
									radius={[4, 4, 0, 0]}
									name="Avg Profit ($)"
								/>
							</BarChart>
						</ResponsiveContainer>
					)}

					{activeChart === "recommendations" && (
						<div className="flex items-center justify-center h-full">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-8 w-full">
								<div>
									<h5
										className={`text-center font-semibold mb-4 ${
											isDarkMode
												? "text-white"
												: "text-[#111215]"
										}`}>
										AI Recommendations
									</h5>
									<ResponsiveContainer
										width="100%"
										height={250}>
										<RechartsPieChart>
											<Tooltip
												content={<CustomTooltip />}
											/>
											<RechartsPieChart.Pie
												data={getRecommendationData()}
												cx="50%"
												cy="50%"
												outerRadius={80}
												fill="#8884d8"
												dataKey="value"
												label={({
													name,
													percentage,
												}: {
													name: string;
													percentage: string | number;
												}) =>
													`${name} (${percentage}%)`
												}>
												{getRecommendationData().map(
													(entry, index) => (
														<Cell
															key={`cell-${index}`}
															fill={
																pieColors[
																	index %
																		pieColors.length
																]
															}
														/>
													)
												)}
											</RechartsPieChart.Pie>
										</RechartsPieChart>
									</ResponsiveContainer>
								</div>

								<div>
									<h5
										className={`font-semibold mb-4 ${
											isDarkMode
												? "text-white"
												: "text-[#111215]"
										}`}>
										Recommendation Breakdown
									</h5>
									<div className="space-y-3">
										{getRecommendationData().map(
											(rec, index) => (
												<div
													key={rec.name}
													className="flex items-center justify-between">
													<div className="flex items-center gap-2">
														<div
															className="w-3 h-3 rounded-full"
															style={{
																backgroundColor:
																	pieColors[
																		index %
																			pieColors.length
																	],
															}}
														/>
														<span
															className={
																isDarkMode
																	? "text-gray-300"
																	: "text-gray-700"
															}>
															{rec.name}
														</span>
													</div>
													<div className="text-right">
														<div
															className={`font-medium ${
																isDarkMode
																	? "text-white"
																	: "text-[#111215]"
															}`}>
															{rec.value.toLocaleString()}
														</div>
														<div
															className={`text-xs ${
																isDarkMode
																	? "text-gray-400"
																	: "text-gray-600"
															}`}>
															{rec.percentage}%
														</div>
													</div>
												</div>
											)
										)}
									</div>
								</div>
							</div>
						</div>
					)}
				</div>
			</div>

			{/* Scanning Stats */}
			<div className="p-6 border-t border-gray-700">
				<h4
					className={`font-semibold mb-4 ${
						isDarkMode ? "text-white" : "text-[#111215]"
					}`}>
					Scanning Performance
				</h4>

				<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
					<div className="text-center">
						<div
							className={`text-2xl font-bold ${
								isDarkMode ? "text-white" : "text-[#111215]"
							}`}>
							{analytics.scanningStats.totalScans.toLocaleString()}
						</div>
						<div
							className={`text-sm ${
								isDarkMode ? "text-gray-400" : "text-gray-600"
							}`}>
							Total Scans
						</div>
					</div>

					<div className="text-center">
						<div
							className={`text-2xl font-bold ${
								isDarkMode ? "text-white" : "text-[#111215]"
							}`}>
							{analytics.scanningStats.monthlyScans.toLocaleString()}
						</div>
						<div
							className={`text-sm ${
								isDarkMode ? "text-gray-400" : "text-gray-600"
							}`}>
							This Month
						</div>
					</div>

					<div className="text-center">
						<div className={`text-2xl font-bold text-[#10A37F]`}>
							{analytics.scanningStats.successRate.toFixed(1)}%
						</div>
						<div
							className={`text-sm ${
								isDarkMode ? "text-gray-400" : "text-gray-600"
							}`}>
							Success Rate
						</div>
					</div>

					<div className="text-center">
						<div
							className={`text-2xl font-bold ${
								isDarkMode ? "text-white" : "text-[#111215]"
							}`}>
							{analytics.scanningStats.averageTime.toFixed(1)}s
						</div>
						<div
							className={`text-sm ${
								isDarkMode ? "text-gray-400" : "text-gray-600"
							}`}>
							Avg Time
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default ExtensionsAnalytics;
