import React, {
  useState,
  useRef,
  useEffect,
  use<PERSON><PERSON>back,
  useMemo,
  KeyboardEvent,
  FormEvent,
} from "react";
import { X, Send, Bo<PERSON>, User, Loader } from "lucide-react";

import { ProductData, ProfitData } from "@/lib/types/home";
import { cn } from "@/lib/utils";
import { analyzeProductWithAI, buildConversationHistory } from "@/lib/api/productApi";

interface Message {
  id: string;
  text: string;
  sender: "user" | "bot";
  timestamp: Date;
  isStreaming?: boolean;
}

interface ChatBotProps {
  /** Controls visibility */
  isOpen: boolean;
  onClose: () => void;
  isDarkMode?: boolean;

  /** Injected product + profit context */
  productData?: ProductData;
  profitData?: ProfitData;

  /** Optional customisation (defaults provided) */
  assistantName?: string;
  assistantTagline?: string;
}

/* ────────────────────────────────────────────────────────────────────────── */
/*                               Markdown utils                              */
/* ────────────────────────────────────────────────────────────────────────── */

/* Convert a markdown table (set of pipe-delimited lines) to HTML */
const convertTableToHTML = (tableLines: string[]): string => {
  if (tableLines.length === 0) return "";

  const headerCells = tableLines[0]
    .split("|")
    .map((c) => c.trim())
    .filter(Boolean);

  let html =
    '<table class="min-w-full my-3 text-xs border-collapse border border-gray-300">';
  html += '<thead class="bg-gray-50"><tr>';
  headerCells.forEach(
    (c) =>
      (html += `<th class="border border-gray-300 px-2 py-1 text-left">${c}</th>`),
  );
  html += "</tr></thead>";

  if (tableLines.length > 1) {
    html += "<tbody>";
    for (let i = 1; i < tableLines.length; i++) {
      const cells = tableLines[i]
        .split("|")
        .map((c) => c.trim())
        .filter(Boolean);
      html += "<tr>";
      cells.forEach(
        (c) =>
          (html += `<td class="border border-gray-300 px-2 py-1">${c}</td>`),
      );
      html += "</tr>";
    }
    html += "</tbody>";
  }

  html += "</table>";
  return html;
};

/* Replace all markdown tables in a block with HTML tables */
const parseMarkdownTables = (text: string): string => {
  const lines = text.split("\n");
  const out: string[] = [];
  let i = 0;

  while (i < lines.length) {
    const line = lines[i];
    const next = lines[i + 1] ?? "";

    // Detect a table if the second line is a ---|--- separator
    if (
      line.includes("|") &&
      /^\s*\|?[\s\-\:]+\|[\s\-\:\|]*$/.test(next.trim())
    ) {
      const table: string[] = [line];
      i += 2;
      while (i < lines.length && lines[i].includes("|")) {
        table.push(lines[i]);
        i++;
      }
      out.push(convertTableToHTML(table));
    } else {
      out.push(line);
      i++;
    }
  }

  return out.join("\n");
};

/* Main markdown → HTML converter (very lightweight) */
const renderMarkdown = (raw: string): string => {
  let html = raw
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;");

  /* Code blocks */
  html = html.replace(
    /```([\s\S]*?)```/g,
    '<pre class="bg-gray-800 text-gray-100 p-3 rounded-md overflow-x-auto text-xs"><code>$1</code></pre>',
  );
  /* Inline code */
  html = html.replace(
    /`([^`]+)`/g,
    '<code class="bg-gray-200 text-gray-800 px-1 py-0.5 rounded text-xs font-mono">$1</code>',
  );
  /* Headers */
  html = html
    .replace(/^###### (.+)$/gim, '<h6 class="text-xs font-bold mt-2">$1</h6>')
    .replace(/^##### (.+)$/gim, '<h5 class="text-xs font-bold mt-2">$1</h5>')
    .replace(/^#### (.+)$/gim, '<h4 class="text-sm font-bold mt-2">$1</h4>')
    .replace(/^### (.+)$/gim, '<h3 class="text-base font-bold mt-2">$1</h3>')
    .replace(/^## (.+)$/gim, '<h2 class="text-lg font-bold mt-2">$1</h2>')
    .replace(/^# (.+)$/gim, '<h1 class="text-xl font-bold mt-2">$1</h1>');
  /* Bold + italic */
  html = html
    .replace(/\*\*([^*]+)\*\*/g, "<strong>$1</strong>")
    .replace(/\*([^*]+)\*/g, "<em>$1</em>");

  /* Tables */
  html = parseMarkdownTables(html);

  /* Numbered lists sometimes arrive as “1. 1. 1.” — insert newlines */
  html = html
    /* existing fix: separate consecutive list items that were merged       */
    .replace(/(\d+\.\s+[^\n]+?)(\d+\.\s+)/g, "$1\n$2")
    /* NEW: insert a newline before the FIRST item if it’s stuck to text    */
    .replace(/([^\n])(\d+\.\s+)/g, "$1\n$2");

  /* Lists */
  const lines = html.split("\n");
  const processed: string[] = [];
  let listType: "ul" | "ol" | null = null;
  let items: string[] = [];

  const flush = () => {
    if (!listType) return;
    processed.push(
      `<${listType} class="my-2 space-y-1 ${
        listType === "ol" ? "list-decimal pl-4" : ""
      }">`,
    );
    processed.push(...items);
    processed.push(`</${listType}>`);
    listType = null;
    items = [];
  };

  for (const l of lines) {
    const trimmed = l.trim();
    const ul = /^[-*+] (.+)$/.exec(trimmed);
    const ol = /^\d+\. (.+)$/.exec(trimmed);

    if (ul) {
      if (listType !== "ul") flush(), (listType = "ul");
      items.push(`<li>${ul[1]}</li>`);
    } else if (ol) {
      if (listType !== "ol") flush(), (listType = "ol");
      items.push(`<li>${ol[1]}</li>`);
    } else {
      flush();
      processed.push(l);
    }
  }
  flush();

  /* Blockquotes, HRs, links */
  html = processed
    .join("\n")
    .replace(
      /^> (.+)$/gim,
      '<blockquote class="border-l-4 pl-4 italic">$1</blockquote>',
    )
    .replace(/^---+$/gim, '<hr class="my-4 border-gray-300">')
    .replace(
      /\[([^\]]+)\]\(([^)]+)\)/g,
      '<a href="$2" class="text-gray-600 underline" target="_blank" rel="noopener">$1</a>',
    );

  /* Paragraph wrapper (skip if already HTML) */
  html = html
    .split("\n")
    .map((l) =>
      l.trim() === "" || l.trim().match(/^<\/?[\w\s="':;-]+>/)
        ? l
        : `<p class="mb-2">${l}</p>`,
    )
    .join("\n");

  return html.trim();
};

const MarkdownMessage: React.FC<{ content: string; className?: string, isDarkMode?: boolean }> = ({
  content,
  className,
  isDarkMode,
}) => (
  <div
    className={cn(
      "text-sm leading-relaxed",
      isDarkMode 
        ? "[&>h1]:text-white [&>p]:text-gray-300" 
        : "[&>h1]:!text-gray-900 [&>p]:!text-gray-700 [&>ol]:!text-gray-700",
      className,
    )}
    dangerouslySetInnerHTML={{ __html: renderMarkdown(content) }}
  />
);

/* ────────────────────────────────────────────────────────────────────────── */
/*                                Component                                  */
/* ────────────────────────────────────────────────────────────────────────── */

const ChatBot: React.FC<ChatBotProps> = ({
  isOpen,
  onClose,
  productData,
  profitData,
  assistantName = "Checkora AI",
  assistantTagline = "Product Analysis Assistant",
  isDarkMode = true,
}) => {
  /* 🔹 Build dynamic welcome message once */
  const welcomeMessage = useMemo(
    () =>
      `Hi! I'm **${assistantName}**.\n\nHere are three things I can help you with:\n\n` +
      "1. **Performance Check-ups** – analyze sales trends, reviews, and rankings.\n" +
      "2. **Profit Planning** – break down pricing, fees, and margins.\n" +
      "3. **Competitor Scouting** – surface rival pricing, keywords, and market share.\n\n" +
      "What would you like to know?",
    [assistantName],
  );

  /* 🔹 Messages state (initialised lazily so it sees welcomeMessage) */
  const [messages, setMessages] = useState<Message[]>(() => [
    {
      id: "welcome",
      text: welcomeMessage,
      sender: "bot",
      timestamp: new Date(),
    },
  ]);

  const [input, setInput] = useState("");
  const [loading, setLoading] = useState(false);

  const endRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  /* Auto-scroll */
  useEffect(
    () => endRef.current?.scrollIntoView({ behavior: "smooth" }),
    [messages],
  );
  useEffect(() => {
    if (isOpen) setTimeout(() => inputRef.current?.focus(), 100);
  }, [isOpen]);

  /* Utility to tidy repeated *** etc. before rendering */
  const clean = (txt: string) =>
    txt
      .replace(/^\s*\*{3,}\s*$/gm, "")
      .replace(/^\s*#{4,}\s*$/gm, "")
      .replace(/\n{3,}/g, "\n\n")
      .trim();

  /* ───────────────  Send handler  ─────────────── */
  const send = useCallback(
    async (e?: FormEvent) => {
      e?.preventDefault();
      if (!input.trim() || loading) return;

      /* 1️⃣  push user message */
      const userMsg: Message = {
        id: Date.now().toString(),
        text: input.trim(),
        sender: "user",
        timestamp: new Date(),
      };
      setMessages((m) => [...m, userMsg]);

      setInput("");
      setLoading(true);

      /* 2️⃣  create placeholder bot message */
      const botId = `${Date.now()}-bot`;
      setMessages((m) => [
        ...m,
        {
          id: botId,
          text: "",
          sender: "bot",
          timestamp: new Date(),
          isStreaming: true,
        },
      ]);

      /* 3️⃣  fetch / stream */
      try {
        const history = buildConversationHistory([...messages, userMsg]);
        const stream = await analyzeProductWithAI(
          userMsg.text,
          history,
          {
            asin: productData?.asin,
          },
          true, // stream = true
        );

        let buffer = "";

        if (
          stream &&
          typeof stream === "object" &&
          Symbol.asyncIterator in stream
        ) {
          /* Stream loop */
          for await (const chunk of stream as AsyncGenerator<string>) {
            buffer += chunk;
            const cleaned = clean(buffer);
            setMessages((m) =>
              m.map((msg) =>
                msg.id === botId
                  ? { ...msg, text: cleaned, isStreaming: true }
                  : msg,
              ),
            );
          }
          setMessages((m) =>
            m.map((msg) =>
              msg.id === botId
                ? { ...msg, text: clean(buffer), isStreaming: false }
                : msg,
            ),
          );
        } else {
          /* Non-stream fallback */
          const resp = stream as { response: string };
          setMessages((m) =>
            m.map((msg) =>
              msg.id === botId
                ? { ...msg, text: clean(resp.response), isStreaming: false }
                : msg,
            ),
          );
        }
      } catch (err) {
        console.error(err);
        setMessages((m) =>
          m.map((msg) =>
            msg.id === botId
              ? {
                  ...msg,
                  text: "I’m having trouble reaching the AI service right now – please try again shortly.",
                  isStreaming: false,
                }
              : msg,
          ),
        );
      } finally {
        setLoading(false);
      }
    },
    [input, loading, messages, productData, profitData],
  );

  const quick = (txt: string) => {
    if (loading) return;
    setInput(txt);
    setTimeout(() => send(), 50);
  };

  const onKey = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      send();
    }
  };

  if (!isOpen) return null;

  /* ──────────────  JSX  ────────────── */
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className={` rounded-lg shadow-xl w-full max-w-md mx-4 flex flex-col max-h-[600px] ${isDarkMode ? "bg-gray-800" : "bg-white"}`}>
        {/* Header */}
        <div className={`flex items-center justify-between p-4 border-b rounded-t-lg ${isDarkMode ? "border-gray-700 bg-gray-900" : "border-gray-200"}`}>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center">
              <Bot size={18} className="text-white" />
            </div>
            <div>
              <h3 className={`font-semibold text-gray-900 ${isDarkMode ? "text-white" : "text-gray-900"}`}>{assistantName}</h3>
              <p className={`text-xs text-gray-500 ${isDarkMode ? "text-gray-400" : "text-gray-500"}`}>{assistantTagline}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((m) => {
            const isUser = m.sender === "user";
            return (
              <div
                key={m.id}
                className={`flex ${isUser ? "justify-end" : "justify-start"}`}
              >
                <div className="flex items-start space-x-2 max-w-xs lg:max-w-md">
                  {!isUser && (
                    <div className="min-w-6 min-h-6 bg-gray-500 rounded-full flex items-center justify-center">
                      <Bot size={12} className="text-white" />
                    </div>
                  )}

                  <div
                    className={`px-3 py-2 rounded-lg ${
                      isUser ? "bg-gray-700 text-white" : "bg-gray-100"
                    } ${isDarkMode ? "bg-gray-700 text-white" : "bg-gray-100"}`}
                  >
                    {isUser ? (
                      <div className="text-sm whitespace-pre-wrap break-words">
                        <MarkdownMessage
                          content={m.text}
                          isDarkMode={isDarkMode}
                          className={`${
                            isDarkMode 
                              ? "[&>h1]:text-white [&>p]:text-white text-white" 
                              : "[&>h1]:!text-gray-900 [&>p]:!text-gray-900 !text-gray-900"
                          }`}
                        />
                      </div>
                    ) : (
                      <MarkdownMessage content={m.text} isDarkMode={isDarkMode} />
                    )}

                    {m.isStreaming && (
                      <div className="flex items-center mt-2">
                        <div className="flex space-x-1">
                          <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"></div>
                          <div
                            className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"
                            style={{ animationDelay: "0.1s" }}
                          ></div>
                          <div
                            className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"
                            style={{ animationDelay: "0.2s" }}
                          ></div>
                        </div>
                        <span className="ml-2 text-xs text-gray-500">
                          Analyzing…
                        </span>
                      </div>
                    )}

                    <span
                      className={`block mt-1 text-xs ${
                        isUser ? "text-gray-100" : "text-gray-500"
                      }`}
                    >
                      {m.timestamp.toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </span>
                  </div>

                  {isUser && (
                    <div className="w-6 h-6 bg-gray-500 rounded-full flex items-center justify-center">
                      <User size={12} className="text-white" />
                    </div>
                  )}
                </div>
              </div>
            );
          })}
          <div ref={endRef} />
        </div>

        {/* Input */}
        <div className={`p-4 rounded-b-lg ${isDarkMode ? "border-gray-700 bg-gray-900" : "border-gray-200 bg-white"}`}>
          <form onSubmit={send} className="flex space-x-2">
            <input
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={onKey}
              placeholder="Ask me about this product…"
              disabled={loading}
              className="flex-1 px-3 py-2 border rounded-lg text-sm focus:ring-2 focus:ring-black/10 disabled:opacity-50"
            />
            <button
              type="submit"
              disabled={!input.trim() || loading}
              className="px-4 py-2 bg-black text-white rounded-lg hover:bg-black/80 disabled:opacity-50"
            >
              {loading ? (
                <Loader size={16} className="animate-spin" />
              ) : (
                <Send size={16} />
              )}
            </button>
          </form>

          {/* Quick actions */}
          <div className="flex flex-wrap gap-1 mt-2">
            <button
              onClick={() =>
                quick("What's the profit potential of this product?")
              }
              disabled={loading}
              className={`px-2 py-1 text-xs border rounded ${isDarkMode ? "bg-gray-800 border-gray-700" : "border-gray-200"}`}
            >
              💰 Profit Analysis
            </button>
            <button
              onClick={() => quick("How competitive is this market?")}
              disabled={loading}
              className={`px-2 py-1 text-xs ${isDarkMode ? "bg-gray-800 border-gray-700" : "border-gray-200"} border rounded hover:bg-gray-50 `}
            >
              📊 Competition
            </button>
            <button
              onClick={() =>
                quick("What should I know about sourcing this product?")
              }
              disabled={loading}
              className={`px-2 py-1 text-xs ${isDarkMode ? "bg-gray-800 border-gray-700" : "border-gray-200"} border rounded hover:bg-gray-50 `}
            >
              🏭 Sourcing Tips
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatBot;
