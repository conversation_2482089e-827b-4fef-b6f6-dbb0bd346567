'use client'
import { MessageSquare } from "lucide-react";
import FeedbackModal from "./feedback-modal";
import { useState } from "react";
import { useTheme } from "@/hooks/useTheme";

const Feedback = () => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const { isDarkMode, mounted } = useTheme();

    // Don't render until theme is mounted to prevent hydration mismatch
    if (!mounted) {
        return null;
    }

  return (
    <div className="">
        <FeedbackModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
        />
        <div
          onClick={() => setIsModalOpen(!isModalOpen)}
          className={`absolute rounded-lg items-center justify-center flex bottom-8 right-8 px-4 py-2 cursor-pointer hover:scale-105 transition-all duration-200 hover:shadow-lg ${
            isDarkMode
              ? 'bg-gray-800 text-white border border-gray-700 hover:bg-gray-700'
              : 'bg-white text-black border border-gray-200 hover:bg-gray-50'
          }`}
        >
            <MessageSquare className="w-4 h-4 mr-2" />
            Feedback
        </div>
    </div>
  );
};

export default Feedback;
