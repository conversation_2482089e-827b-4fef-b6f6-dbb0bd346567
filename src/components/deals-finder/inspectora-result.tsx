/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { useMemo, useEffect, useCallback, useState } from "react";
import {
	Search,
	RefreshCw,
	AlertCircle,
	CheckCircle,
	XCircle,
	ExternalLink,
	ArrowUpDown,
	ChevronLeft,
	ChevronRight,
	BarChart3,
	Package,
	Weight,
	Star,
	Tag,
	Store,
	Loader2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

import ComingSoon from "./coming-soon";
import CheckoraModal from "./checkora-model";
import { useProductContext } from "@/hooks/use-product-analysis";
import { useInspectoraDetailsContext } from "@/hooks/use-get-inspectora-details";
import SourceOraModal from "./sourceora-modal";
import { useSourceora } from "@/hooks/use-post-source";

export interface SearchResult {
	id: number;
	asin: string;
	title: string;
	price: string;
	sales_rank: number;
	item_weight: string;
	amazon_in_buybox: boolean;
	amazon_in_listing: boolean;
	amazon_url?: string;
	category?: string;
	manufacturer?: string;
}

type SortField = keyof SearchResult;

interface InspectoraResultProps {
	isDarkMode: boolean;
	// searchResults: SearchResult[];
	resultCount: number;
	hasSearched: boolean;
	isSearching: boolean;
	cachedAsins: string[];
	searchError: string | null;
	// onProductsLoaded: (products: SearchResult[]) => void;
}

export interface SourceoraFilter {
	"target_stores": number,
	"price": number,
	"title": string
}

const InspectoraResult: React.FC<InspectoraResultProps> = ({
	isDarkMode,
	// searchResults,
	resultCount,
	hasSearched,
	isSearching,
	cachedAsins,
	searchError,
	// onProductsLoaded,
}) => {
	// Get all states and functions from context
	const {
		results: searchResults,
		setResults: setSearchResults,
		sortField,
		setSortField,
		sortDirection,
		setSortDirection,
		currentPage,
		setCurrentPage,
		itemsPerPage,
		setItemsPerPage,
		sourceoraModal,
		setSourceoraModal,
		checkoraModal,
		setCheckoraModal,
		checkModalAsin,
		setCheckModalAsin,
		currentBatch,
		setCurrentBatch,
		isLoadingMore,
		setIsLoadingMore,
		loadingError,
		setLoadingError,
		handleSort,
		handlePageChange,
		loadNextBatch,
		resetStates,
	} = useInspectoraDetailsContext();

	const { setAsin} = useProductContext();

	const maxAsinPerBatch = 10;
	const columns = [
		{ key: "asin", label: "ASIN", icon: Tag, sortable: true },
		{ key: "title", label: "Product Title", icon: Package, sortable: true },
		{ key: "brand", label: "Brand", icon: Store, sortable: true },
		{ key: "price", label: "Price", sortable: true },
		{ key: "sales_rank", label: "Sales Rank", icon: Star, sortable: true },
		{ key: "category", label: "Category", icon: BarChart3, sortable: true },
		{
			key: "checkora",
			label: "Checkora",
			icon: Weight,
			sortable: true,
		},
		{
			key: "sourceora",
			label: "SOURCEORA",
			icon: Store,
			sortable: true,
		},
	];
	const [sourceoraFilter, setSourceoraFilter] = useState<SourceoraFilter>({
		"target_stores": 0,
		"price": 0,
		"title": ""
	})

	const { mutate } = useSourceora();

	console.log(cachedAsins, "cached asins");

	// Calculate batch info
	const totalBatches = Math.ceil(cachedAsins.length / maxAsinPerBatch);
	const hasMoreBatches = currentBatch < totalBatches - 1;
	const loadedProductCount = searchResults.length;
	const remainingProducts = cachedAsins.length - loadedProductCount;



	// Create a wrapper function for loadNextBatch that provides the required parameters
	const handleLoadNextBatch = useCallback(() => {
		loadNextBatch(cachedAsins, maxAsinPerBatch);
	}, [loadNextBatch, cachedAsins, maxAsinPerBatch]);

	// Load initial batch when cached ASINs change
	useEffect(() => {
		if (
			cachedAsins.length > 0 &&
			searchResults.length === 0 &&
			!isLoadingMore
		) {
			handleLoadNextBatch();
		}
	}, [cachedAsins, searchResults.length, isLoadingMore, handleLoadNextBatch]);

	// Sort and paginate data
	const sortedAndPaginatedData = useMemo(() => {
		const sortedData = [...searchResults];

		// Sort data
		sortedData.sort((a, b) => {
			let aValue = a[sortField];
			let bValue = b[sortField];

			// Handle undefined values
			if (aValue === undefined && bValue === undefined) return 0;
			if (aValue === undefined) return 1;
			if (bValue === undefined) return -1;

			// Handle null values
			if (aValue === null && bValue === null) return 0;
			if (aValue === null) return 1;
			if (bValue === null) return -1;

			// Handle different data types
			if (typeof aValue === "string" && typeof bValue === "string") {
				if (!isNaN(Number(aValue)) && !isNaN(Number(bValue))) {
					aValue = Number(aValue);
					bValue = Number(bValue);
				} else {
					aValue = aValue.toLowerCase();
					bValue = bValue.toLowerCase();
				}
			}

			if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
			if (aValue > bValue) return sortDirection === "asc" ? 1 : -1;
			return 0;
		});

		// Paginate data
		const startIndex = (currentPage - 1) * itemsPerPage;
		const endIndex = startIndex + itemsPerPage;
		return sortedData.slice(startIndex, endIndex);
	}, [searchResults, sortField, sortDirection, currentPage, itemsPerPage]);

	// Calculate pagination info
	const totalPages = Math.ceil(searchResults.length / itemsPerPage);
	const startItem = (currentPage - 1) * itemsPerPage + 1;
	const endItem = Math.min(currentPage * itemsPerPage, searchResults.length);

	// Format cell content
	const formatCellContent = (
		value: any,
		columnKey: string,
		item: SearchResult
	) => {
		switch (columnKey) {
			case "price":
				return value === "0" ? "N/A" : `${value}`;
			case "sales_rank":
				return value === 0
					? "N/A"
					: `${Number(value).toLocaleString()}`;
			case "item_weight":
				return value;
			case "amazon_in_buybox":
			case "amazon_in_listing":
				return value ? (
					<div className="flex items-center gap-1 text-green-500">
						<CheckCircle className="w-4 h-4" />
						<span className="text-xs">Yes</span>
					</div>
				) : (
					<div className="flex items-center gap-1 text-red-500">
						<XCircle className="w-4 h-4" />
						<span className="text-xs">No</span>
					</div>
				);
			case "title":
				return (
					<div className="max-w-xs truncate" title={value}>
						{value}
					</div>
				);
			case "asin":
				return (
					<div className="flex items-center gap-2">
						<code className="text-xs bg-gray-500/20 px-2 py-1 rounded">
							{value}
						</code>
						{item.amazon_url && (
							<a
								href={item.amazon_url}
								target="_blank"
								rel="noopener noreferrer"
								className="text-[#19D86C] hover:text-[#12C2E9]">
								<ExternalLink className="w-3 h-3" />
							</a>
						)}
					</div>
				);
			case "category":
			case "manufacturer":
				return (
					<span className="text-xs bg-blue-100 dark:bg-blue-900 px-2 py-1 rounded">
						{value || "N/A"}
					</span>
				);
			default:
				return value || "N/A";
		}
	};


	if (isSearching) {
		return (
			<div className="flex items-center justify-center py-12">
				<div className="flex items-center gap-3">
					<RefreshCw className="animate-spin h-6 w-6 text-[#19D86C]" />
					<span
						className={isDarkMode ? "text-white" : "text-gray-900"}>
						Searching products...
					</span>
				</div>
			</div>
		);
	}

	if (searchError) {
		return (
			<div
				className={`p-6 rounded-lg border ${
					isDarkMode
						? "bg-gray-800 border-gray-700"
						: "bg-white border-gray-200"
				}`}>
				<div className="text-center py-12">
					<AlertCircle
						className={`h-12 w-12 mx-auto mb-4 ${
							isDarkMode ? "text-red-400" : "text-red-500"
						}`}
					/>
					<h3
						className={`text-lg font-medium mb-2 ${
							isDarkMode ? "text-white" : "text-gray-900"
						}`}>
						Search Failed
					</h3>
					<p
						className={`text-sm mb-4 ${
							isDarkMode ? "text-gray-400" : "text-gray-600"
						}`}>
						{searchError}
					</p>
				</div>
			</div>
		);
	}

	if (!hasSearched) {
		return (
			<div
				className={`p-6 rounded-lg border ${
					isDarkMode
						? "bg-gray-800 border-gray-700"
						: "bg-white border-gray-200"
				}`}>
				<div className="text-center py-12">
					<Search
						className={`h-12 w-12 mx-auto mb-4 ${
							isDarkMode ? "text-gray-600" : "text-gray-400"
						}`}
					/>
					<h3
						className={`text-lg font-medium mb-2 ${
							isDarkMode ? "text-white" : "text-gray-900"
						}`}>
						No Search Performed
					</h3>
					<p
						className={`text-sm ${
							isDarkMode ? "text-gray-400" : "text-gray-600"
						}`}>
						Use the Filter tab to set your search criteria and find
						products.
					</p>
				</div>
			</div>
		);
	}

	if (resultCount === 0) {
		return (
			<div
				className={`p-6 rounded-lg border ${
					isDarkMode
						? "bg-gray-800 border-gray-700"
						: "bg-white border-gray-200"
				}`}>
				<div className="text-center py-12">
					<AlertCircle
						className={`h-12 w-12 mx-auto mb-4 ${
							isDarkMode ? "text-gray-600" : "text-gray-400"
						}`}
					/>
					<h3
						className={`text-lg font-medium mb-2 ${
							isDarkMode ? "text-white" : "text-gray-900"
						}`}>
						No Results Found
					</h3>
					<p
						className={`text-sm ${
							isDarkMode ? "text-gray-400" : "text-gray-600"
						}`}>
						Try adjusting your search filters to find more products.
					</p>
				</div>
			</div>
		);
	}

	// Show loading state while initial batch is loading
	if (resultCount > 0 && searchResults.length === 0 && isLoadingMore) {
		return (
			<div
				className={`p-6 rounded-lg border ${
					isDarkMode
						? "bg-gray-800 border-gray-700"
						: "bg-white border-gray-200"
				}`}>
				<div className="text-center py-12">
					<Loader2
						className={`h-12 w-12 mx-auto mb-4 text-[#19D86C] animate-spin`}
					/>
					<h3
						className={`text-lg font-medium mb-2 ${
							isDarkMode ? "text-white" : "text-gray-900"
						}`}>
						Loading Product Details
					</h3>
					<p
						className={`text-sm ${
							isDarkMode ? "text-gray-400" : "text-gray-600"
						}`}>
						Found {resultCount} products, fetching details...
					</p>
				</div>
			</div>
		);
	}

	// Show error state if we have results count but failed to load any products
	if (resultCount > 0 && searchResults.length === 0 && loadingError) {
		return (
			<div
				className={`p-6 rounded-lg border ${
					isDarkMode
						? "bg-gray-800 border-gray-700"
						: "bg-white border-gray-200"
				}`}>
				<div className="text-center py-12">
					<AlertCircle
						className={`h-12 w-12 mx-auto mb-4 ${
							isDarkMode ? "text-red-400" : "text-red-500"
						}`}
					/>
					<h3
						className={`text-lg font-medium mb-2 ${
							isDarkMode ? "text-white" : "text-gray-900"
						}`}>
						Failed to Load Product Details
					</h3>
					<p
						className={`text-sm mb-4 ${
							isDarkMode ? "text-gray-400" : "text-gray-600"
						}`}>
						Found {resultCount} products but failed to fetch
						details: {loadingError}
					</p>
					<Button
						onClick={handleLoadNextBatch}
						className="bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] hover:from-[#12C2E9] hover:to-[#19D86C]">
						Try Again
					</Button>
				</div>
			</div>
		);
	}

	return (
		<div
			className={`rounded-lg border ${
				isDarkMode
					? "bg-gray-800 border-gray-700"
					: "bg-white border-gray-200"
			}`}>
			<SourceOraModal isOpen={sourceoraModal} onClose={() => setSourceoraModal(false)} sourceoraFilter={sourceoraFilter} />
			<CheckoraModal isOpen={checkoraModal} onClose={() => setCheckoraModal(false)} checkModalAsin={checkModalAsin} />
			{/* Results Header */}
			<div
				className={`px-6 py-4 border-b ${
					isDarkMode ? "border-gray-700" : "border-gray-200"
				}`}>
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-2">
						<Search className="w-5 h-5 text-[#19D86C]" />
						<h2
							className={`text-lg font-semibold ${
								isDarkMode ? "text-white" : "text-gray-900"
							}`}>
							Search Results
						</h2>
						<span
							className={`text-sm px-2 py-1 rounded-full ${
								isDarkMode
									? "bg-gray-700 text-gray-300"
									: "bg-gray-100 text-gray-600"
							}`}>
							{loadedProductCount} loaded of{" "}
							{resultCount.toLocaleString()} found
						</span>
					</div>
					<div className="flex items-center gap-2">
						<Select
							value={itemsPerPage.toString()}
							onValueChange={(value: any) => {
								setItemsPerPage(Number(value));
								setCurrentPage(1);
							}}>
							<SelectTrigger
								className={`w-20 ${
									isDarkMode
										? "bg-gray-700 border-gray-600"
										: "bg-white border-gray-300"
								}`}>
								<SelectValue />
							</SelectTrigger>
							<SelectContent
								className={
									isDarkMode
										? "bg-gray-800 border-gray-700"
										: "bg-white border-gray-300"
								}>
								<SelectItem value="25">25</SelectItem>
								<SelectItem value="50">50</SelectItem>
								<SelectItem value="100">100</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</div>
			</div>

			{/* Loading Progress */}
			{loadedProductCount < resultCount && (
				<div
					className={`px-6 py-3 border-b ${
						isDarkMode
							? "border-gray-700 bg-gray-800"
							: "border-gray-200 bg-gray-50"
					}`}>
					<div className="flex items-center justify-between mb-2">
						<span
							className={`text-sm ${
								isDarkMode ? "text-gray-300" : "text-gray-700"
							}`}>
							Loading progress... (Max 10 products per batch)
						</span>
						<span
							className={`text-sm ${
								isDarkMode ? "text-gray-400" : "text-gray-600"
							}`}>
							{Math.round(
								(loadedProductCount / resultCount) * 100
							)}
							%
						</span>
					</div>
					<div
						className={`w-full h-2 rounded-full ${
							isDarkMode ? "bg-gray-700" : "bg-gray-200"
						}`}>
						<div
							className="h-2 bg-gradient-to-r from-[#19D86C] to-[#12C2E9] rounded-full transition-all duration-300"
							style={{
								width: `${
									(loadedProductCount / resultCount) * 100
								}%`,
							}}
						/>
					</div>
				</div>
			)}

			{/* Results Table */}
			<div className="overflow-x-auto">
				<table className="w-full">
					<thead
						className={`${
							isDarkMode ? "bg-gray-900" : "bg-gray-50"
						}`}>
						<tr>
							{columns.map((column) => {
								return (
									<th
										key={column.key}
										className={`px-4 py-3 text-left text-xs font-medium uppercase tracking-wider ${
											isDarkMode
												? "text-gray-400"
												: "text-gray-500"
										} ${
											column.sortable
												? "cursor-pointer hover:bg-opacity-80"
												: ""
										}`}
										onClick={() =>
											column.sortable &&
											handleSort(column.key as SortField)
										}>
										<div className="flex items-center gap-2">
											<span>{column.label}</span>
											{column.sortable &&
												sortField === column.key && (
													<ArrowUpDown className="w-3 h-3" />
												)}
										</div>
									</th>
								);
							})}
						</tr>
					</thead>
					<tbody
						className={`divide-y ${
							isDarkMode ? "divide-gray-700" : "divide-gray-200"
						}`}>
						{sortedAndPaginatedData.map((item) => (
							<tr
								key={item.id}
								className={`${
									isDarkMode
										? "hover:bg-gray-700"
										: "hover:bg-gray-50"
								} transition-colors`}>
								{columns.map((column) => (
									<td
										key={column.key}
										className={`px-4 py-4 whitespace-nowrap text-sm ${
											isDarkMode
												? "text-gray-300"
												: "text-gray-700"
										}`}>
										{column.key === "checkora" ? (
											<button
												onClick={() => {
													setCheckoraModal(true)
													setAsin(item.asin);
													setCheckModalAsin(item.asin);
												}}
												className="cursor-pointer inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-md bg-brand-secondary/10 text-brand-secondary border border-brand-primary/20 hover:bg-brand-secondary/20"
											>
												<BarChart3 size={12} />
												Analyze
											</button>
										) : column.key === "sourceora" ? (
											<button
												onClick={() => {
													setSourceoraModal(true)
													mutate({
														"target_stores": 20,
														"price": Number(item.price),
														"title": item.title
													})
												}}
												className="cursor-pointer inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-md bg-brand-secondary/10 text-brand-secondary border border-brand-primary/20 hover:bg-brand-secondary/20"
											>
												<BarChart3 size={12} />
												Analyze
											</button>
										) : (
											formatCellContent(
												item[column.key as keyof SearchResult],
												column.key,
												item
											)
										)}
									</td>
								))}
							</tr>
						))}
					</tbody>
				</table>
			</div>

			{/* Load More Section */}
			{hasMoreBatches && (
				<div
					className={`px-6 py-4 border-t ${
						isDarkMode ? "border-gray-700" : "border-gray-200"
					}`}>
					<div className="flex items-center justify-center">
						<Button
							onClick={handleLoadNextBatch}
							disabled={isLoadingMore}
							className="bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] hover:from-[#12C2E9] hover:to-[#19D86C] disabled:opacity-50">
							{isLoadingMore ? (
								<div className="flex items-center gap-2">
									<Loader2 className="w-4 h-4 animate-spin" />
									Loading{" "}
									{Math.min(
										maxAsinPerBatch,
										remainingProducts
									)}{" "}
									more...
								</div>
							) : (
								`Load More Products (${remainingProducts} remaining)`
							)}
						</Button>
					</div>

					{loadingError && (
						<div className="mt-3 text-center">
							<p className="text-red-500 text-sm">
								{loadingError}
							</p>
							<Button
								variant="outline"
								size="sm"
								onClick={handleLoadNextBatch}
								className="mt-2">
								Try Again
							</Button>
						</div>
					)}
				</div>
			)}

			{/* Pagination */}
			{totalPages > 1 && (
				<div
					className={`px-6 py-4 border-t ${
						isDarkMode ? "border-gray-700" : "border-gray-200"
					}`}>
					<div className="flex items-center justify-between">
						<div
							className={`text-sm ${
								isDarkMode ? "text-gray-400" : "text-gray-600"
							}`}>
							Showing {startItem} to {endItem} of{" "}
							{searchResults.length} loaded results
						</div>
						<div className="flex items-center gap-2">
							<Button
								variant="outline"
								size="sm"
								onClick={() =>
									handlePageChange(currentPage - 1)
								}
								disabled={currentPage === 1}
								className={`${
									isDarkMode
										? "border-gray-600 text-gray-300"
										: "border-gray-300 text-gray-700"
								}`}>
								<ChevronLeft className="w-4 h-4" />
								Previous
							</Button>

							{/* Page numbers */}
							<div className="flex items-center gap-1">
								{Array.from(
									{ length: Math.min(5, totalPages) },
									(_, i) => {
										let pageNum;
										if (totalPages <= 5) {
											pageNum = i + 1;
										} else if (currentPage <= 3) {
											pageNum = i + 1;
										} else if (
											currentPage >=
											totalPages - 2
										) {
											pageNum = totalPages - 4 + i;
										} else {
											pageNum = currentPage - 2 + i;
										}

										return (
											<Button
												key={pageNum}
												variant={
													currentPage === pageNum
														? "default"
														: "outline"
												}
												size="sm"
												onClick={() =>
													handlePageChange(pageNum)
												}
												className={`w-8 h-8 p-0 ${
													currentPage === pageNum
														? "bg-[#19D86C] text-[#111215] hover:bg-[#12C2E9]"
														: isDarkMode
														? "border-gray-600 text-gray-300"
														: "border-gray-300 text-gray-700"
												}`}>
												{pageNum}
											</Button>
										);
									}
								)}
							</div>

							<Button
								variant="outline"
								size="sm"
								onClick={() =>
									handlePageChange(currentPage + 1)
								}
								disabled={currentPage === totalPages}
								className={`${
									isDarkMode
										? "border-gray-600 text-gray-300"
										: "border-gray-300 text-gray-700"
								}`}>
								Next
								<ChevronRight className="w-4 h-4" />
							</Button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default InspectoraResult;
