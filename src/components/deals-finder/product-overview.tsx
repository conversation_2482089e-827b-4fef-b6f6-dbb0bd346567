"use client";
import Image from "next/image";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { Box, Info, List, Loader, Lock, TrendingUp } from "lucide-react";

import { useEffect, useState } from "react";
import {
  formatToCurrency,
  formatWithCommas,
  getCurrencySymbol,
  getCurrentProfit,
  getCurrentROI,
  getGlobalESearchParams,
} from "@/lib/index";

import { useAuth } from "@/hooks/use-auth";
import { useProductContext } from "@/hooks/use-product-analysis";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";

const Overview = ({ isDarkMode = true }) => {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  const { user } = useAuth();
  const {
    productData,
    setProductData,
    selectedCountry,
    isVatRegistered,
    inputBuyBoxPrice,
    setInputBuyBoxPrice,
    inputPurchasePrice,
    setInputPurchasePrice,
    profitLoading,
    hasNotProfitLoaded,
    asin
  } = useProductContext();

  const handlePurchasePriceChange = (value: string) => {
    setInputPurchasePrice(value);
  };

  const handleBuyBoxPriceChange = (value: string) => {
    setInputBuyBoxPrice(value);
  };

  const formatCurrency = (value: number | null | undefined) => {
    if (value === undefined || value === null)
      return `${getCurrencySymbol(selectedCountry)}0.00`;
    return `${getCurrencySymbol(selectedCountry)}${value.toFixed(2)}`;
  };

  console.log('inputPurchasePrice ===>', inputPurchasePrice);

  return (
    <div className={`border p-2 border-black ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-100 border-gray-200'} `}>
     
      <div className="flex gap-2 mb-2">
        <div className="w-1/4 h-fit border p-1 rounded-md">
          <div className="relative aspect-square">
            <Image
              src={
                (productData && productData.mainImage) ||
                "/api/placeholder/200/200"
              }
              alt={(productData && productData.title) || "Amazon Product"}
              width={200}
              height={200}
              className="object-contain w-full h-full"
            />
          </div>
        </div>

        <div className="w-3/4">
          <div className={`whitespace-normal break-words text-xs font-bold pb-1 ${isDarkMode ? 'text-white' : 'text-black'}`}>
            {(productData && productData.title) ||
              "Product Title Not Available"}
          </div>

          <div className="border-b border-gray-200 my-1"></div>

          {/* Secondary info */}
          <div className={`grid grid-cols-2 gap-x-2 gap-y-0 text-[11px] mb-2 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            <div>
              <span className="font-medium">ASIN:</span>{" "}
              {(asin && asin) || "N/A"}
            </div>
            <div>
              <span className="font-medium">EAN:</span>{" "}
              {productData && productData.ean !== "Not available without API"
                ? productData?.ean || "N/A"
                : "N/A"}
            </div>
            <div>
              <span className="font-medium">Brand:</span>{" "}
              {(productData && productData.brand) || "N/A"}
            </div>
            <div>
              <span className="font-medium">Rating:</span>{" "}
              {productData && productData.rating
                ? `${parseFloat(productData.rating.split(" ")[0]).toFixed(2)} (${productData.reviewCount || 0})`
                : "0.00 (0)"}
            </div>
          </div>

          {/* Buttons for Product Details */}
          <div className="flex flex-wrap gap-1 mt-1 mb-1">
            <div className="relative inline-block">
              <Popover>
                <PopoverTrigger asChild>
                  <button className={`flex items-center px-1.5 py-0.5 rounded text-xs cursor-pointer border ${isDarkMode ? 'text-white !bg-gray-800 hover:bg-gray-700 border-gray-700' : 'text-black bg-gray-100 hover:bg-gray-200 border-gray-200'}`}>
                    <Box size={10} className="mr-0.5" />
                    Dimensions
                  </button>
                </PopoverTrigger>
                <PopoverContent
                  className={`w-[220px] p-2 text-xs ${isDarkMode ? 'text-white bg-gray-800 border-gray-700' : 'text-black bg-gray-100 border-gray-200'}`}
                  align="start"
                >
                  <p className="mb-0.5">
                    <strong>Dimensions:</strong>
                  </p>
                  <ul className={`list-none ${isDarkMode ? 'text-white' : 'text-black'}`}>
                    <li>
                      <strong>Height:</strong>{" "}
                      {(productData &&
                        productData.dimensions &&
                        productData.dimensions.height.value) ||
                        "0"}{" "}
                      {(productData &&
                        productData.dimensions &&
                        productData.dimensions.height.unit) ||
                        ""}
                    </li>
                    <li>
                      <strong>Length:</strong>{" "}
                      {(productData &&
                        productData.dimensions &&
                        productData.dimensions.length.value) ||
                        "0"}{" "}
                      {(productData &&
                        productData.dimensions &&
                        productData.dimensions.length.unit) ||
                        ""}
                    </li>
                    <li>
                      <strong>Width:</strong>{" "}
                      {(productData &&
                        productData.dimensions &&
                        productData.dimensions.width.value) ||
                        "0"}{" "}
                      {(productData &&
                        productData.dimensions &&
                        productData.dimensions.width.unit) ||
                        ""}
                    </li>
                    <li>
                      <strong>Weight:</strong>{" "}
                      {(productData &&
                        productData.dimensions &&
                        productData.dimensions.weight.value) ||
                        "0"}{" "}
                      {(productData &&
                        productData.dimensions &&
                        productData.dimensions.weight.unit) ||
                        ""}
                    </li>
                  </ul>
                </PopoverContent>
              </Popover>
            </div>

            <div className="relative inline-block">
              <Popover>
                <PopoverTrigger asChild>
                  <button className={`flex items-center px-1.5 py-0.5 rounded text-xs cursor-pointer border ${isDarkMode ? 'text-white !bg-gray-800 hover:bg-gray-700 border-gray-700' : 'text-black bg-gray-100 hover:bg-gray-200 border-gray-200'}`}>
                    <List size={10} className="mr-0.5" />
                    Features
                  </button>
                </PopoverTrigger>
                <PopoverContent
                  className={`border border-black bg-white w-[250px] p-2 text-black ${isDarkMode ? 'text-white bg-gray-800 border-gray-700' : 'text-black bg-gray-100 border-gray-200'}`}
                  align="end"
                >
                  <div className="max-h-[200px] overflow-y-auto py-1">
                    {productData &&
                    productData.features &&
                    productData.features.length > 0 ? (
                      <ul className="list-disc pl-4 space-y-1">
                        {productData.features.map((feature, index) => (
                          <li key={index} className="text-xs">
                            {feature}
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-xs text-gray-300">
                        No feature information available
                      </p>
                    )}
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            {/* Links Popover */}
            <div className="relative inline-block">
              <Popover>
                <PopoverTrigger asChild>
                  <button className={`flex items-center px-1.5 py-0.5 rounded text-xs cursor-pointer border ${isDarkMode ? 'text-white !bg-gray-800 hover:bg-gray-700 border-gray-700' : 'text-black bg-gray-100 hover:bg-gray-200 border-gray-200'}`}>
                    <Box size={10} className="mr-0.5" />
                    Links
                  </button>
                </PopoverTrigger>
                <PopoverContent
                  className={`border border-black bg-white w-[220px] p-2 ${isDarkMode ? 'text-white bg-gray-800 border-gray-700' : 'text-black bg-gray-100 border-gray-200'}`}
                  align="end"
                >
                  <div className="flex flex-col gap-1">
                    {productData?.summary &&
                    (productData.summary as any)?.ebay_active_listing_url ? (
                      <a
                        href={
                          (productData?.summary as any)?.ebay_active_listing_url
                        }
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <button className="w-full bg-gray-100 text-gray-800 hover:bg-gray-200 px-1.5 py-0.5 rounded text-xs font-medium">
                          eBay Live
                        </button>
                      </a>
                    ) : (
                      <button
                        disabled
                        className="w-full bg-gray-100 text-gray-400 px-1.5 py-0.5 rounded text-xs font-medium"
                      >
                        eBay Live
                      </button>
                    )}

                    {productData?.summary &&
                    (productData.summary as any)?.ebay_sold_listing_url ? (
                      <a
                        href={
                          (productData?.summary as any)?.ebay_sold_listing_url
                        }
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <button className="w-full bg-green-100 text-green-800 hover:bg-green-200 px-1.5 py-0.5 rounded text-xs font-medium">
                          eBay Sold
                        </button>
                      </a>
                    ) : (
                      <button
                        disabled
                        className="w-full bg-gray-100 text-gray-400 px-1.5 py-0.5 rounded text-xs font-medium"
                      >
                        eBay Sold
                      </button>
                    )}

                    {productData?.summary &&
                    (productData.summary as any)?.hagglezon_url ? (
                      <a
                        href={(productData?.summary as any)?.hagglezon_url}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <button className="w-full bg-yellow-100 text-yellow-800 hover:bg-yellow-200 px-1.5 py-0.5 rounded text-xs font-medium">
                          Hagglezon
                        </button>
                      </a>
                    ) : (
                      <button
                        disabled
                        className="w-full bg-gray-100 text-gray-400 px-1.5 py-0.5 rounded text-xs font-medium"
                      >
                        Hagglezon
                      </button>
                    )}

                    <a
                      href={`https://globalesearch.com/?searchTerm=${productData?.summary?.ean?.[0] || productData?.asin}&lt=1&lt=2&sortBy=price&category=-1&searchInDescription=false&se=0&se=${getGlobalESearchParams(selectedCountry)}`}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <button className="w-full bg-purple-100 text-purple-800 hover:bg-purple-200 px-1.5 py-0.5 rounded text-xs font-medium">
                        Global
                      </button>
                    </a>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-1 mb-1">
        <div className="flex gap-2">
          <div className={`border border-black rounded p-1 flex flex-col items-center bg-gray-100 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-100 border-gray-200'}`}>
            <div className={`font-bold text-[10px] flex items-center gap-1 ${isDarkMode ? 'text-white' : 'text-black'}`}>
              Buy Price
              <Tooltip>
                <TooltipTrigger>
                  <Info className="w-3 h-3 inline-block text-muted-foreground mb-[1px] cursor-pointer" />
                </TooltipTrigger>
                <TooltipContent className="bg-black animate-in fade-in-50 duration-200 slide-in-from-bottom-2">
                  <p className="text-muted-foreground text-white font-normal">
                    Your total purchase cost, <br />
                    including any VAT.
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className="relative w-full mt-2">
              {user && (
                <div className="flex items-center border border-gray-400 rounded focus:ring-1 focus:ring-black focus:border-black gap-1 overflow-hidden">
                  <span className="text-xs inset-y-0 left-0 flex items-center pl-1 text-gray-500 shrink-0">
                    {getCurrencySymbol(selectedCountry)}
                  </span>
                  <input
                    type="text"
                    value={inputPurchasePrice}
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^0-9.]/g, "");
                      const parts = value.split(".");
                      const formattedValue =
                        parts.length > 2
                          ? parts[0] + "." + parts.slice(1).join("")
                          : value;
                      handlePurchasePriceChange(formattedValue);
                    }}
                    className={`w-full py-0 text-sm font-bold text-center min-w-0 ${isDarkMode ? 'text-white' : 'text-black'}`}
                  />
                </div>
              ) }
            </div>
          </div>

          <div className={`border border-black rounded p-1 flex flex-col items-center bg-gray-100 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-100 border-gray-200'}`}>
            <div className={`font-bold text-[10px] ${isDarkMode ? 'text-white' : 'text-black'}`}>Sells price</div>
            <div className="relative w-full mt-2">
              {user && (
                <div className="flex items-center border border-gray-400 rounded focus:ring-1 focus:ring-black focus:border-black gap-1 overflow-hidden">
                  <span className="text-xs inset-y-0 left-0 flex items-center pl-1 text-gray-500 shrink-0">
                    {getCurrencySymbol(selectedCountry)}
                  </span>
                  <input
                    type="text"
                    value={inputBuyBoxPrice}
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^0-9.]/g, "");
                      const parts = value.split(".");
                      const formattedValue =
                        parts.length > 2
                          ? parts[0] + "." + parts.slice(1).join("")
                          : value;
                      handleBuyBoxPriceChange(formattedValue);
                    }}
                    className={`w-full py-0 text-sm font-bold text-center min-w-0 ${isDarkMode ? 'text-white' : 'text-black'}`}
                  />
                </div>
              ) }
            </div>
          </div>
        </div>
        
        <div className=" w-full gap-1 grid grid-cols-12">
          <div className={`border col-span-4 border-black rounded p-1 flex flex-col items-center bg-gray-100 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-100 border-gray-200'} justify-between`}>
            <div className={`font-bold text-[10px] ${isDarkMode ? 'text-white' : 'text-black'}`}>BSR</div>
            <div className={`text-md font-bold mb-[2px] truncate w-full text-center ${isDarkMode ? 'text-white' : 'text-black'}`}>
              {formatWithCommas(Number(productData?.summary?.bsr)) || "N/A"}
            </div>
          </div>

          <div className={`border col-span-4 border-black rounded p-1 flex flex-col items-center bg-gray-100 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-100 border-gray-200'} justify-between`}>
            <div className={`font-bold text-[10px] flex items-center ${isDarkMode ? 'text-white' : 'text-black'}`}>
              <TrendingUp size={10} className="mr-0.5 shrink-0" />
              <div className="flex items-center gap-1">
                ROI
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="w-3 h-3 inline-block text-muted-foreground mb-[1px] cursor-pointer" />
                  </TooltipTrigger>
                  <TooltipContent className="bg-black animate-in fade-in-50 duration-200 slide-in-from-bottom-2">
                    <p className="text-muted-foreground text-white font-normal">
                      Shows your profit as a percentage of the purchase price.
                      <br />
                      Formula: (Profit ÷ Buy Price) x 100
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
            {hasNotProfitLoaded || profitLoading ? (
              <Loader size={14} className={`mr-1 animate-spin mb-2 ${isDarkMode ? 'text-white' : 'text-black'}`} />
            ) : (
              <div
                className={`text-md font-bold mb-[2px] truncate w-full text-center ${productData && getCurrentROI({ isVatRegistered, productData }) > 0 ? "text-green-600" : "text-red-600"} ${isDarkMode ? 'text-white' : 'text-black'}`}
              >
                {productData &&
                  getCurrentROI({ isVatRegistered, productData }).toFixed(1)}
                %
              </div>
            )}
          </div>

          <div className={`border col-span-4 border-black rounded p-1 flex flex-col items-center bg-gray-100 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-100 border-gray-200'} justify-between`}>
            <div className={`font-bold text-[10px] flex items-center gap-1 ${isDarkMode ? 'text-white' : 'text-black'}`}>
              Profit
              <Tooltip>
                <TooltipTrigger>
                  <Info className="w-3 h-3 inline-block text-muted-foreground mb-[1px] cursor-pointer" />
                </TooltipTrigger>
                <TooltipContent className="bg-black animate-in fade-in-50 duration-200 slide-in-from-bottom-2">
                  <p className="text-muted-foreground text-white font-normal">
                    Your selling price minus all costs.
                    <br />
                    For VAT-registered users, this calculation <br />
                    also accounts for the net VAT.
                    <br />
                  </p>
                </TooltipContent>
              </Tooltip>
            </div>
            {hasNotProfitLoaded || profitLoading ? (
              <Loader size={14} className={`mr-1 animate-spin mb-2 ${isDarkMode ? 'text-white' : 'text-black'}`} />
            ) : (
              <div
                className={`text-md font-bold mb-[2px] truncate w-full text-center ${productData && getCurrentProfit({ isVatRegistered, productData }) > 0 ? "text-green-600" : "text-red-600"}`}
              >
                <span className="inline-flex items-center">
                  <span className="shrink-0">{getCurrencySymbol(selectedCountry)}</span>
                  <span className="truncate">
                    {productData && getCurrentProfit({ isVatRegistered, productData }).toFixed(2)}
                  </span>
                </span>
              </div>
            )}
          </div>

        </div>
      </div>
    </div>
  );
};

export default Overview;
