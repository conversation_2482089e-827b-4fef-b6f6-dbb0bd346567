import React from "react";
import Button from "@/components/Button";

interface LoadingStateProps {
	isDarkMode: boolean;
}

export const ExtensionsLoadingState: React.FC<LoadingStateProps> = ({}) => {
	return (
		<div className="flex h-screen items-center justify-center bg-background text-foreground">
			<div className="text-center">
				<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-primary mx-auto mb-4"></div>
				<p>Loading your extensions dashboard...</p>
			</div>
		</div>
	);
};

interface ErrorStateProps {
	isDarkMode: boolean;
	onRetry: () => void;
}

export const ExtensionsErrorState: React.FC<ErrorStateProps> = ({
	onRetry,
}) => {
	return (
		<div className="flex h-screen items-center justify-center bg-background text-foreground">
			<div className="text-center">
				<div className="text-6xl mb-4">❌</div>
				<h3 className="text-xl font-semibold mb-2 text-foreground">
					Failed to Load Extensions
				</h3>
				<p className="mb-4 text-muted-foreground">
					Unable to load extensions data. Please try again.
				</p>
				<Button onClick={onRetry} variant="primary">
					Retry
				</Button>
			</div>
		</div>
	);
};
