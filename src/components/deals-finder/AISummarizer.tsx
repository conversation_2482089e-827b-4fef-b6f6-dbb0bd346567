// Updated AISummarizer with dummy design

import React, { useState } from "react";
import {
  TrendingUp,
  Lock,
  Loader,
  Check,
  X,
  Wand,
  Bo<PERSON>,
  Info,
} from "lucide-react";
import { Button } from "../ui/button";

import {
  formatEstimatedSales,
  getCurrentProfit,
  getCurrentROI,
} from "@/lib/index";
import { useScoreCalculator } from "@/hooks/useScoreCalculator";
import ComingSoon from "./coming-soon";
import { useProductContext } from "@/hooks/use-product-analysis";
import { useAuth } from "@/hooks/use-auth";
import ChatBot from "./ChatBot";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";


const AISummarizer = ({ isDarkMode = true }) => {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [isComingSoonOpen, setIsComingSoonOpen] = useState(false);

  const { user } = useAuth();
  const {
    productData,
    isGated,
    fullResponse,
    isVatRegistered,
    hasNotProfitLoaded,
    asin
  } = useProductContext();

  const { data: scoreData, isLoading } = useScoreCalculator({
    ScoreRequest: {
      amazon_in_bb: productData?.warnings?.amz_in_buy_box || false,
      asin_code: asin || "",
      bsr: Number(productData?.summary?.bsr) || 0,
      est_sales: productData?.estimated_sales || 0,
      gated: isGated || false,
      profit:
        (productData &&
          getCurrentProfit({
            isVatRegistered: isVatRegistered,
            productData: productData,
          })) ||
        0,
      roi:
        (productData &&
          getCurrentROI({
            isVatRegistered: isVatRegistered,
            productData: productData,
          })) ||
        0,
      stock_level: productData?.offers?.sellers_offers?.[0]?.stock || 0,
      amazon_buybox: productData?.warnings?.amz_in_buy_box || false,
      fullResponse: !hasNotProfitLoaded,
      isVatRegistered: isVatRegistered,
    },
  });

  // Get color for battery based on score percentage
  const getBatteryColor = (percentage: number): string => {
    if (percentage < 0.5) return "bg-red-500";
    if (percentage < 0.7) return "bg-yellow-500";
    return "bg-green-500";
  };

  // Get text color for score value
  const getScoreTextColor = (value: string): string => {
    switch (value.toLowerCase()) {
      case "excellent":
        return "text-green-600";
      case "good":
        return "text-green-500";
      case "average":
        return "text-yellow-500";
      case "weak":
        return "text-red-500";
      case "poor":
        return "text-red-500";
      case "very poor":
        return "text-red-600";
      default:
        return "text-gray-700";
    }
  };

  return (
    <div className={`overflow-hidden ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow`}>
      <ComingSoon isOpen={isComingSoonOpen} onClose={() =>setIsComingSoonOpen(false)} />
      <div className="">
        {user && (
          <>
            <div className="p-2">
              <div className="grid grid-cols-3 gap-2 ">
                <div className={`text-center col-span-1 border border-black rounded-md py-1 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-100 border-gray-200'}`}>
                  <div className={`font-bold text-[10px] flex items-center justify-center gap-1 ${isDarkMode ? 'text-white' : 'text-black'}`}>
                    <TrendingUp size={15} /> Est. Sales
                  </div>
                  <div className={`text-md font-bold ${isDarkMode ? 'text-white' : 'text-black'}`}>
                    {formatEstimatedSales(productData?.estimated_sales || 0)}
                  </div>
                </div>
                <div className={`text-center col-span-1 border border-black rounded-md py-1 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-100 border-gray-200'}`}>
                  <div className={`font-bold text-[10px] flex items-center justify-center gap-1 ${isDarkMode ? 'text-white' : 'text-black'}`}>
                    Gated
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="w-3 h-3 inline-block text-muted-foreground mb-[1px] cursor-pointer" />
                      </TooltipTrigger>
                      <TooltipContent className="bg-black animate-in fade-in-50 duration-200 slide-in-from-bottom-2">
                        <p className="text-muted-foreground text-white font-normal">
                          To check if you're approved to sell <br />
                          an item (your 'Gating' status), <br />
                          you must connect your Amazon account.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <div
                    className={`text-md font-bold ${isGated === undefined ? "text-black" : isGated ? "text-red-600" : "text-green-600"}`}
                  >
                    {isGated === undefined ? (
                      <div className="flex items-center justify-center gap-1">
                        N/A
                      </div>
                    ) : isGated ? (
                      <div className="flex items-center gap-1 justify-center">
                        <div className="w-fit h-fit rounded-full border border-red-600 p-[1px]">
                          <X size={14} />
                        </div>
                        Yes
                      </div>
                    ) : (
                      <div className="flex items-center justify-center gap-1">
                        <div className="w-fit h-fit rounded-full border border-green-600 p-[1px]">
                          <Check size={14} />
                        </div>
                        No
                      </div>
                    )}
                  </div>
                </div>
                <div className={`text-center col-span-1 border border-black rounded-md py-1 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-100 border-gray-200'}`}>
                  <div className={`font-bold text-[10px] ${isDarkMode ? 'text-white' : 'text-black'}`}>Amazon In Buy Box</div>
                  <div
                    className={`text-md font-bold ${productData && productData.warnings && productData.warnings.amz_in_buy_box ? "text-red-600" : "text-green-600"}`}
                  >
                    {productData &&
                    productData.warnings &&
                    productData.warnings.amz_in_buy_box
                      ? "Yes"
                      : "No"}
                  </div>
                </div>
              </div>

              <div className="mb-2 border my-2 p-2 flex flex-col gap-1 rounded-md">
                <div className="flex items-center justify-between mb-1">
                  <span className={`text-xs flex items-center gap-1 ${isDarkMode ? 'text-white' : 'text-black'}`}>
                    <Wand size={14} /> 
                    <div className="flex items-center gap-1">
                      CheckOra Product Score
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="w-3 h-3 inline-block text-muted-foreground mb-[1px] cursor-pointer" />
                        </TooltipTrigger>
                        <TooltipContent className="bg-black animate-in fade-in-50 duration-200 slide-in-from-bottom-2">
                          <p className="text-muted-foreground text-white font-normal">
                            This score is based on 7 key factors, including your{" "}
                            <br />
                            minimum profit, sales estimates, and whether Amazon
                            is <br />
                            a seller. You can adjust the importance of each
                            factor <br />
                            to personalise the AI's recommendations to your
                            strategy.
                          </p>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                  </span>
                  <div className="flex items-center gap-1">
                    {isLoading ? (
                      <div className="flex items-center text-sm text-gray-600">
                        <Loader size={14} className="mr-1 animate-spin" />
                        Calculating...
                      </div>
                    ) : (
                      <span
                        className={`text-xs  ${scoreData && getScoreTextColor(scoreData.value)}`}
                      >
                        {scoreData && scoreData.value} (
                        {scoreData && scoreData.percentage.toFixed(1)}%)
                      </span>
                    )}
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="w-3 h-3 inline-block text-muted-foreground mb-[1px] cursor-pointer" />
                      </TooltipTrigger>
                      <TooltipContent className="bg-black animate-in fade-in-50 duration-200 slide-in-from-bottom-2">
                        <p className="text-muted-foreground text-white font-normal">
                          The AI does more than repeat data. <br />
                          It also provides trend analysis, <br />
                          expert opinions, and data from <br />
                          other sources.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </div>
                <div className="w-full bg-gray-200 mb-1 border border-gray-400 overflow-hidden rounded-full h-4">
                  {isLoading ? (
                    <div className="h-4 w-full bg-gray-300 animate-pulse rounded-full"></div>
                  ) : (
                    <div
                      className="h-4 rounded-full -ms-[1px] -mt-[1px] transition-all duration-1000 ease-out border border-gray-400"
                      style={{
                        width: `${scoreData && scoreData.percentage}%`,
                        backgroundColor:
                          (scoreData && scoreData.color) ||
                          (scoreData && scoreData.percentage < 0.5
                            ? "#ef4444"
                            : scoreData && scoreData.percentage < 0.7
                              ? "#eab308"
                              : "#22c55e"),
                      }}
                    ></div>
                  )}
                </div>
                <button
                  className={`w-full bg-black text-white py-[2px] rounded font-bold flex items-center justify-center gap-1 cursor-pointer ${isDarkMode ? 'bg-gray-900 hover:bg-gray-800' : 'bg-gray-600 hover:bg-gray-500'}`}
                  onClick={() => setIsChatOpen(true)}
                >
                  <Bot /> Initiate CheckOra
                </button>
              </div>

              <div className="grid grid-cols-2 gap-1">
              <Tooltip>
                  <TooltipTrigger asChild>
                    <a  href={`https://clickbuy.ai/aep-solutions/platforms?tab=inspect-ora&section=filter&asin=${asin || ""}&country=uk`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`text-black font-bold border border-black py-[2px] text-xs rounded text-xs flex items-center justify-center gap-1 ${isDarkMode ? 'text-white border-gray-700' : 'text-black border-gray-200'}`} >
                      <Bot size={14} /> Explore InspectOra
                    </a>
                  </TooltipTrigger>
                  <TooltipContent className="bg-black animate-in fade-in-50 duration-200 slide-in-from-bottom-2">
                    <p className="text-muted-foreground text-white font-normal">
                      Use the AI to find and analyse <br />
                      up to 100 similar products.
                    </p>
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <button className={` text-black font-bold border border-black py-[2px] text-xs rounded text-xs flex items-center justify-center gap-1 ${isDarkMode ? 'text-white border-gray-700' : 'text-black border-gray-200'}`} onClick={() => setIsComingSoonOpen(true)}>
                      <Bot size={14} /> Explore SourceOra
                    </button>
                  </TooltipTrigger>
                  <TooltipContent className="bg-black animate-in fade-in-50 duration-200 slide-in-from-bottom-2">
                    <p className="text-muted-foreground text-white font-normal">
                      Our AI agent will search the web to find <br />
                      profitable suppliers for this product.
                    </p>
                  </TooltipContent>
                </Tooltip>
  
               
              </div>
            </div>

            {isChatOpen && (
              <ChatBot
                isOpen={isChatOpen}
                onClose={() => setIsChatOpen(false)}
                productData={productData ?? undefined}
                isDarkMode={isDarkMode}
              />
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default AISummarizer;
