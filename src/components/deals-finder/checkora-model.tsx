"use client";

import React, { useState } from "react";
import { Clock, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTheme } from "@/hooks/useTheme";
import {
	Search,
	Zap,
	AlertTriangle,
	RefreshCw,
	Loader2,
} from "lucide-react";
import { ProductAnalysis } from "@/types/extension";
import Overview from "./product-overview";
import { useProductContext } from "@/hooks/use-product-analysis";
import { Input } from "../ui/input";
import AccordionData from "./accordion-data";


interface ComingSoonProps {
  isOpen: boolean;
  onClose: () => void;
  checkModalAsin: string | null;
}

const CheckoraModal: React.FC<ComingSoonProps> = ({ isOpen, onClose }) => {
  const { isDarkMode } = useTheme();
  const [asin, setAsin] = useState<string | null>(null);
	const { productData, keepaLoading} = useProductContext();
	const [analyzing, setAnalyzing] = useState(false);
	const [currentAnalysis, setCurrentAnalysis] = useState<ProductAnalysis | null>(null)
		// useState<ProductAnalysis | null>(getDefaultAnalysis(recentAnalyses));
	const [activeView, setActiveView] = useState<
		"overview" | "trends" | "competitors"
	>("overview");


  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm ${
      isDarkMode ? 'bg-black/70' : 'bg-white/70'
    }`}>
      <div className={`relative w-full max-w-5xl max-h-[80vh] overflow-y-auto mx-4 p-8 rounded-2xl border shadow-2xl ${
        isDarkMode
          ? 'bg-gray-800 border-gray-700'
          : 'bg-white border-gray-200'
      }`}>
        <button
          onClick={onClose}
          className={`absolute cursor-pointer top-4 right-4 p-2 rounded-lg transition-colors ${
            isDarkMode
              ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200'
              : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
          }`}
        >
          <X size={20} />
        </button>

        {/* content */}

      
			<div className="">
				<div className="flex items-center justify-between mb-4">
					<div>
						<h3
							className={`text-xl font-bold ${
								isDarkMode ? "text-white" : "text-gray-900"
							}`}>
							Product Analyzer
						</h3>
						<p
							className={`text-sm mt-1 ${
								isDarkMode ? "text-gray-400" : "text-gray-600"
							}`}>
							AI-powered Amazon product analysis with
							profitability insights
						</p>
					</div>

				</div>

				{keepaLoading && (
					<div className="mt-4 py-10 flex flex-col items-center justify-center border rounded-lg">
						<Loader2 className="w-8 h-8 text-green-500 animate-spin" />
						<h3 className={`text-lg font-medium mb-2 ${isDarkMode ? "text-white" : "text-gray-900"}`}>
							Loading Product Data
						</h3>
						<p className={`text-sm text-center max-w-md ${isDarkMode ? "text-gray-400" : "text-gray-600"}`}>
							Fetching product information and analyzing market data...
						</p>
					</div>
				)}

				{
					(!productData && !keepaLoading) && (
						<div className="mt-4 flex py-10 flex-col items-center justify-center border rounded-lg">
							<Search className={`w-12 h-12 mb-4 ${isDarkMode ? "text-gray-600" : "text-gray-400"}`} />
							<h3 className={`text-lg font-medium mb-2 ${isDarkMode ? "text-white" : "text-gray-900"}`}>
								Product Analysis
							</h3>
							<p className={`text-sm text-center max-w-md ${isDarkMode ? "text-gray-400" : "text-gray-600"}`}>
								Enter an Amazon ASIN above to analyze product profitability, market trends, and get AI-powered insights.
							</p>
							<div className="mt-6 flex items-center gap-2 text-sm text-gray-500">
								<AlertTriangle size={14} />
								<span>Example ASIN: B09SVGB8GG</span>
							</div>
						</div>
					)
				}
				{
					(productData && !keepaLoading) && (
						<div className="mt-4">
							<div className={`relative mt-8 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-100 border-gray-200'} border px-2 py-1 flex items-center justify-center`}>
								<span className={`text-sm font-extrabold tracking-wide ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
									Overview
								</span>
			
								<div className="absolute right-2 flex items-center gap-2">
									<Button
										className={`${isDarkMode ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'} p-0.5 rounded h-fit`}
										title="Refresh Analysis"
									>
										<RefreshCw size={14} className={isDarkMode ? 'text-gray-300' : 'text-gray-700'} />
									</Button>
								</div>
							</div>
							<Overview isDarkMode={isDarkMode} />
							<AccordionData isDarkMode={isDarkMode} activeAccordion={[]}/>
						</div>
					)
				}
			</div>




        
      </div>
    </div>
  );
};

export default CheckoraModal;

