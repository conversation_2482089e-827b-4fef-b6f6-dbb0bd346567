"use client";

import Link from "next/link";
import React from "react";
import { Button } from "../ui/button";
import { Download } from "lucide-react";

interface Step {
    number: number;
    title: string;
    description: string;
}

const steps: Step[] = [
    {
        number: 1,
        title: "Install Extension",
        description:
            "Add our Chrome extension in seconds with a one-click install.",
    },
    {
        number: 2,
        title: "Browse & Click",
        description:
            "Shop normally. When you find a product, click our extension icon to scan it.",
    },
    {
        number: 3,
        title: "Instant Insights",
        description:
            "Instantly see a deal's quality, profit potential, and any restrictions.",
    },
    {
        number: 4,
        title: "Deep AI Analysis",
        description:
            "Chat with our AI agent to get detailed reports and find similar deals.",
    },
    {
        number: 5,
        title: "Inspectora Agent",
        description:
            "Find another 100 similar winning items using this AEP agent.",
    },
];

export const DownloadExtension: React.FC = () => {
    return (
		<section
			className="py-24 bg-slate-800 relative overflow-hidden"
			id="how-it-works">
			<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent" />

			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="text-center mb-16">
					<h2 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
						Find Smarter Deals,{" "}
						<span className="text-green-500">Your Way!</span>
					</h2>
				</div>

				<div className="max-w-4xl mx-auto space-y-8">
					{steps.map((step, index) => (
						<div
							key={index}
							className="flex items-center gap-8 p-6 bg-gradient-to-br from-slate-700 to-slate-600 border border-slate-600 rounded-xl transition-all duration-300 hover:border-green-500 group">
							<div className="flex-shrink-0">
								<div className="w-12 h-12 bg-green-500 text-slate-50 rounded-full flex items-center justify-center text-xl font-bold group-hover:scale-110 transition-transform duration-300">
									{step.number}
								</div>
							</div>
							<div>
								<h3 className="text-xl font-semibold text-slate-50 mb-2">
									{step.title}
								</h3>
								<p className="text-slate-400">
									{step.description}
								</p>
							</div>
						</div>
					))}
				</div>

				{/* CTA Section */}
				<div className="text-center mt-16">
					<Link href="https://chromewebstore.google.com/detail/clickbuy-deals-amazon-fba/jgfbjiffihkclcbnpmhcejahbpggebno" target="_blank">
						<Button
							size="lg"
							className="text-lg px-12 py-6 mb-4 bg-green-500 text-slate-50 hover:bg-green-600 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30 transition-all duration-300">
							<Download className="h-4 w-4 mr-2" />
							Download Extension
						</Button>
					</Link>
				</div>
			</div>
		</section>
	);
};
