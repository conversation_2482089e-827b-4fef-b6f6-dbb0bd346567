import React, { useState, useMemo } from "react";
import { ExternalLink, Store, BarChart3 } from "lucide-react";
import { createColumnHelper } from "@tanstack/react-table";

import ChatInterface from "./ai-agent/chat-interface";
import { DataTable, ProductImage } from "./ai-agent/ai-overview-reusable";

interface ChatMessage {
	id: string;
	message: string;
	sentTime: string;
	sender: string;
	direction: "incoming" | "outgoing";
	type: "text" | "audio";
	audioUrl?: string;
	isRead?: boolean;
}

interface Seller {
	id: string;
	name: string;
	sellerUrl: string;
	storeUrl: string;
	image: string;
	price: number;
	rating?: number;
	fulfillmentType: "FBA" | "FBM";
	shipsFrom: string;
	inStock: boolean;
}

interface SelectedProduct {
	asin: string;
	title: string;
	image: string;
	buyBoxPrice: number;
	category: string;
	rating: number;
	reviews: number;
}

interface SourceOraOverviewProps {
	isDarkMode?: boolean;
	onNavigateToInspectOra?: () => void;
	selectedProductFromInspect?: SelectedProduct | null;
}

const SourceOraOverview: React.FC<SourceOraOverviewProps> = ({
	isDarkMode = true,
	selectedProductFromInspect,
}) => {
	// Use selected product from Inspect-Ora or default
	const [selectedProduct] = useState<SelectedProduct>(
		selectedProductFromInspect || {
			asin: "B08N5WRWNW",
			title: "Echo Dot (4th Gen) | Smart speaker with Alexa | Charcoal",
			image: "https://images.unsplash.com/photo-1543512214-318c7553f230?w=120&h=120&fit=crop&auto=format",
			buyBoxPrice: 49.99,
			category: "Electronics",
			rating: 4.7,
			reviews: 245680,
		}
	);

	// Sellers data for the selected product
	const [sellers] = useState<Seller[]>([
		{
			id: "1",
			name: "TechDeals Pro",
			sellerUrl: "https://amazon.com/sp?seller=ATVPDKIKX0DER",
			storeUrl: "https://techdeals.store",
			image: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=40&h=40&fit=crop&auto=format",
			price: 47.99,
			rating: 4.8,
			fulfillmentType: "FBA",
			shipsFrom: "US",
			inStock: true,
		},
		{
			id: "2",
			name: "ElectroShop",
			sellerUrl: "https://amazon.com/sp?seller=A2EUQ1WTGCTBG2",
			storeUrl: "https://electroshop.com",
			image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=40&h=40&fit=crop&auto=format",
			price: 48.5,
			rating: 4.6,
			fulfillmentType: "FBA",
			shipsFrom: "US",
			inStock: true,
		},
		{
			id: "3",
			name: "GadgetWorld",
			sellerUrl: "https://amazon.com/sp?seller=A1ZFFQZ3YFFQZ1",
			storeUrl: "https://gadgetworld.net",
			image: "https://images.unsplash.com/photo-1498049794561-7780e7231661?w=40&h=40&fit=crop&auto=format",
			price: 51.25,
			rating: 4.4,
			fulfillmentType: "FBM",
			shipsFrom: "CA",
			inStock: false,
		},
		{
			id: "4",
			name: "SmartHome Hub",
			sellerUrl: "https://amazon.com/sp?seller=A3SGHHQZFFQZ3S",
			storeUrl: "https://smarthomehub.com",
			image: "https://images.unsplash.com/photo-1593642532973-d31b6557fa68?w=40&h=40&fit=crop&auto=format",
			price: 46.75,
			rating: 4.9,
			fulfillmentType: "FBA",
			shipsFrom: "US",
			inStock: true,
		},
		{
			id: "5",
			name: "Digital Paradise",
			sellerUrl: "https://amazon.com/sp?seller=A4DFFQZ2YSSHQZ",
			storeUrl: "https://digitalparadise.store",
			image: "https://images.unsplash.com/photo-1593642532973-d31b6557fa68?w=40&h=40&fit=crop&auto=format",
			price: 52.99,
			rating: 4.3,
			fulfillmentType: "FBM",
			shipsFrom: "UK",
			inStock: true,
		},
	]);

	// Chat messages state
	const [messages, setMessages] = useState<ChatMessage[]>([
		{
			id: "1",
			message:
				"Welcome to Source-Ora AI! I can help you find the best suppliers and analyze seller opportunities for your products.",
			sentTime: "just now",
			sender: "AI Assistant",
			direction: "incoming",
			type: "text",
			isRead: true,
		},
		{
			id: "2",
			message: `I found ${
				sellers.length
			} sellers for your selected product. The best price is $${Math.min(
				...sellers.map((s) => s.price)
			).toFixed(2)} from ${
				sellers.find(
					(s) => s.price === Math.min(...sellers.map((s) => s.price))
				)?.name
			} with FBA fulfillment.`,
			sentTime: "1 minute ago",
			sender: "AI Assistant",
			direction: "incoming",
			type: "text",
			isRead: true,
		},
	]);

	const [isTyping, setIsTyping] = useState(false);

	// Table setup with TanStack Table
	const columnHelper = createColumnHelper<Seller>();

	const columns = useMemo(
		() => [
			columnHelper.accessor("name", {
				header: "Seller Name",
				size: 250,
				minSize: 250,
				cell: (info) => (
					<div className="flex items-center gap-2 min-w-[250px]">
						<ProductImage
							src={info.row.original.image}
							alt={info.getValue()}
							asin={info.row.original.id}
							size="sm"
						/>
						<div className="min-w-0">
							<div className="font-semibold text-sm text-foreground whitespace-nowrap">
								{info.getValue()}
							</div>
							<div className="text-xs flex items-center gap-2 text-muted-foreground whitespace-nowrap">
								<span className="flex items-center gap-1">
									⭐ {info.row.original.rating}
								</span>
								<span
									className={`px-1 py-0.5 rounded text-xs font-medium ${
										info.row.original.fulfillmentType ===
										"FBA"
											? "bg-brand-primary/10 text-brand-primary border border-brand-primary/20"
											: "bg-brand-secondary/10 text-brand-secondary border border-brand-secondary/20"
									}`}>
									{info.row.original.fulfillmentType}
								</span>
							</div>
						</div>
					</div>
				),
			}),
			columnHelper.accessor("sellerUrl", {
				header: "Seller URL",
				size: 120,
				minSize: 120,
				cell: (info) => (
					<div className="min-w-[120px]">
						<a
							href={info.getValue()}
							target="_blank"
							rel="noopener noreferrer"
							className="inline-flex items-center gap-1 text-brand-secondary hover:text-brand-primary text-xs whitespace-nowrap transition-colors">
							<ExternalLink size={12} />
							View Profile
						</a>
					</div>
				),
			}),
			columnHelper.accessor("storeUrl", {
				header: "Store URL",
				size: 120,
				minSize: 120,
				cell: (info) => (
					<div className="min-w-[120px]">
						<a
							href={info.getValue()}
							target="_blank"
							rel="noopener noreferrer"
							className="inline-flex items-center gap-1 text-brand-primary hover:text-brand-secondary text-xs whitespace-nowrap transition-colors">
							<Store size={12} />
							Visit Store
						</a>
					</div>
				),
			}),
			columnHelper.accessor("price", {
				header: "Price",
				size: 140,
				minSize: 140,
				cell: (info) => (
					<div className="text-right min-w-[140px]">
						<div
							className={`text-lg font-bold ${
								info.getValue() < selectedProduct.buyBoxPrice
									? "text-brand-primary"
									: "text-destructive"
							}`}>
							${info.getValue().toFixed(2)}
						</div>
						<div className="text-xs text-muted-foreground whitespace-nowrap">
							{info.row.original.shipsFrom} •{" "}
							{info.row.original.inStock
								? "In Stock"
								: "Out of Stock"}
						</div>
					</div>
				),
			}),
			columnHelper.display({
				id: "actions",
				header: "Actions",
				size: 100,
				minSize: 100,
				cell: (info) => (
					<div className="min-w-[100px]">
						<button
							onClick={() => {
								console.log(
									"Analyzing seller:",
									info.row.original.name
								);
							}}
							disabled={!info.row.original.inStock}
							className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-md transition-colors whitespace-nowrap ${
								info.row.original.inStock
									? "bg-brand-secondary/10 text-brand-secondary border border-brand-secondary/20 hover:bg-brand-secondary/20"
									: "bg-muted text-muted-foreground border border-border opacity-50 cursor-not-allowed"
							}`}
							title={
								info.row.original.inStock
									? "Analyze this seller"
									: "Out of stock"
							}>
							<BarChart3 size={12} />
							Analyze
						</button>
					</div>
				),
			}),
		],
		[columnHelper, selectedProduct.buyBoxPrice]
	);

	// Chat handlers
	const handleSendMessage = (message: string) => {
		const newMsg: ChatMessage = {
			id: Date.now().toString(),
			message,
			sentTime: "just now",
			sender: "User",
			direction: "outgoing",
			type: "text",
			isRead: false,
		};

		setMessages((prev) => [...prev, newMsg]);

		// Simulate AI response
		setIsTyping(true);
		setTimeout(() => {
			setIsTyping(false);
			const aiResponse: ChatMessage = {
				id: (Date.now() + 1).toString(),
				message:
					"Let me analyze the sourcing opportunities for that seller...",
				sentTime: "just now",
				sender: "AI Assistant",
				direction: "incoming",
				type: "text",
				isRead: true,
			};
			setMessages((prev) => [...prev, aiResponse]);
		}, 2000);
	};

	const handleAudioRecorded = (blob: Blob) => {
		console.log("Audio recorded:", blob);

		const audioMsg: ChatMessage = {
			id: Date.now().toString(),
			message: "🎵 Audio message sent",
			sentTime: "just now",
			sender: "User",
			direction: "outgoing",
			type: "audio",
			audioUrl: URL.createObjectURL(blob),
			isRead: false,
		};

		setMessages((prev) => [...prev, audioMsg]);

		// Simulate AI response to audio
		setIsTyping(true);
		setTimeout(() => {
			setIsTyping(false);
			const aiResponse: ChatMessage = {
				id: (Date.now() + 1).toString(),
				message:
					"I heard your audio message. Analyzing sourcing data...",
				sentTime: "just now",
				sender: "AI Assistant",
				direction: "incoming",
				type: "text",
				isRead: true,
			};
			setMessages((prev) => [...prev, aiResponse]);
		}, 3000);
	};

	return (
		<div className="flex flex-col h-full bg-background">
			{/* Compact Selected Product Card */}
			<div className="p-3 border border-border rounded-lg bg-card mb-4 mx-2">
				<h3 className="text-sm font-semibold mb-2 text-foreground">
					Selected Product
				</h3>
				<div className="flex items-center gap-3">
					<ProductImage
						src={selectedProduct.image}
						alt={selectedProduct.title}
						asin={selectedProduct.asin}
						size="sm"
					/>
					<div className="flex-1 min-w-0">
						<div className="grid grid-cols-2 lg:grid-cols-4 gap-2 text-xs">
							<div>
								<p className="text-muted-foreground">ASIN</p>
								<p className="font-mono font-medium text-foreground">
									{selectedProduct.asin}
								</p>
							</div>
							<div>
								<p className="text-muted-foreground">Buy Box</p>
								<p className="text-sm font-bold text-brand-primary">
									${selectedProduct.buyBoxPrice.toFixed(2)}
								</p>
							</div>
							<div>
								<p className="text-muted-foreground">Rating</p>
								<p className="text-foreground">
									⭐ {selectedProduct.rating} (
									{selectedProduct.reviews.toLocaleString()})
								</p>
							</div>
							<div>
								<p className="text-muted-foreground">
									Category
								</p>
								<p className="text-foreground">
									{selectedProduct.category}
								</p>
							</div>
						</div>
						<div className="mt-1">
							<p className="text-muted-foreground text-xs">
								Title
							</p>
							<p className="font-medium text-foreground text-xs truncate">
								{selectedProduct.title}
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* Main Content Area */}
			<div className="flex-1 flex flex-col lg:flex-row gap-3 px-2 pb-2 min-h-0">
				{/* Sellers Table - Left Side */}
				<div className="flex-1 lg:w-3/5 min-h-0">
					<div className="h-full border border-border rounded-lg bg-card overflow-hidden">
						<div className="p-3 border-b border-border bg-card">
							<h2 className="text-sm font-semibold text-foreground">
								Available Sellers ({sellers.length} found)
							</h2>
							<p className="text-xs text-muted-foreground mt-1">
								Compare prices and analyze opportunities
							</p>
						</div>
						<div className="flex-1 overflow-x-auto">
							<DataTable
								data={sellers}
								columns={columns}
								isDarkMode={isDarkMode}
								showHeader={false}
								className="h-full min-w-[730px]"
							/>
						</div>
					</div>
				</div>

				{/* Chat Interface - Right Side */}
				<div className="w-full lg:w-2/5 min-h-0 flex flex-col">
					<div className="flex-1 border border-border rounded-lg bg-card overflow-hidden flex flex-col">
						<div className="p-3 border-b border-border bg-card flex-shrink-0">
							<h2 className="text-sm font-semibold text-foreground">
								Source-Ora AI Assistant
							</h2>
							<p className="text-xs text-muted-foreground mt-1">
								Get sourcing insights and opportunities
							</p>
						</div>
						<div className="flex-1 min-h-0 overflow-hidden">
							<ChatInterface
								messages={messages}
								onSendMessage={handleSendMessage}
								onAudioRecorded={handleAudioRecorded}
								isTyping={isTyping}
								isDarkMode={isDarkMode}
								placeholder="Ask about sourcing opportunities..."
								aiName="Source-Ora AI"
								showAudioRecorder={true}
								height="h-full"
								className="border-0 rounded-none h-full"
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default SourceOraOverview;
