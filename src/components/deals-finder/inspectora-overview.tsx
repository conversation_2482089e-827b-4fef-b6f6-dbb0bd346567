import React, { useState, useMemo } from "react";
import { Eye, Zap } from "lucide-react";
import { createColumnHelper } from "@tanstack/react-table";

import ChatInterface from "./ai-agent/chat-interface";
import { DataTable, ProductImage } from "./ai-agent/ai-overview-reusable";

interface ChatMessage {
	id: string;
	message: string;
	sentTime: string;
	sender: string;
	direction: "incoming" | "outgoing";
	type: "text" | "audio";
	audioUrl?: string;
	isRead?: boolean;
}

interface Product {
	asin: string;
	title: string;
	image: string;
	buyBoxPrice: number;
	category: string;
	rating: number;
	reviews: number;
	bsr: number;
	status: "analyzing" | "completed" | "flagged" | "pending";
	lastUpdated: Date;
	profitMargin?: number;
	competitorCount?: number;
}

interface SelectedProduct {
	asin: string;
	title: string;
	image: string;
	buyBoxPrice: number;
	category: string;
	rating: number;
	reviews: number;
}

interface InspectOraOverviewProps {
	isDarkMode?: boolean;
	onNavigateToSourceOra?: (product: SelectedProduct) => void;
}

const InspectOraOverview: React.FC<InspectOraOverviewProps> = ({
	isDarkMode = true,
	onNavigateToSourceOra,
}) => {
	// Dummy product data
	const [products] = useState<Product[]>([
		{
			asin: "B08N5WRWNW",
			title: "Echo Dot (4th Gen) | Smart speaker with Alexa | Charcoal",
			image: "https://images.unsplash.com/photo-1543512214-318c7553f230?w=60&h=60&fit=crop&auto=format",
			buyBoxPrice: 49.99,
			category: "Electronics",
			rating: 4.7,
			reviews: 245680,
			bsr: 12,
			status: "completed",
			lastUpdated: new Date(Date.now() - 86400000),
			profitMargin: 25,
			competitorCount: 8,
		},
		{
			asin: "B093DKGSHW",
			title: "Apple AirPods Pro (2nd generation) with MagSafe Case",
			image: "https://images.unsplash.com/photo-1606220945770-b5b6c2c55bf1?w=60&h=60&fit=crop&auto=format",
			buyBoxPrice: 249.99,
			category: "Electronics",
			rating: 4.4,
			reviews: 89234,
			bsr: 5,
			status: "analyzing",
			lastUpdated: new Date(Date.now() - 3600000),
			profitMargin: 15,
			competitorCount: 12,
		},
		{
			asin: "B08C1W5N87",
			title: "Fire TV Stick 4K Max streaming device, Wi-Fi 6",
			image: "https://images.unsplash.com/photo-1593359677879-a4bb92f829d1?w=60&h=60&fit=crop&auto=format",
			buyBoxPrice: 54.99,
			category: "Electronics",
			rating: 4.5,
			reviews: 156789,
			bsr: 8,
			status: "flagged",
			lastUpdated: new Date(Date.now() - 7200000),
			profitMargin: 8,
			competitorCount: 15,
		},
		{
			asin: "B0B7BP6CJN",
			title: "Instant Vortex Plus 4QT Air Fryer, From the Makers of Instant Pot",
			image: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=60&h=60&fit=crop&auto=format",
			buyBoxPrice: 79.95,
			category: "Kitchen",
			rating: 4.3,
			reviews: 34567,
			bsr: 45,
			status: "completed",
			lastUpdated: new Date(Date.now() - 43200000),
			profitMargin: 22,
			competitorCount: 6,
		},
		{
			asin: "B09JQMJSXY",
			title: "COSORI Air Fryer Pro LE 5-Qt Airfryer, With 20PCS paper liners",
			image: "https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=60&h=60&fit=crop&auto=format",
			buyBoxPrice: 119.99,
			category: "Kitchen",
			rating: 4.6,
			reviews: 67890,
			bsr: 23,
			status: "pending",
			lastUpdated: new Date(Date.now() - 21600000),
			profitMargin: 18,
			competitorCount: 10,
		},
	]);

	// Chat messages state
	const [messages, setMessages] = useState<ChatMessage[]>([
		{
			id: "1",
			message:
				"Welcome to Inspect-Ora AI! I can help you analyze products and identify potential issues or opportunities.",
			sentTime: "just now",
			sender: "AI Assistant",
			direction: "incoming",
			type: "text",
			isRead: true,
		},
		{
			id: "2",
			message:
				"I see you have 5 products in your analysis queue. Would you like me to prioritize any specific ones?",
			sentTime: "1 minute ago",
			sender: "AI Assistant",
			direction: "incoming",
			type: "text",
			isRead: true,
		},
	]);

	const [isTyping, setIsTyping] = useState(false);

	// Helper function to convert Product to SelectedProduct
	const convertToSelectedProduct = (product: Product): SelectedProduct => ({
		asin: product.asin,
		title: product.title,
		image: product.image,
		buyBoxPrice: product.buyBoxPrice,
		category: product.category,
		rating: product.rating,
		reviews: product.reviews,
	});

	// Table setup with TanStack Table
	const columnHelper = createColumnHelper<Product>();

	const columns = useMemo(
		() => [
			columnHelper.accessor("title", {
				id: "product",
				header: "Product",
				size: 350,
				minSize: 350,
				cell: (info) => (
					<div className="flex items-center gap-3 min-w-[350px]">
						<ProductImage
							src={info.row.original.image}
							alt={info.row.original.title}
							asin={info.row.original.asin}
							size="sm"
						/>
						<div className="min-w-0 flex-1">
							<div className="font-semibold text-sm text-foreground whitespace-nowrap overflow-hidden text-ellipsis">
								{info.getValue()}
							</div>
							<div className="text-xs flex items-center gap-2 mt-1 text-muted-foreground whitespace-nowrap">
								<span className="flex items-center gap-1">
									⭐ {info.row.original.rating}
								</span>
								<span>•</span>
								<span>
									{info.row.original.reviews.toLocaleString()}{" "}
									reviews
								</span>
							</div>
						</div>
					</div>
				),
			}),
			columnHelper.accessor("asin", {
				header: "ASIN",
				size: 140,
				minSize: 140,
				cell: (info) => (
					<div className="text-xs min-w-[140px]">
						<div className="font-mono font-medium text-foreground whitespace-nowrap">
							{info.getValue()}
						</div>
						<div className="text-muted-foreground mt-1 whitespace-nowrap">
							BSR #{info.row.original.bsr.toLocaleString()}
						</div>
					</div>
				),
			}),
			columnHelper.accessor("buyBoxPrice", {
				header: "Price",
				size: 120,
				minSize: 120,
				cell: (info) => (
					<div className="text-right min-w-[120px]">
						<div className="text-lg font-bold text-brand-primary">
							${info.getValue().toFixed(2)}
						</div>
						<div className="text-xs text-muted-foreground whitespace-nowrap">
							{info.row.original.category}
						</div>
					</div>
				),
			}),
			columnHelper.accessor("status", {
				header: "Status",
				size: 120,
				minSize: 120,
				cell: (info) => {
					const status = info.getValue();
					const statusConfig = {
						analyzing: {
							label: "Analyzing",
							className:
								"bg-brand-secondary/10 text-brand-secondary border-brand-secondary/20",
						},
						completed: {
							label: "Completed",
							className:
								"bg-brand-primary/10 text-brand-primary border-brand-primary/20",
						},
						flagged: {
							label: "Flagged",
							className:
								"bg-destructive/10 text-destructive border-destructive/20",
						},
						pending: {
							label: "Pending",
							className:
								"bg-muted text-muted-foreground border-border",
						},
					};

					const config = statusConfig[status];

					return (
						<div className="min-w-[120px]">
							<span
								className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border whitespace-nowrap ${config.className}`}>
								{config.label}
							</span>
						</div>
					);
				},
			}),
			columnHelper.display({
				id: "actions",
				header: "Actions",
				size: 160,
				minSize: 160,
				cell: (info) => (
					<div className="flex items-center gap-1 min-w-[160px]">
						<button
							disabled
							className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-md bg-brand-secondary/10 text-brand-secondary border border-brand-secondary/20 opacity-50 cursor-not-allowed whitespace-nowrap"
							title="Coming soon">
							<Eye size={12} />
							Inspect
						</button>
						<button
							onClick={() => {
								const selectedProduct =
									convertToSelectedProduct(info.row.original);
								onNavigateToSourceOra?.(selectedProduct);
							}}
							className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-md bg-brand-primary/10 text-brand-primary border border-brand-primary/20 hover:bg-brand-primary/20 transition-colors whitespace-nowrap"
							title="Find sourcing opportunities">
							<Zap size={12} />
							Source
						</button>
					</div>
				),
			}),
		],
		[columnHelper, onNavigateToSourceOra]
	);

	// Chat handlers
	const handleSendMessage = (message: string) => {
		const newMsg: ChatMessage = {
			id: Date.now().toString(),
			message,
			sentTime: "just now",
			sender: "User",
			direction: "outgoing",
			type: "text",
			isRead: false,
		};

		setMessages((prev) => [...prev, newMsg]);

		// Simulate AI response
		setIsTyping(true);
		setTimeout(() => {
			setIsTyping(false);
			const aiResponse: ChatMessage = {
				id: (Date.now() + 1).toString(),
				message:
					"I understand your query. Let me analyze that for you...",
				sentTime: "just now",
				sender: "AI Assistant",
				direction: "incoming",
				type: "text",
				isRead: true,
			};
			setMessages((prev) => [...prev, aiResponse]);
		}, 2000);
	};

	const handleAudioRecorded = (blob: Blob) => {
		console.log("Audio recorded:", blob);

		const audioMsg: ChatMessage = {
			id: Date.now().toString(),
			message: "🎵 Audio message sent",
			sentTime: "just now",
			sender: "User",
			direction: "outgoing",
			type: "audio",
			audioUrl: URL.createObjectURL(blob),
			isRead: false,
		};

		setMessages((prev) => [...prev, audioMsg]);

		// Simulate AI response to audio
		setIsTyping(true);
		setTimeout(() => {
			setIsTyping(false);
			const aiResponse: ChatMessage = {
				id: (Date.now() + 1).toString(),
				message:
					"I heard your audio message. Processing your request...",
				sentTime: "just now",
				sender: "AI Assistant",
				direction: "incoming",
				type: "text",
				isRead: true,
			};
			setMessages((prev) => [...prev, aiResponse]);
		}, 3000);
	};

	return (
		<div className="flex min-h-screen flex-col h-full bg-background">
			{/* Main Content Area */}
			<div className="flex-1 flex flex-col lg:flex-row gap-3 px-2 pb-2 min-h-0">
				{/* Products Table - Left Side */}
				<div className="flex-1 lg:w-3/5 min-h-0">
					<div className="h-full border border-border rounded-lg bg-card overflow-hidden">
						<div className="p-3 border-b border-border bg-card">
							<h2 className="text-sm font-semibold text-foreground">
								Product Analysis Queue ({products.length}{" "}
								products)
							</h2>
							<p className="text-xs text-muted-foreground mt-1">
								Analyze and manage your product queue
							</p>
						</div>
						<div className="flex-1 overflow-x-auto">
							<DataTable
								data={products}
								columns={columns}
								isDarkMode={isDarkMode}
								showHeader={false}
								className="h-full min-w-[890px]"
							/>
						</div>
					</div>
				</div>

				{/* Chat Interface - Right Side */}
				<div className="w-full lg:w-2/5 min-h-0 flex flex-col">
					<div className="flex-1 border border-border rounded-lg bg-card overflow-hidden flex flex-col">
						<div className="p-3 border-b border-border bg-card flex-shrink-0">
							<h2 className="text-sm font-semibold text-foreground">
								Inspect-Ora AI Assistant
							</h2>
							<p className="text-xs text-muted-foreground mt-1">
								Get insights about your products
							</p>
						</div>
						<div className="flex-1 min-h-0 overflow-hidden">
							<ChatInterface
								messages={messages}
								onSendMessage={handleSendMessage}
								onAudioRecorded={handleAudioRecorded}
								isTyping={isTyping}
								isDarkMode={isDarkMode}
								placeholder="Ask about your products..."
								aiName="Inspect-Ora AI"
								showAudioRecorder={true}
								height="h-full"
								className="border-0 rounded-none h-full"
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default InspectOraOverview;
