import { useAuth } from "@/hooks/use-auth";
import { useProductContext } from "@/hooks/use-product-analysis";
import {
  formatToCurrency,
  getCurrencySymbol,
  getCurrentBuyBoxPrice,
  getCurrentFBAFees,
  getCurrentProfit,
  getCurrentReferralFee,
  getCurrentROI,
  getCurrentTotalFees,
} from "@/lib/index";
import { Box, Info, Lock } from "lucide-react";
import { useState } from "react";
import { Tooltip, TooltipContent, TooltipTrigger } from "../ui/tooltip";

const ProfitCalculator = ({ isDarkMode = true }) => {
  const { user } = useAuth();
  const {
    productData,
    isVatRegistered,
    setIsVatRegistered,
    selectedCountry,
    vat,
    setVat,
  } = useProductContext();

  const handleVatRegisteredChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    setIsVatRegistered(e.target.checked);
  };

  const handleVatPercentageChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    setVat(Number(e.target.value));
  };

  const formatCurrency = (value: number | undefined | null): string => {
    if (value === undefined || value === null)
      return `${getCurrencySymbol(selectedCountry)}0.00`;
    return `${getCurrencySymbol(selectedCountry)}${value.toFixed(2)}`;
  };

  return (
    <div className={` ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
      <div className="p-2">
        {/* Profit and ROI - separate box, extreme left, bigger text */}
        <div className={`bg-gray-100 border border-gray-400 rounded p-2 mb-2 ${isDarkMode ? 'bg-gray-900 border-gray-700' : 'bg-gray-100 border-gray-400'}`}>
          <div className="flex justify-between items-center mb-1">
        <span className={`text-xs font-medium ${isDarkMode ? 'text-white' : 'text-black'}`}>Profit:</span>
            <span
              className={`text-xs font-bold ${
                ((productData &&
                  getCurrentProfit({ isVatRegistered, productData })) ||
                  0) > 0
                  ? "text-green-600"
                  : "text-red-600"
              } ${isDarkMode ? 'text-white' : 'text-black'}`}
            >
              {!productData ? (
                <div className="inline-block h-3 w-3 animate-spin rounded-full border border-gray-500 border-t-transparent"></div>
              ) : (
                formatCurrency(
                  getCurrentProfit({ isVatRegistered, productData }) || 0,
                )
              )}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className={`text-xs font-medium ${isDarkMode ? 'text-white' : 'text-black'}`}>ROI:</span>
            <span
              className={`text-xs font-bold ${
                ((productData &&
                  getCurrentROI({ isVatRegistered, productData })) ||
                  0) > 0
                  ? "text-green-600"
                  : "text-red-600"
              } ${isDarkMode ? 'text-white' : 'text-black'}`}
            >
              {!productData ? (
                <div className="inline-block h-3 w-3 animate-spin rounded-full border border-gray-500 border-t-transparent"></div>
              ) : (
                `${getCurrentROI({ isVatRegistered, productData })?.toFixed(1) || "0.0"}%`
              )}
            </span>
          </div>
        </div>

        {/* All breakdown including GST/VAT and Total Fees - in one separate box */}
        <div className={`bg-gray-100 border border-gray-400 rounded p-2 ${isDarkMode ? 'bg-gray-900 border-gray-700' : 'bg-gray-100 border-gray-400'}`}>
          {/* GST/VAT section inside the box */}
          <div className="flex items-center gap-2 mb-2 pb-2 border-b border-gray-300">
            {!user ? (
              <button className={`flex items-center bg-white border px-1.5 py-0.5 rounded text-xs ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-black'}`}>
                <Lock size={10} className="mr-0.5" />
              </button>
            ) : (
              <input
                type="checkbox"
                checked={isVatRegistered || false}
                onChange={handleVatRegisteredChange}
              />
            )}
            <span className={`text-xs flex items-center gap-1 ${isDarkMode ? 'text-white' : 'text-black'}`}>
              <Tooltip>
                <TooltipTrigger>
                  <Info className="w-3 h-3 inline-block text-muted-foreground mb-[1px] cursor-pointer" />
                </TooltipTrigger>
                <TooltipContent className="bg-black animate-in fade-in-50 duration-200 slide-in-from-bottom-2">
                  <p className="text-muted-foreground text-white font-normal">
                    Ticking the 'VAT Registered' box tells the calculator <br />
                    your business is registered in that country. It will <br />
                    then provide a more accurate profit and ROI by adding <br />
                    VAT to the selling price and deducting the VAT you can{" "}
                    <br />
                    reclaim on your purchase and fees.
                  </p>
                </TooltipContent>
              </Tooltip>
              GST/VAT Registered
            </span>
            {!user ? (
              <button className={`flex items-center bg-white border px-1.5 py-0.5 rounded text-xs ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-black'}`}>
                <Lock size={10} className="mr-0.5" />
              </button>
            ) : (
              <input
                type="number"
                value={vat}
                onChange={handleVatPercentageChange}
                className="w-12 text-xs border rounded px-1"
              />
            )}
            <span className={`text-xs ${isDarkMode ? 'text-white' : 'text-black'}`}>GST/VAT %</span>
          </div>

          <div className="space-y-1 text-xs">
            <span className={`flex items-center gap-1 font-bold ${isDarkMode ? 'text-white' : 'text-black'}`}>
                Fees
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="w-3 h-3 inline-block text-muted-foreground mb-[1px] cursor-pointer" />
                  </TooltipTrigger>
                  <TooltipContent className="bg-black animate-in fade-in-50 duration-200 slide-in-from-bottom-2">
                    <p className="text-muted-foreground text-white font-normal">
                      VAT on FBA and Referral Fees is already included
                      <br />
                      in the totals shown for fees and profit.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </span>
            <div className="flex justify-between">
              <span className={` ${isDarkMode ? 'text-white' : 'text-black'}`}>Amazon Price:</span>
              <span className={` ${isDarkMode ? 'text-white' : 'text-black'}`}>
                {productData &&
                  formatCurrency(
                    getCurrentBuyBoxPrice({ isVatRegistered, productData }) ||
                      0,
                  )}
              </span>
            </div>
            <div className="flex justify-between">
              <span className={` ${isDarkMode ? 'text-white' : 'text-black'}`}>FBA Fee:</span>
              <span className={`text-red-600 ${isDarkMode ? 'text-white' : 'text-black'}`}>
                {productData &&
                  formatCurrency(
                    getCurrentFBAFees({ isVatRegistered, productData }) || 0,
                  )}
              </span>
            </div>
            <div className="flex justify-between">
              <span className={` ${isDarkMode ? 'text-white' : 'text-black'}`}>Referral Fee:</span>
              <span className={`text-red-600 ${isDarkMode ? 'text-white' : 'text-black'}`}>
                {productData &&
                  formatCurrency(
                    getCurrentReferralFee({ isVatRegistered, productData }) ||
                      0,
                  )}
              </span>
            </div>
            <div className="flex justify-between ">
              <span className={` ${isDarkMode ? 'text-white' : 'text-black'}`}>Total Fees:</span>
              <span className={`text-red-600 ${isDarkMode ? 'text-white' : 'text-black'}`}>
                {productData &&
                  formatCurrency(
                    getCurrentTotalFees({ isVatRegistered, productData }) || 0,
                  )}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfitCalculator;
