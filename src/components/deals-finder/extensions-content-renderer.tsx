/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useRef, useState } from "react";
import ProductAnalyzer from "@/components/deals-finder/product-analyzer";
import TokenUsage from "@/components/deals-finder/token-usage";
import ExtensionsAnalytics from "@/components/deals-finder/extensions-analytics";
import InspectOraAISettings from "@/components/deals-finder/ai-agent/inspectora-settings";
import SourceOraAISettings from "@/components/deals-finder/ai-agent/sourceora-settings";
import ComingSoonPlaceholder from "@/components/coming-soon";
import BillingPage from "@/components/billing/billing-page";
import InspectoraPage from "@/components/deals-finder/inspectora-page";
import ComingSoonOverlay from "../coming-soon-overlay";
import SolutionsHubPage from "@/app/aep-solutions/(components)/aep-page-contents";
import CreateAgentComponent from "../ai-digital-workforce/create-agent";
import AIAgents, { AIAgentsRef } from "../ai-digital-workforce/ai-agents";
import { DownloadExtension } from "./download-extension";
import AmazonVerificationModal from "./amz-verification-modal";
import { useAuth } from "@/hooks/use-auth";
import { AlertCircle } from "lucide-react";

interface ExtensionsContentRendererProps {
	activeTab: string;
	extensionsData: any;
	isDarkMode: boolean;
	user?: any;
	onAnalyzeProduct: (asin: string) => Promise<any>;
	onUpdateSettings: (settings: any) => Promise<void>;
	onUpdateWeights: (weights: any) => Promise<void>;
	onUpgrade: () => void;
	onNavigateToSourceOra?: () => void;
	section?: string | null;
	onTabChange: (tab: string) => void;
}

const ExtensionsContentRenderer: React.FC<ExtensionsContentRendererProps> = ({
	activeTab,
	extensionsData,
	isDarkMode,
	onAnalyzeProduct,
	onUpdateSettings,
	onUpdateWeights,
	onUpgrade,
	onNavigateToSourceOra,
	section,
	onTabChange,
}) => {
	const aiAgentsRef = useRef<AIAgentsRef>(null);
	const [showAmazonModal, setShowAmazonModal] = useState(false);
	const {user} = useAuth();

	const handleCreateAgent = () => {
		onTabChange("request-agent");
	};

	const handleAgentCreated = async () => {
		if (aiAgentsRef.current) {
			aiAgentsRef.current.refresh();
		}
		onTabChange("ai-agent");
	};

	const safeUser =
		user &&
		typeof user === "object" &&
		!Array.isArray(user) &&
		!("type" in user)
			? user
			: null;
	const isAmazonConnected = safeUser?.amz_verify === true || false;

	const renderAmazonRequiredOverlay = (content: React.ReactNode) => {
		return (
			<div className="relative">
				{content}
				<div className="absolute inset-0 z-10 flex justify-center pt-20 bg-black/70">
					<div className="max-w-md w-full mx-4 p-8 rounded-2xl border bg-gray-800 border-gray-700 shadow-2xl text-center h-fit">
						<div className="flex justify-center mb-6">
							<div className="p-4 rounded-full bg-red-500/20 text-red-400">
								<AlertCircle size={24} />
							</div>
						</div>
						<h3 className="text-md font-bold text-white mb-3">
							Amazon Account Required
						</h3>
						<p className="text-gray-300 mb-6">
							To access AI Deals Sourcing features, you need to
							connect your Amazon Seller Central account first.
						</p>
						<button
							onClick={() => setShowAmazonModal(true)}
							className="w-full px-6 py-3 rounded-lg font-medium transition-all duration-200 bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] hover:from-[#12C2E9] hover:to-[#19D86C] hover:scale-105 active:scale-95">
							Connect Amazon Account
						</button>
					</div>
				</div>
			</div>
		);
	};

	const renderContent = () => {
		switch (activeTab) {
			// General
			case "billing":
				return (
					<BillingPage
						isDarkMode={isDarkMode}
						section={section || undefined}
					/>
				);
			case "tokens":
				return (
					<TokenUsage isDarkMode={isDarkMode} onUpgrade={onUpgrade} />
				);
			case "home":
				return <SolutionsHubPage isDarkMode={isDarkMode} />;
			case "personal-info":
				return (
					<ComingSoonOverlay
						title="Personal Data Hub"
						description="Control and transparency over your personal information — launching soon"
						estimatedDate="Q3 2025"
						features={[
							"Access and manage your data",
							"Download personal information",
							"Privacy settings control",
							"Consent history and logs",
						]}
						onNotifyClick={() =>
							console.log("Notify me about Personal Data Hub!")
						}
					/>
				);

			// AI Deals Sourcing
			case "analyzer":
				// BRUTE FORCE: Explicit true check - only show overlay if Amazon is definitively NOT connected
				if (isAmazonConnected === true) {
					// Amazon IS connected - show normal component
					return (
						<ProductAnalyzer
							onAnalyze={onAnalyzeProduct}
							recentAnalyses={extensionsData.recentAnalyses}
							isDarkMode={isDarkMode}
						/>
					);
				} else {
					// Amazon is NOT connected - show overlay
					return renderAmazonRequiredOverlay(
						<ProductAnalyzer
							onAnalyze={onAnalyzeProduct}
							recentAnalyses={extensionsData.recentAnalyses}
							isDarkMode={isDarkMode}
						/>
					);
				}

			case "analytics":
				return (
					<div className="relative">
						<ExtensionsAnalytics
							analytics={extensionsData.analytics}
							isDarkMode={isDarkMode}
						/>
						<ComingSoonOverlay
							title="Analytics Dashboard"
							description="Advanced analytics and performance insights are currently under development"
							estimatedDate="Q3 2025"
							features={[
								"Performance metrics tracking",
								"Category breakdown analysis",
								"AI recommendation insights",
								"Scanning performance stats",
							]}
							onNotifyClick={() =>
								console.log("Analytics coming soon!")
							}
						/>
					</div>
				);

			case "inspect-ora-ai":
				return (
					<div className="relative">
						<InspectOraAISettings
							settings={extensionsData.settings}
							onUpdateSettings={onUpdateSettings}
							onUpdateWeights={onUpdateWeights}
							isDarkMode={isDarkMode}
							onNavigateToSourceOra={onNavigateToSourceOra}
						/>
						<ComingSoonOverlay
							title="Inspect-Ora AI Agent"
							description="Advanced AI-powered Amazon product analysis and automation coming soon"
							estimatedDate="Q3 2025"
							features={[
								"AI-powered product scanning",
								"Automated profitability analysis",
								"Smart competitor monitoring",
								"Real-time price alerts",
								"Custom AI weight configuration",
							]}
							onNotifyClick={() =>
								console.log("Inspect-Ora AI Agent coming soon!")
							}
						/>
					</div>
				);

			case "source-ora":
				return (
					<div className="relative">
						<SourceOraAISettings
							settings={extensionsData.settings}
							onUpdateSettings={onUpdateSettings}
							onUpdateWeights={onUpdateWeights}
							isDarkMode={isDarkMode}
						/>
						<ComingSoonOverlay
							title="Source-Ora AI Agent"
							description="Intelligent product sourcing and supplier discovery powered by AI"
							estimatedDate="Q3 2025"
							features={[
								"AI-driven supplier discovery",
								"Automated sourcing recommendations",
								"Supply chain optimization",
								"Wholesale opportunity detection",
								"Smart inventory forecasting",
							]}
							onNotifyClick={() =>
								console.log("Source-Ora AI Agent coming soon!")
							}
						/>
					</div>
				);

			case "inspect-ora":
				// BRUTE FORCE: Explicit true check - only show overlay if Amazon is definitively NOT connected
				if (isAmazonConnected === true) {
					// Amazon IS connected - show normal component
					return <InspectoraPage isDarkMode={isDarkMode} />;
				} else {
					// Amazon is NOT connected - show overlay
					return renderAmazonRequiredOverlay(
						<InspectoraPage isDarkMode={isDarkMode} />
					);
				}

			case "extension":
				return <DownloadExtension />;

			// Digital Workforce
			case "ai-agent":
				return (
					<AIAgents
						isDarkMode={isDarkMode}
						onCreateAgent={handleCreateAgent}
						ref={aiAgentsRef}
					/>
				);

			case "request-agent":
				return (
					<CreateAgentComponent
						isDarkMode={isDarkMode}
						onSubmit={handleAgentCreated}
						onCancel={() => {
							onTabChange("ai-agent");
						}}
					/>
				);

			// Cross Selling
			case "clickbuy-store":
				return (
					<ComingSoonOverlay
						title="ClickBuy Cross-Selling Platform"
						description="Seamlessly list and manage products across Amazon, eBay, and other marketplaces – all in one place"
						estimatedDate="Q3 2025"
						features={[
							"Multi-channel product listing",
							"Unified inventory management",
							"Cross-platform order syncing",
							"Real-time stock and price updates",
							"Insights to optimise product reach",
						]}
						onNotifyClick={() =>
							console.log(
								"ClickBuy Cross-Selling Platform coming soon!"
							)
						}
					/>
				);

			// Systemise Fulfilment
			case "systemise-fulfilment":
				return (
					<ComingSoonOverlay
						title="Systemise Fulfilment Integration"
						description="Your trusted eCommerce fulfilment & logistics partner. Managing your warehouse order fulfilment effortlessly and preparation efficiently."
						estimatedDate="Q2 2025"
						features={[
							"Amazon, Shopify & TikTok Shop integration",
							"Real-time inventory tracking across UK, EU & USA",
							"7-day shipping guarantee",
							"Multi-platform order management",
							"Seamless warehouse preparation",
						]}
						onNotifyClick={() =>
							console.log(
								"Systemise Fulfilment Integration coming soon!"
							)
						}
					/>
				);

			default:
				return (
					<ComingSoonPlaceholder
						icon="🔧"
						title="Feature Not Found"
						description="The requested feature is not available."
						isDarkMode={isDarkMode}
					/>
				);
		}
	};

	return (
		<>
			<div className="flex-1 overflow-auto bg-background transition-colors duration-300">
				{renderContent()}
			</div>

			{/* Amazon Verification Modal */}
			<AmazonVerificationModal
				isOpen={showAmazonModal}
				onClose={() => setShowAmazonModal(false)}
				isDarkMode={isDarkMode}
			/>
		</>
	);
};

export default ExtensionsContentRenderer;
