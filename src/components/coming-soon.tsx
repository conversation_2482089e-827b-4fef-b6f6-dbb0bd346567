import React from 'react';

interface ComingSoonPlaceholderProps {
  icon: string;
  title: string;
  description: string;
  isDarkMode?: boolean;
  className?: string;
}

const ComingSoonPlaceholder: React.FC<ComingSoonPlaceholderProps> = ({
  icon,
  title,
  description,
  isDarkMode = true,
  className = '',
}) => {
  return (
    <div className={`text-center py-12 ${className}`}>
      <div className="text-6xl mb-4">{icon}</div>
      <h3 className={`text-xl font-semibold mb-2 ${
        isDarkMode ? 'text-white' : 'text-gray-900'
      }`}>
        {title}
      </h3>
      <p className={isDarkMode ? 'text-gray-400' : 'text-gray-600'}>
        {description}
      </p>
    </div>
  );
};

export default ComingSoonPlaceholder;