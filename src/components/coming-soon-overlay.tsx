// src/components/ui/ComingSoonOverlay.tsx
import React from "react";
import { <PERSON>, Zap, Bell } from "lucide-react";

interface ComingSoonOverlayProps {
	title?: string;
	description?: string;
	estimatedDate?: string;
	features?: string[];
	isDarkMode?: boolean;
	showNotifyButton?: boolean;
	onNotifyClick?: () => void;
	className?: string;
	isMobileMenuOpen?: boolean;
}

const ComingSoonOverlay: React.FC<ComingSoonOverlayProps> = ({
	title = "Coming Soon",
	description = "This feature is currently under development and will be available soon.",
	estimatedDate,
	features = [],
	isDarkMode = true,
	showNotifyButton = true,
	onNotifyClick,
	className = "",
	isMobileMenuOpen = false,
}) => {
	// Hide overlay when mobile menu is open
	if (isMobileMenuOpen) {
		return null;
	}
	return (
		<div
			className={`absolute inset-0 z-10 flex items-center justify-center ${
				isDarkMode ? "bg-black/70" : "bg-white/70"
			} ${className}`}>
			<div
				className={`max-w-md w-full mx-4 p-8 rounded-2xl border ${
					isDarkMode
						? "bg-gray-800 border-gray-700 shadow-2xl"
						: "bg-white border-gray-200 shadow-2xl"
				}`}>
				{/* Icon */}
				<div className="flex justify-center mb-6">
					<div
						className={`p-4 rounded-full ${
							isDarkMode
								? "bg-[#19D86C]/20 text-[#19D86C]"
								: "bg-[#19D86C]/20 text-[#19D86C]"
						}`}>
						<Clock size={32} />
					</div>
				</div>

				{/* Title */}
				<h3
					className={`text-2xl font-bold text-center mb-3 ${
						isDarkMode ? "text-white" : "text-gray-900"
					}`}>
					{title}
				</h3>

				{/* Description */}
				<p
					className={`text-center mb-6 ${
						isDarkMode ? "text-gray-300" : "text-gray-600"
					}`}>
					{description}
				</p>

				{/* Estimated Date */}
				{estimatedDate && (
					<div
						className={`flex items-center justify-center gap-2 mb-6 p-3 rounded-lg border ${
							isDarkMode
								? "bg-gray-700 border-gray-600 text-gray-300"
								: "bg-gray-50 border-gray-200 text-gray-700"
						}`}>
						<Zap size={16} className="text-[#19D86C]" />
						<span className="text-sm font-medium">
							Expected: {estimatedDate}
						</span>
					</div>
				)}

				{/* Features List */}
				{features.length > 0 && (
					<div className="mb-6">
						<h4
							className={`text-sm font-semibold mb-3 ${
								isDarkMode ? "text-gray-200" : "text-gray-800"
							}`}>
							What&apos;s Coming:
						</h4>
						<ul className="space-y-2">
							{features.map((feature, index) => (
								<li
									key={index}
									className={`flex items-center gap-2 text-sm ${
										isDarkMode
											? "text-gray-300"
											: "text-gray-600"
									}`}>
									<div className="w-1.5 h-1.5 bg-[#19D86C] rounded-full flex-shrink-0" />
									{feature}
								</li>
							))}
						</ul>
					</div>
				)}

				{/* Notify Button */}
				{showNotifyButton && (
					<button
						onClick={onNotifyClick}
						className="w-full flex items-center justify-center gap-2 px-4 py-3 rounded-lg font-medium transition-all duration-200 bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] hover:from-[#12C2E9] hover:to-[#19D86C] hover:scale-105 active:scale-95">
						<Bell size={16} />
						Notify Me When Ready
					</button>
				)}
			</div>
		</div>
	);
};

export default ComingSoonOverlay;
