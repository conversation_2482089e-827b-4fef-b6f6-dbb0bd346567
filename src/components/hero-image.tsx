"use client";

import Image from "next/image";
import { Card } from "@/components/ui/card";

export default function HeroImageSection() {
	return (
		<div className="relative w-full flex items-center justify-center">
			{/* Device Mockups Container */}
			<div className="relative flex items-center justify-center w-fit">
				{/* Desktop Device */}
				<Card className="relative z-0 w-80 -mr-12 -mt-16 md:w-[28rem] lg:w-[32rem] lg:-mr-42 overflow-hidden bg-gradient-to-br from-slate-800 to-slate-700 border border-slate-700 rounded-2xl shadow-2xl">
					<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent" />
					<Image
						src="/image1.png"
						alt="Desktop marketplace view"
						width={800}
						height={600}
						className="object-cover w-full h-auto"
					/>
				</Card>

				{/* Mobile Device Positioned Over Desktop */}
				<Card className="absolute z-10 w-32 md:w-48 lg:w-56 overflow-hidden bg-gradient-to-br from-slate-800 to-slate-700 border border-slate-700 rounded-2xl shadow-2xl top-10 left-[-3rem] sm:left-[-4rem] md:left-[-5rem] lg:left-[-8rem] lg:mt-5">
					<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent" />
					<Image
						src="/mobile1.png"
						alt="Mobile marketplace view"
						width={300}
						height={600}
						className="object-cover w-full h-auto"
					/>
				</Card>
			</div>
		</div>
	);
}
