// src/components/ui/ComingSoonVariants.tsx
import React from "react";
import {
	Clock,
	Lock,
	Bell,
	Settings,
	Shield,
	Sparkles,
} from "lucide-react";

interface BaseOverlayProps {
	isDarkMode?: boolean;
	onNotifyClick?: () => void;
	className?: string;
}

// Simple Coming Soon
export const SimpleComingSoon: React.FC<BaseOverlayProps> = ({
	isDarkMode = true,
	className = "",
}) => (
	<div
		className={`absolute inset-0 z-50 flex items-center justify-center ${
			isDarkMode
				? "bg-gray-900/70 backdrop-blur-sm"
				: "bg-white/70 backdrop-blur-sm"
		} ${className}`}>
		<div
			className={`px-8 py-6 rounded-xl border ${
				isDarkMode
					? "bg-gray-800/90 border-gray-700"
					: "bg-white/90 border-gray-200"
			} backdrop-blur-md shadow-xl`}>
			<div className="flex items-center gap-3">
				<Clock size={24} className="text-blue-500" />
				<div>
					<h3
						className={`font-bold ${
							isDarkMode ? "text-white" : "text-gray-900"
						}`}>
						Coming Soon
					</h3>
					<p
						className={`text-sm ${
							isDarkMode ? "text-gray-400" : "text-gray-600"
						}`}>
						This feature is under development
					</p>
				</div>
			</div>
		</div>
	</div>
);

// Premium Feature Lock
export const PremiumFeatureLock: React.FC<
	BaseOverlayProps & {
		featureName?: string;
		planRequired?: string;
	}
> = ({
	isDarkMode = true,
	onNotifyClick,
	className = "",
	featureName = "Advanced Analytics",
	planRequired = "Pro Plan",
}) => (
	<div
		className={`absolute inset-0 z-50 flex items-center justify-center ${
			isDarkMode
				? "bg-gray-900/75 backdrop-blur-sm"
				: "bg-white/75 backdrop-blur-sm"
		} ${className}`}>
		<div
			className={`max-w-sm w-full mx-4 p-6 rounded-xl border ${
				isDarkMode
					? "bg-gray-800/90 border-gray-700"
					: "bg-white/90 border-gray-200"
			} backdrop-blur-md shadow-xl text-center`}>
			<div className="flex justify-center mb-4">
				<div className="p-3 rounded-full bg-gradient-to-r from-purple-500 to-blue-500">
					<Lock size={24} className="text-white" />
				</div>
			</div>

			<h3
				className={`text-lg font-bold mb-2 ${
					isDarkMode ? "text-white" : "text-gray-900"
				}`}>
				{featureName}
			</h3>

			<p
				className={`text-sm mb-4 ${
					isDarkMode ? "text-gray-400" : "text-gray-600"
				}`}>
				Unlock this feature with {planRequired}
			</p>

			<button
				onClick={onNotifyClick}
				className="w-full px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg font-medium hover:from-purple-700 hover:to-blue-700 transition-all">
				Upgrade Now
			</button>
		</div>
	</div>
);

// Under Construction
export const UnderConstruction: React.FC<
	BaseOverlayProps & {
		progress?: number;
		completionDate?: string;
	}
> = ({
	isDarkMode = true,
	onNotifyClick,
	className = "",
	progress = 65,
	completionDate = "Q2 2025",
}) => (
	<div
		className={`absolute inset-0 z-50 flex items-center justify-center ${
			isDarkMode
				? "bg-gray-900/80 backdrop-blur-sm"
				: "bg-white/80 backdrop-blur-sm"
		} ${className}`}>
		<div
			className={`max-w-md w-full mx-4 p-6 rounded-xl border ${
				isDarkMode
					? "bg-gray-800/90 border-gray-700"
					: "bg-white/90 border-gray-200"
			} backdrop-blur-md shadow-xl`}>
			<div className="flex justify-center mb-4">
				<div className="p-3 rounded-full bg-orange-500/20">
					<Settings
						size={24}
						className="text-orange-500 animate-spin"
					/>
				</div>
			</div>

			<h3
				className={`text-xl font-bold text-center mb-2 ${
					isDarkMode ? "text-white" : "text-gray-900"
				}`}>
				Under Construction
			</h3>

			<p
				className={`text-center mb-4 ${
					isDarkMode ? "text-gray-400" : "text-gray-600"
				}`}>
				We&apos;re building something amazing for you
			</p>

			{/* Progress Bar */}
			<div className="mb-4">
				<div className="flex justify-between items-center mb-2">
					<span
						className={`text-sm font-medium ${
							isDarkMode ? "text-gray-300" : "text-gray-700"
						}`}>
						Progress
					</span>
					<span
						className={`text-sm font-medium ${
							isDarkMode ? "text-gray-300" : "text-gray-700"
						}`}>
						{progress}%
					</span>
				</div>
				<div
					className={`w-full h-2 rounded-full ${
						isDarkMode ? "bg-gray-700" : "bg-gray-200"
					}`}>
					<div
						className="h-2 bg-gradient-to-r from-green-500 to-blue-500 rounded-full transition-all duration-300"
						style={{ width: `${progress}%` }}
					/>
				</div>
			</div>

			<div
				className={`text-center p-3 rounded-lg ${
					isDarkMode ? "bg-gray-700/50" : "bg-gray-100"
				}`}>
				<span
					className={`text-sm ${
						isDarkMode ? "text-gray-300" : "text-gray-600"
					}`}>
					Expected completion: {completionDate}
				</span>
			</div>

			{onNotifyClick && (
				<button
					onClick={onNotifyClick}
					className={`w-full mt-4 px-4 py-2 rounded-lg font-medium transition-all ${
						isDarkMode
							? "bg-blue-600 hover:bg-blue-700 text-white"
							: "bg-blue-600 hover:bg-blue-700 text-white"
					}`}>
					<Bell size={16} className="inline mr-2" />
					Notify When Ready
				</button>
			)}
		</div>
	</div>
);

// Beta Feature
export const BetaFeature: React.FC<
	BaseOverlayProps & {
		featureName?: string;
		betaNote?: string;
	}
> = ({
	isDarkMode = true,
	onNotifyClick,
	className = "",
	featureName = "AI Analysis",
	betaNote = "This feature is in beta testing",
}) => (
	<div
		className={`absolute inset-0 z-50 flex items-center justify-center ${
			isDarkMode
				? "bg-gray-900/70 backdrop-blur-sm"
				: "bg-white/70 backdrop-blur-sm"
		} ${className}`}>
		<div
			className={`max-w-sm w-full mx-4 p-6 rounded-xl border ${
				isDarkMode
					? "bg-gray-800/90 border-gray-700"
					: "bg-white/90 border-gray-200"
			} backdrop-blur-md shadow-xl`}>
			<div className="flex justify-center mb-4">
				<div className="relative">
					<div className="p-3 rounded-full bg-yellow-500/20">
						<Sparkles size={24} className="text-yellow-500" />
					</div>
					<span className="absolute -top-1 -right-1 px-2 py-0.5 text-xs font-bold bg-yellow-500 text-black rounded-full">
						BETA
					</span>
				</div>
			</div>

			<h3
				className={`text-lg font-bold text-center mb-2 ${
					isDarkMode ? "text-white" : "text-gray-900"
				}`}>
				{featureName}
			</h3>

			<p
				className={`text-center mb-4 ${
					isDarkMode ? "text-gray-400" : "text-gray-600"
				}`}>
				{betaNote}
			</p>

			<div className="flex gap-2">
				<button
					onClick={onNotifyClick}
					className="flex-1 px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg font-medium transition-all">
					Join Beta
				</button>
				<button
					className={`px-4 py-2 rounded-lg font-medium transition-all ${
						isDarkMode
							? "bg-gray-700 hover:bg-gray-600 text-gray-300"
							: "bg-gray-200 hover:bg-gray-300 text-gray-700"
					}`}>
					Learn More
				</button>
			</div>
		</div>
	</div>
);

// Maintenance Mode
export const MaintenanceMode: React.FC<
	BaseOverlayProps & {
		estimatedDuration?: string;
		reason?: string;
	}
> = ({
	isDarkMode = true,
	className = "",
	estimatedDuration = "2-3 hours",
	reason = "Scheduled maintenance for performance improvements",
}) => (
	<div
		className={`absolute inset-0 z-50 flex items-center justify-center ${
			isDarkMode
				? "bg-gray-900/85 backdrop-blur-sm"
				: "bg-white/85 backdrop-blur-sm"
		} ${className}`}>
		<div
			className={`max-w-md w-full mx-4 p-6 rounded-xl border ${
				isDarkMode
					? "bg-gray-800/90 border-gray-700"
					: "bg-white/90 border-gray-200"
			} backdrop-blur-md shadow-xl text-center`}>
			<div className="flex justify-center mb-4">
				<div className="p-3 rounded-full bg-red-500/20">
					<Shield size={24} className="text-red-500" />
				</div>
			</div>

			<h3
				className={`text-xl font-bold mb-2 ${
					isDarkMode ? "text-white" : "text-gray-900"
				}`}>
				Under Maintenance
			</h3>

			<p
				className={`mb-4 ${
					isDarkMode ? "text-gray-400" : "text-gray-600"
				}`}>
				{reason}
			</p>

			<div
				className={`p-3 rounded-lg border mb-4 ${
					isDarkMode
						? "bg-gray-700/50 border-gray-600"
						: "bg-gray-100 border-gray-200"
				}`}>
				<p
					className={`text-sm ${
						isDarkMode ? "text-gray-300" : "text-gray-700"
					}`}>
					Estimated duration:{" "}
					<span className="font-medium">{estimatedDuration}</span>
				</p>
			</div>

			<p
				className={`text-sm ${
					isDarkMode ? "text-gray-500" : "text-gray-500"
				}`}>
				Thank you for your patience
			</p>
		</div>
	</div>
);

// Usage Examples Component
export const ComingSoonExamples: React.FC<{ isDarkMode?: boolean }> = ({
	isDarkMode = true,
}) => {
	const handleNotify = () => {
		console.log("Notify me clicked!");
	};

	return (
		<div className="space-y-8 p-6">
			<h2
				className={`text-2xl font-bold ${
					isDarkMode ? "text-white" : "text-gray-900"
				}`}>
				Coming Soon Overlay Examples
			</h2>

			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				{/* Simple Example */}
				<div className="relative h-64 bg-gray-100 rounded-lg overflow-hidden">
					<div className="p-4 bg-blue-500 text-white">
						Sample Content
					</div>
					<SimpleComingSoon
						isDarkMode={isDarkMode}
						onNotifyClick={handleNotify}
					/>
				</div>

				{/* Premium Lock Example */}
				<div className="relative h-64 bg-gray-100 rounded-lg overflow-hidden">
					<div className="p-4 bg-purple-500 text-white">
						Premium Feature
					</div>
					<PremiumFeatureLock
						isDarkMode={isDarkMode}
						onNotifyClick={handleNotify}
						featureName="Advanced Reports"
						planRequired="Pro Plan"
					/>
				</div>

				{/* Under Construction Example */}
				<div className="relative h-64 bg-gray-100 rounded-lg overflow-hidden">
					<div className="p-4 bg-orange-500 text-white">
						New Feature
					</div>
					<UnderConstruction
						isDarkMode={isDarkMode}
						onNotifyClick={handleNotify}
						progress={75}
						completionDate="March 2025"
					/>
				</div>

				{/* Beta Feature Example */}
				<div className="relative h-64 bg-gray-100 rounded-lg overflow-hidden">
					<div className="p-4 bg-yellow-500 text-white">
						Beta Feature
					</div>
					<BetaFeature
						isDarkMode={isDarkMode}
						onNotifyClick={handleNotify}
						featureName="AI Insights"
						betaNote="Early access feature in testing"
					/>
				</div>

				{/* Maintenance Example */}
				<div className="relative h-64 bg-gray-100 rounded-lg overflow-hidden">
					<div className="p-4 bg-red-500 text-white">Service</div>
					<MaintenanceMode
						isDarkMode={isDarkMode}
						estimatedDuration="1-2 hours"
						reason="Database optimization in progress"
					/>
				</div>
			</div>
		</div>
	);
};
