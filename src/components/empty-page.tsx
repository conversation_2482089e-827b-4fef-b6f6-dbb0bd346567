"use client";

import React from "react";
import {
	FileX,
	Plus,
	Search,
	RefreshCw,
	Filter,
	Download,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface EmptyPageProps {
	isDarkMode?: boolean;
	title: string;
	description: string;
	icon?: React.ReactNode;
	primaryAction?: {
		label: string;
		onClick: () => void;
		icon?: React.ReactNode;
		variant?: "primary" | "secondary";
	};
	secondaryAction?: {
		label: string;
		onClick: () => void;
		icon?: React.ReactNode;
	};
	showSearch?: boolean;
	showFilters?: boolean;
	showExport?: boolean;
	searchPlaceholder?: string;
	onSearch?: (value: string) => void;
	onRefresh?: () => void;
	customEmptyIcon?: React.ReactNode;
	emptyTitle?: string;
	emptyDescription?: string;
	emptyActionLabel?: string;
	onEmptyAction?: () => void;
	children?: React.ReactNode;
}

const EmptyPageComponent: React.FC<EmptyPageProps> = ({
	isDarkMode = true,
	title,
	description,
	icon,
	primaryAction,
	secondaryAction,
	showSearch = false,
	showFilters = false,
	showExport = false,
	searchPlaceholder = "Search...",
	onSearch,
	onRefresh,
	customEmptyIcon,
	emptyTitle,
	emptyDescription,
	emptyActionLabel,
	onEmptyAction,
	children,
}) => {
	const [searchValue, setSearchValue] = React.useState("");

	const handleSearchChange = (value: string) => {
		setSearchValue(value);
		if (onSearch) {
			onSearch(value);
		}
	};

	return (
		<div
			className={`${isDarkMode ? "bg-gray-900" : "bg-white"} border min-h-screen ${
				isDarkMode ? "border-gray-700" : "border-gray-200"
			} rounded-lg overflow-hidden`}>
			{/* Header */}
			<div className="p-6 border-b border-gray-700">
				<div className="flex items-center justify-between mb-4">
					<div className="flex items-center gap-3">
						{icon && (
							<div
								className={`w-8 h-8 rounded-full flex items-center justify-center ${
									isDarkMode ? "bg-[#10A37F]" : "bg-[#10A37F]"
								}`}>
								{icon}
							</div>
						)}
						<div>
							<h3
								className={`text-xl font-bold ${
									isDarkMode ? "text-white" : "text-gray-900"
								}`}>
								{title}
							</h3>
							<p
								className={`text-sm mt-1 ${
									isDarkMode
										? "text-gray-400"
										: "text-gray-600"
								}`}>
								{description}
							</p>
						</div>
					</div>

					<div className="flex items-center gap-2">
						{onRefresh && (
							<Button
								variant="outline"
								size="sm"
								onClick={onRefresh}
								className={`${
									isDarkMode
										? "border-gray-600 text-gray-300 hover:bg-gray-800"
										: "border-gray-300 text-gray-700 hover:bg-gray-50"
								}`}>
								<RefreshCw className="w-4 h-4" />
							</Button>
						)}

						{showExport && (
							<Button
								variant="outline"
								size="sm"
								className={`${
									isDarkMode
										? "border-gray-600 text-gray-300 hover:bg-gray-800"
										: "border-gray-300 text-gray-700 hover:bg-gray-50"
								}`}>
								<Download className="w-4 h-4 mr-2" />
								Export
							</Button>
						)}

						{secondaryAction && (
							<Button
								variant="outline"
								size="sm"
								onClick={secondaryAction.onClick}
								className={`${
									isDarkMode
										? "border-gray-600 text-gray-300 hover:bg-gray-800"
										: "border-gray-300 text-gray-700 hover:bg-gray-50"
								}`}>
								{secondaryAction.icon && (
									<span className="mr-2">
										{secondaryAction.icon}
									</span>
								)}
								{secondaryAction.label}
							</Button>
						)}

						{primaryAction && (
							<Button
								onClick={primaryAction.onClick}
								className={`${
									primaryAction.variant === "secondary"
										? isDarkMode
											? "border-gray-600 text-gray-300 hover:bg-gray-800"
											: "border-gray-300 text-gray-700 hover:bg-gray-50"
										: "bg-[#10A37F] text-white hover:bg-[#0d8f5f]"
								} transition-all duration-300`}
								variant={
									primaryAction.variant === "secondary"
										? "outline"
										: "default"
								}>
								{primaryAction.icon && (
									<span className="mr-2">
										{primaryAction.icon}
									</span>
								)}
								{primaryAction.label}
							</Button>
						)}
					</div>
				</div>

				{/* Search and Filters Row */}
				{(showSearch || showFilters) && (
					<div className="flex items-center gap-3">
						{showSearch && (
							<div className="flex-1 max-w-md">
								<div className="relative">
									<Search
										className={`absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 ${
											isDarkMode
												? "text-gray-400"
												: "text-gray-500"
										}`}
									/>
									<Input
										type="text"
										placeholder={searchPlaceholder}
										value={searchValue}
										onChange={(e) =>
											handleSearchChange(e.target.value)
										}
										className={`pl-10 ${
											isDarkMode
												? "bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:border-[#10A37F]"
												: "bg-white border-gray-300 text-black placeholder-gray-500 focus:border-[#10A37F]"
										}`}
									/>
								</div>
							</div>
						)}

						{showFilters && (
							<Button
								variant="outline"
								size="sm"
								className={`${
									isDarkMode
										? "border-gray-600 text-gray-300 hover:bg-gray-800"
										: "border-gray-300 text-gray-700 hover:bg-gray-50"
								}`}>
								<Filter className="w-4 h-4 mr-2" />
								Filters
							</Button>
						)}
					</div>
				)}
			</div>

			{/* Content Area */}
			<div className="p-6">
				{children ? (
					children
				) : (
					/* Empty State */
					<div className="flex flex-col items-center justify-center py-16 px-4">
						<div
							className={`w-24 h-24 rounded-full flex items-center justify-center mb-6 ${
								isDarkMode ? "bg-gray-800" : "bg-gray-100"
							}`}>
							{customEmptyIcon || (
								<FileX
									className={`w-12 h-12 ${
										isDarkMode
											? "text-gray-400"
											: "text-gray-500"
									}`}
								/>
							)}
						</div>

						<h4
							className={`text-xl font-semibold mb-2 ${
								isDarkMode ? "text-white" : "text-gray-900"
							}`}>
							{emptyTitle || "No data found"}
						</h4>

						<p
							className={`text-center max-w-md mb-6 ${
								isDarkMode ? "text-gray-400" : "text-gray-600"
							}`}>
							{emptyDescription ||
								"There's nothing here yet. Get started by creating your first item."}
						</p>

						{(onEmptyAction || primaryAction) && (
							<Button
								onClick={
									onEmptyAction || primaryAction?.onClick
								}
								className="bg-[#10A37F] text-white hover:bg-[#0d8f5f] transition-all duration-300">
								<Plus className="w-4 h-4 mr-2" />
								{emptyActionLabel ||
									primaryAction?.label ||
									"Get Started"}
							</Button>
						)}
					</div>
				)}
			</div>
		</div>
	);
};

export default EmptyPageComponent;
