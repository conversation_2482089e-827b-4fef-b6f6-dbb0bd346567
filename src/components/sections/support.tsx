"use client";

import React from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import {
	Drawer,
	DrawerContent,
	Drawer<PERSON>eader,
	DrawerTitle,
	DrawerTrigger,
} from "@/components/ui/drawer";
import { useMediaQuery } from "@/hooks/use-media-query";
import apiClient from "@/lib/api/client";
import { useState } from "react";

export default function ContactSupport({
	trigger,
}: {
	trigger: React.ReactNode;
}) {
    const isMobile = useMediaQuery("(max-width: 768px)")
    const [email, setEmail] = useState("");
	const [message, setMessage] = useState("");
	const [status, setStatus] = useState<
		"idle" | "loading" | "success" | "error"
	>("idle");

    const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		setStatus("loading");
		try {
			await apiClient.post(
				"https://app.clickbuy.ai/extension-backend/api/v1/feedback",
				{
					email,
					message,
				}
			);
			setStatus("success");
			setEmail("");
			setMessage("");
		} catch (err) {
			console.error("❌ Feedback error:", err);
			setStatus("error");
		}
	};

	const content = (
		<form onSubmit={handleSubmit} className="space-y-4">
			<input
				type="email"
				value={email}
				onChange={(e) => setEmail(e.target.value)}
				placeholder="Your email"
				required
				className="w-full bg-slate-700 text-slate-50 placeholder:text-slate-400 border border-slate-600 rounded-md p-2 focus:outline-none focus:ring-green-500 focus:border-green-500"
			/>
			<textarea
				value={message}
				onChange={(e) => setMessage(e.target.value)}
				placeholder="How can we help?"
				required
				className="w-full bg-slate-700 text-slate-50 placeholder:text-slate-400 border border-slate-600 rounded-md p-2 h-28 resize-none focus:outline-none focus:ring-green-500 focus:border-green-500"
			/>
			<button
				type="submit"
				disabled={status === "loading"}
				className="bg-green-500 hover:bg-green-600 text-slate-900 font-medium px-4 py-2 rounded-md transition-colors disabled:opacity-50">
				{status === "loading" ? "Sending..." : "Send Message"}
			</button>
			{status === "success" && (
				<p className="text-green-500 text-sm">
					Message sent successfully.
				</p>
			)}
			{status === "error" && (
				<p className="text-red-500 text-sm">
					Something went wrong. Please try again.
				</p>
			)}
		</form>
	);

	return isMobile ? (
		<Drawer>
			<DrawerTrigger asChild>{trigger}</DrawerTrigger>
			<DrawerContent className="bg-slate-800 border-t border-slate-700">
				<DrawerHeader>
					<DrawerTitle className="text-slate-50">
						Contact Support
					</DrawerTitle>
				</DrawerHeader>
				<div className="p-4">{content}</div>
			</DrawerContent>
		</Drawer>
	) : (
		<Dialog>
			<DialogTrigger asChild>{trigger}</DialogTrigger>
			<DialogContent className="bg-slate-800 border border-slate-700 text-slate-50">
				<DialogHeader>
					<DialogTitle className="text-slate-50">
						Contact Support
					</DialogTitle>
				</DialogHeader>
				{content}
			</DialogContent>
		</Dialog>
	);
}
