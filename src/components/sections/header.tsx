"use client";

import Link from "next/link";
import { useAuth } from "@/hooks/use-auth";
import { But<PERSON> } from "@/components/ui/button";
import { AuthDialog } from "@/components/auth/auth-dialog";
import { AuthDrawer } from "@/components/auth/auth-drawer";
import { Menu, X, LogOut, LayoutDashboard } from "lucide-react";
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";

export function Header() {
	const { user, isAuthenticated, logout, isLoggingOut } = useAuth();
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
	const [authDialogOpen, setAuthDialogOpen] = useState(false);
	const [authDrawerOpen, setAuthDrawerOpen] = useState(false);
	const [authMode, setAuthMode] = useState<"login" | "signup">("login");
	const [isScrolled, setIsScrolled] = useState(false);

	// Handle scroll effect
	useEffect(() => {
		const handleScroll = () => {
			setIsScrolled(window.scrollY > 0);
		};

		window.addEventListener("scroll", handleScroll);
		return () => window.removeEventListener("scroll", handleScroll);
	}, []);

	// Handle body scroll lock when mobile menu is open
	useEffect(() => {
		if (isMobileMenuOpen) {
			document.body.style.overflow = "hidden";
		} else {
			document.body.style.overflow = "unset";
		}

		return () => {
			document.body.style.overflow = "unset";
		};
	}, [isMobileMenuOpen]);

	// Close mobile menu on escape key
	useEffect(() => {
		const handleEscape = (e: KeyboardEvent) => {
			if (e.key === "Escape" && isMobileMenuOpen) {
				setIsMobileMenuOpen(false);
			}
		};

		document.addEventListener("keydown", handleEscape);
		return () => document.removeEventListener("keydown", handleEscape);
	}, [isMobileMenuOpen]);

	const searchParams = useSearchParams();
	const router = useRouter();

	useEffect(() => {
		const authParam = searchParams?.get("auth");
		if (authParam === "login" || authParam === "signup") {
			setAuthMode(authParam);
			setAuthDialogOpen(true);
		}
	}, [searchParams]);

	const handleLogout = async () => {
		try {
			logout();
			setIsMobileMenuOpen(false);
		} catch (error) {
			console.error("Logout failed:", error);
		}
	};

	const openAuthDialog = (mode: "login" | "signup") => {
		setAuthMode(mode);
		setAuthDialogOpen(true);
		router.push(`/?auth=${mode}`);
	};

	const openAuthDrawer = (mode: "login" | "signup") => {
		setAuthMode(mode);
		setAuthDrawerOpen(true);
		setIsMobileMenuOpen(false);
	};

	const handleNavClick = () => {
		setIsMobileMenuOpen(false);
		router.push("/")
	};

	// Smooth scroll function
	const scrollToSection = (sectionId: string) => {
		const element = document.getElementById(sectionId);
		if (element) {
			element.scrollIntoView({ behavior: "smooth" });
		}
		setIsMobileMenuOpen(false);
	};

	const scrollToTop = () => {
		window.scrollTo({ top: 0, behavior: "smooth" });
	};

	// Navigation items
	const navigationItems = [
		{ name: "About AEP", id: "about" },
		{ name: "Benefits", id: "benefits" },
		{ name: "Features", id: "features" },
		{ name: "Contact", id: "cta" },
	];

	return (
		<>
			<header
				className={cn(
					"fixed top-0 w-full z-50 transition-all duration-300",
					isScrolled
						? "bg-slate-900/95 backdrop-blur-md border-b border-slate-700"
						: "bg-transparent"
				)}>
				<div className="max-w-7xl mx-auto pt-6 pb-4">
					<div className="flex items-center justify-between h-16">
						{/* Logo - scrolls to top */}
						<button
							onClick={scrollToTop}
							className="flex items-center transition-transform hover:scale-105 z-50 relative">
							<Image
								src="/logos/logo-white.svg"
								alt="Clickbuy logo"
								width={48}
								height={48}
								className="w-30 h-30"
								priority
							/>
							<div className="-ml-4">
								<h1 className="text-xl font-semibold text-slate-50">
									ClickBuy
								</h1>
								<span className="text-sm font-medium text-green-500">
									AEP Platform
								</span>
							</div>
						</button>

						<div className="hidden md:flex items-center space-x-8">
							{navigationItems.map((item) => (
								<button
									key={item.name}
									onClick={() => scrollToSection(item.id)}
									className="text-slate-50 hover:text-green-500 transition-colors duration-200 font-medium relative group">
									{item.name}
									<span className="absolute inset-x-0 -bottom-1 h-0.5 bg-green-500 scale-x-0 group-hover:scale-x-100 transition-transform duration-200" />
								</button>
							))}
						</div>


						<div className="hidden md:flex items-center space-x-4">
							{!isAuthenticated ? (
								<>
									<Button
										onClick={() => openAuthDialog("login")}
										variant="outline"
										size="sm"
										className="border-slate-700 bg-transparent text-slate-50 hover:border-green-500 hover:text-green-500">
										Login
									</Button>
									<Button
										onClick={() => openAuthDialog("signup")}
										size="sm"
										className="bg-green-500 text-slate-50 hover:bg-green-600 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30 transition-all duration-300">
										Create Free Account
									</Button>
								</>
							) : (
								<div className="flex items-center space-x-3">
									<div className="flex items-center space-x-2 text-slate-50">
										<span className="text-sm">
											Welcome,{" "}
											{(() => {
												const match =
													user?.email?.match(
														/^[a-zA-Z]+/
													);
												return match ? match[0] : "";
											})()}
										</span>
									</div>
									<Link href="/aep-solutions/platforms">
										<Button
											size="sm"
											className="bg-green-500 text-slate-50 hover:bg-green-600 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30 transition-all duration-300">
											<LayoutDashboard className="h-4 w-4 mr-2" />
											Go to AEP
										</Button>
									</Link>
									<Button
										variant="outline"
										size="sm"
										onClick={handleLogout}
										disabled={isLoggingOut}
										className="border-slate-700 bg-transparent text-slate-50 hover:border-red-500 hover:text-red-500">
										<LogOut className="h-4 w-4 mr-2" />
										{isLoggingOut
											? "Logging out..."
											: "Logout"}
									</Button>
								</div>
							)}
						</div>

						<button
							onClick={() =>
								setIsMobileMenuOpen(!isMobileMenuOpen)
							}
							className="md:hidden p-2 rounded-lg transition-colors text-slate-50 hover:bg-slate-800 z-50 relative"
							aria-label="Toggle navigation menu">
							{isMobileMenuOpen ? (
								<X size={24} />
							) : (
								<Menu size={24} />
							)}
						</button>
					</div>
				</div>

				{/* Mobile Sidebar Overlay */}
				{isMobileMenuOpen && (
					<div
						className="fixed inset-0 bg-black/50 z-40 md:hidden"
						onClick={() => setIsMobileMenuOpen(false)}
					/>
				)}

				{/* Mobile Sidebar */}
				<div
					className={`
          fixed top-0 right-0 h-full w-[85vw] max-w-sm bg-slate-900 border-l border-slate-700 z-50 md:hidden
          transform transition-transform duration-300 ease-in-out
          ${isMobileMenuOpen ? "translate-x-0" : "translate-x-full"}
        `}>
					<div className="flex flex-col h-full">
						{/* Header with close button */}
						<div className="flex items-center justify-between p-6 border-b border-slate-700">
							<div className="flex items-center space-x-2">
								<Image
									src="/logos/logo-white.svg"
									alt="Clickbuy logo"
									width={28}
									height={28}
									className="w-7 h-7"
									priority
								/>
								<span className="text-lg font-semibold text-slate-50">
									ClickBuy
								</span>
							</div>
							<button
								onClick={() => setIsMobileMenuOpen(false)}
								className="p-2 rounded-lg transition-colors text-slate-400 hover:bg-slate-800 hover:text-slate-50"
								aria-label="Close navigation menu">
								<X size={20} />
							</button>
						</div>

						{/* Navigation Menu */}
						<div className="flex-1 px-6 py-6">
							<nav className="flex flex-col space-y-1">
								{navigationItems.map((item) => (
									<button
										key={item.name}
										onClick={() => scrollToSection(item.id)}
										className="flex items-center px-4 py-3 text-slate-400 hover:text-slate-50 hover:bg-slate-800 rounded-lg transition-all text-base font-medium w-full text-left">
										{item.name}
									</button>
								))}
							</nav>

							{/* User info for authenticated mobile users */}
							{isAuthenticated && user && (
								<div className="mt-8 p-4 bg-slate-800 rounded-lg border border-slate-700">
									<div className="flex items-center space-x-2 text-slate-400">
										<span className="text-sm">
											Signed in as:
										</span>
									</div>
									<div className="text-slate-50 font-medium truncate">
										{(() => {
											const match =
												user?.email?.match(
													/^[a-zA-Z]+/
												);
											return match ? match[0] : "";
										})()}
									</div>
									{user.role && (
										<div className="text-green-500 text-xs font-medium uppercase mt-1">
											{user.role}
										</div>
									)}
								</div>
							)}
						</div>

						{/* Auth Buttons */}
						<div className="p-6 border-t border-slate-700">
							<div className="flex flex-col gap-3">
								{!isAuthenticated ? (
									<>
										<Button
											onClick={() =>
												openAuthDrawer("login")
											}
											variant="outline"
											size="lg"
											className="w-full border-slate-700 bg-transparent text-slate-50 hover:border-green-500 hover:text-green-500">
											Login
										</Button>
										<Button
											onClick={() =>
												openAuthDrawer("signup")
											}
											size="lg"
											className="w-full bg-green-500 text-slate-50 hover:bg-green-600">
											Start Free Trial
										</Button>
									</>
								) : (
									<>
										<Link
											href="/aep-solutions"
											onClick={handleNavClick}>
											<Button
												size="lg"
												className="w-full bg-green-500 text-slate-50 hover:bg-green-600">
												<LayoutDashboard className="h-4 w-4 mr-2" />
												Dashboard
											</Button>
										</Link>
										<Button
											variant="outline"
											onClick={handleLogout}
											disabled={isLoggingOut}
											size="lg"
											className="w-full border-slate-700 bg-transparent text-slate-50 hover:border-red-500 hover:text-red-500">
											<LogOut className="h-4 w-4 mr-2" />
											{isLoggingOut
												? "Logging out..."
												: "Logout"}
										</Button>
									</>
								)}
							</div>
						</div>
					</div>
				</div>
			</header>

			{/* Auth Dialog for Desktop */}
			<AuthDialog
				isOpen={authDialogOpen}
				onClose={() => {
					setAuthDialogOpen(false)
					router.push("/")
				}}
				mode={authMode}
				onSwitchMode={setAuthMode}
			/>

			{/* Auth Drawer for Mobile */}
			<AuthDrawer
				isOpen={authDrawerOpen}
				onClose={() => {
					setAuthDrawerOpen(false)
					router.push("/")
				}}
				mode={authMode}
				onSwitchMode={setAuthMode}
			/>
		</>
	);
}
