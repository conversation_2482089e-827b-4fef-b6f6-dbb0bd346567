"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { AuthDialog } from "@/components/auth/auth-dialog";
import { AuthDrawer } from "@/components/auth/auth-drawer";
import { useAuth } from "@/hooks/use-auth";
import Link from "next/link";
import { LayoutDashboard } from "lucide-react";

export const CTASection: React.FC = () => {
	const { isAuthenticated } = useAuth();
	const [authDialogOpen, setAuthDialogOpen] = useState(false);
	const [authDrawerOpen, setAuthDrawerOpen] = useState(false);
	const [authMode, setAuthMode] = useState<"login" | "signup">("signup");

	const handleStartTrial = () => {
		if (isAuthenticated) {
			window.location.href = "/aep-solutions/platforms";
		} else {
			const isMobile = window.innerWidth < 768;

			if (isMobile) {
				setAuthMode("signup");
				setAuthDrawerOpen(true);
			} else {
				setAuthMode("signup");
				setAuthDialogOpen(true);
			}
		}
	};

	return (
		<>
			<section
				id="cta"
				className="py-24 bg-gradient-to-br from-slate-800 to-slate-900 my-16 relative overflow-hidden w-full">
				<div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_30%,rgba(34,197,94,0.15)_0%,transparent_70%),radial-gradient(circle_at_70%_70%,rgba(34,197,94,0.1)_0%,transparent_70%)] pointer-events-none" />

				<div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
					<h2 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
						Ready to Level Up with AI?
					</h2>

					<h3 className="text-xl lg:text-2xl text-green-500 font-semibold mb-8 max-w-4xl mx-auto">
						Transform your business from an outdated, human-software
						focussed model to a future-proof, AI-first operation.
					</h3>

					{isAuthenticated ? (
						<Link href="/aep-solutions/platforms">
							<Button
								size="lg"
								className="text-lg px-12 py-6 mb-4 bg-green-500 text-slate-50 hover:bg-green-600 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30 transition-all duration-300">
								<LayoutDashboard className="h-4 w-4 mr-2" />
								Go to AEP
							</Button>
						</Link>
					) : (
						<Button
							onClick={handleStartTrial}
							size="lg"
							className="text-lg px-12 py-6 mb-4 bg-green-500 text-slate-50 hover:bg-green-600 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30 transition-all duration-300 cursor-pointer">
							Create Free Account
						</Button>
					)}

					<p className="text-slate-400 mb-4">
						No credit card details needed.
					</p>

					<p className="text-lg font-medium text-slate-50">
						Once you see the power of an AI-first business through
						AEP, you can&apos;t unseen it.
					</p>
				</div>
			</section>

			<AuthDialog
				isOpen={authDialogOpen}
				onClose={() => setAuthDialogOpen(false)}
				mode={authMode}
				onSwitchMode={setAuthMode}
			/>

			<AuthDrawer
				isOpen={authDrawerOpen}
				onClose={() => setAuthDrawerOpen(false)}
				mode={authMode}
				onSwitchMode={setAuthMode}
			/>
		</>
	);
};
