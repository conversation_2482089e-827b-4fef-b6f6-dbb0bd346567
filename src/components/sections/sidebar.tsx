"use client";

import React from "react";
import {
	Home,
	BarChart3,
	Settings,
	Users,
	ShoppingCart,
	Package,
	Sparkles,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface SidebarItem {
	icon: React.ReactNode;
	label: string;
	href: string;
	isActive?: boolean;
}

const sidebarItems: SidebarItem[] = [
	{
		icon: <Home className="w-5 h-5" />,
		label: "Dashboard",
		href: "/dashboard",
		isActive: true,
	},
	{
		icon: <BarChart3 className="w-5 h-5" />,
		label: "Analytics",
		href: "/analytics",
	},
	{
		icon: <ShoppingCart className="w-5 h-5" />,
		label: "Orders",
		href: "/orders",
	},
	{
		icon: <Package className="w-5 h-5" />,
		label: "Products",
		href: "/products",
	},
	{
		icon: <Users className="w-5 h-5" />,
		label: "Customers",
		href: "/customers",
	},
	{
		icon: <Sparkles className="w-5 h-5" />,
		label: "AI Agents",
		href: "/ai-agents",
	},
	{
		icon: <Settings className="w-5 h-5" />,
		label: "Settings",
		href: "/settings",
	},
];

export const CustomSidebar: React.FC = () => {
	return (
		<div className="h-full flex flex-col bg-slate-800 border-r border-slate-700">
			{/* Logo */}
			<div className="p-6 border-b border-slate-700">
				<div className="flex items-center space-x-3">
					<Sparkles className="w-8 h-8 text-green-500" />
					<div>
						<h2 className="text-lg font-semibold text-slate-50">
							ClickBuyDeals
						</h2>
						<p className="text-sm text-green-500">AEP Platform</p>
					</div>
				</div>
			</div>

			{/* Navigation */}
			<nav className="flex-1 p-4 space-y-2">
				{sidebarItems.map((item) => (
					<a
						key={item.label}
						href={item.href}
						className={cn(
							"flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200",
							item.isActive
								? "bg-green-500 text-slate-50 shadow-lg"
								: "text-slate-400 hover:text-slate-50 hover:bg-slate-700"
						)}>
						{item.icon}
						<span className="font-medium">{item.label}</span>
					</a>
				))}
			</nav>

			{/* Bottom CTA */}
			<div className="p-4 border-t border-slate-700">
				<div className="bg-slate-700 rounded-lg p-4 text-center">
					<h3 className="text-sm font-semibold text-slate-50 mb-2">
						Upgrade to Pro
					</h3>
					<p className="text-xs text-slate-400 mb-3">
						Get advanced AI features and priority support
					</p>
					<Button
						size="sm"
						className="w-full bg-green-500 text-slate-50 hover:bg-green-600">
						Upgrade Now
					</Button>
				</div>
			</div>
		</div>
	);
};
