"use client";

import React from "react";
import { TrendingUp, RotateCcw, Bar<PERSON>hart3 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import type { BenefitCard } from "@/types";
import { CreateAccountButton } from "../create-free-account-btn";

const benefits: BenefitCard[] = [
	{
		icon: <TrendingUp className="w-12 h-12 text-green-500" />,
		title: "Cut Costs by Up to 80%",
		description:
			"Minimise overhead by automating costly software and labour-intensive workflows.",
	},
	{
		icon: <RotateCcw className="w-12 h-12 text-green-500" />,
		title: "Supercharge Productivity",
		description:
			"AI intelligence quadruples annually—a 300% increase in capability, so your edge only grows.",
	},
	{
		icon: <BarChart3 className="w-12 h-12 text-green-500" />,
		title: "Smarter Business Decisions",
		description:
			"Gain insights instantly with powerful AI analytics and real-time data processing.",
	},
];

export const BenefitsSection: React.FC = () => {
	return (
		<section
			className="py-24 bg-slate-800 my-16 relative overflow-hidden"
			id="benefits">
			<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent" />

			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="text-center mb-16">
					<h2 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
						The AEP AI platform that works not only in your
						business, but on it, 24/7
					</h2>
				</div>

				<div className="grid md:grid-cols-3 gap-8">
					{benefits.map((benefit, index) => (
						<Card
							key={index}
							className="p-8 text-center group hover:border-green-500 transition-all duration-300 relative overflow-hidden bg-gradient-to-br from-slate-700 to-slate-600 border border-slate-600 rounded-xl">
							<div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-green-500/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-500" />

							<CardContent className="space-y-4 p-0">
								<div className="flex justify-center">
									{benefit.icon}
								</div>
								<h3 className="text-xl font-semibold text-slate-50">
									{benefit.title}
								</h3>
								<p className="text-slate-400">
									{benefit.description}
								</p>
							</CardContent>
						</Card>
					))}
				</div>

				<div className="text-center mt-16">
					<CreateAccountButton size="lg" className="text-lg px-8" />
				</div>
			</div>
		</section>
	);
};
