import React, { useEffect, useState } from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	DialogTrigger,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { But<PERSON> } from "@/components/ui/button";
import {
	FileText,
	Shield,
	CreditCard,
	Users,
	AlertTriangle,
	Scale,
	Gavel,
	BookOpen,
} from "lucide-react";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";


const TermsOfServiceDialog = () => {
	const [open, setOpen] = useState(false);

	const searchParams = useSearchParams();
	const router = useRouter()

	const handleIAgree = () => {
		setOpen(false)
		router.push("/")
	}


useEffect(() => {
	if (searchParams?.get("showTerms") === "1") {
		setOpen(true);
	}
}, [searchParams]);

	const sections = [
		{
			id: "acceptance",
			title: "1. Acceptance of Terms",
			icon: <FileText className="w-5 h-5" />,
			content: `By accessing and using ClickBuyDeals services, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service. These terms constitute a legally binding agreement between you and ClickBuy AI Limited (trading as ClickBuyDeals).

If you do not agree with any part of these terms, you must not use our services. Your continued use of our platform constitutes acceptance of any updates or modifications to these terms.

These terms are effective as of March 1, 2025, and will remain in effect until terminated in accordance with the provisions outlined herein.`,
		},
		{
			id: "services",
			title: "2. Description of Services",
			icon: <BookOpen className="w-5 h-5" />,
			content: `ClickBuyDeals operates as an Agentic E-commerce Platform (AEP) that utilizes artificial intelligence to provide automated shopping, deal discovery, and procurement services.

Our services include but are not limited to:
• AI-powered product search and comparison
• Automated deal notifications and alerts
• Price monitoring and tracking services
• Bulk purchasing and procurement assistance
• 24/7 AI agent support for e-commerce activities

We reserve the right to modify, suspend, or discontinue any aspect of our services at any time with reasonable notice to users. Service availability may vary by geographic location and is subject to technical limitations.`,
		},
		{
			id: "user-obligations",
			title: "3. User Responsibilities and Conduct",
			icon: <Users className="w-5 h-5" />,
			content: `As a user of ClickBuyDeals, you agree to:

Account Security: Maintain the confidentiality of your account credentials and notify us immediately of any unauthorized access or security breaches.

Accurate Information: Provide truthful, accurate, and complete information when creating your account and making purchases.

Lawful Use: Use our services only for lawful purposes and in compliance with all applicable laws and regulations.

Prohibited Activities: You must not:
• Attempt to gain unauthorized access to our systems
• Use our platform to engage in fraudulent activities
• Violate intellectual property rights
• Interfere with the proper functioning of our services
• Use automated systems to access our platform without permission

Age Requirement: You must be at least 18 years old or have parental consent to use our services.`,
		},
		{
			id: "payments",
			title: "4. Payment Terms and Billing",
			icon: <CreditCard className="w-5 h-5" />,
			content: `Payment Processing: All payments are processed securely through trusted third-party payment processors. We do not store your complete payment information on our servers.

Pricing: Prices displayed on our platform are subject to change without notice. The price charged will be the price displayed at the time of purchase confirmation.

Billing: You authorize us to charge your selected payment method for all purchases made through our platform. Recurring charges, if applicable, will be clearly disclosed before enrollment.

Refunds and Returns: Refund policies vary by merchant and product. We will assist in facilitating returns and refunds according to the applicable merchant policies.

Disputes: Payment disputes should be reported within 60 days of the transaction date. We will work with you and our payment processors to resolve legitimate disputes.

Fees: We may charge service fees for certain transactions, which will be clearly disclosed before completion of the transaction.`,
		},
		{
			id: "ai-services",
			title: "5. AI Services and Limitations",
			icon: <Shield className="w-5 h-5" />,
			content: `AI Technology: Our platform utilizes artificial intelligence to provide automated services. While we strive for accuracy, AI recommendations and actions are not guaranteed to be error-free.

Service Limitations: You acknowledge that:
• AI decisions are based on available data and algorithms
• Results may vary and are not guaranteed
• Human oversight may be required for complex transactions
• Technical limitations may affect service availability

User Supervision: You are responsible for reviewing and approving AI-generated recommendations before execution. We recommend maintaining oversight of automated processes.

Continuous Improvement: Our AI systems are continuously learning and improving. Performance and accuracy may vary as the system evolves.

No Warranty: AI services are provided "as is" without warranties of specific outcomes or results.`,
		},
		{
			id: "liability",
			title: "6. Limitation of Liability",
			icon: <AlertTriangle className="w-5 h-5" />,
			content: `Disclaimer of Warranties: Our services are provided "as is" and "as available" without warranties of any kind, either express or implied.

Limitation of Damages: To the maximum extent permitted by law, ClickBuyDeals shall not be liable for any indirect, incidental, special, consequential, or punitive damages.

Maximum Liability: Our total liability to you for any claims arising from these terms or your use of our services shall not exceed the amount you paid to us in the 12 months preceding the claim.

Third-Party Services: We are not responsible for the actions, products, or services of third-party merchants or service providers accessed through our platform.

Force Majeure: We shall not be liable for any failure or delay in performance due to circumstances beyond our reasonable control.`,
		},
		{
			id: "intellectual-property",
			title: "7. Intellectual Property Rights",
			icon: <Scale className="w-5 h-5" />,
			content: `Platform Ownership: All content, features, and functionality of the ClickBuyDeals platform, including but not limited to text, graphics, logos, software, and AI algorithms, are owned by ClickBuy AI Limited.

User License: We grant you a limited, non-exclusive, non-transferable license to access and use our services for your personal or business purposes in accordance with these terms.

User Content: You retain ownership of any content you provide to us but grant us a license to use, process, and analyze such content to provide our services.

Restrictions: You may not:
• Copy, modify, or distribute our proprietary content
• Reverse engineer our software or algorithms
• Use our trademarks without permission
• Create derivative works based on our platform

Trademark Notice: ClickBuyDeals, ClickBuy, and associated logos are trademarks of ClickBuy AI Limited.`,
		},
		{
			id: "termination",
			title: "8. Account Termination",
			icon: <Gavel className="w-5 h-5" />,
			content: `Termination by You: You may terminate your account at any time by contacting our customer support or using the account closure feature in your dashboard.

Termination by Us: We reserve the right to suspend or terminate your account if you violate these terms, engage in fraudulent activity, or for any other reason we deem necessary to protect our platform and users.

Effect of Termination: Upon termination:
• Your access to the platform will be immediately revoked
• We may delete your account data in accordance with our privacy policy
• Outstanding obligations (payments, refunds) will be processed according to our policies
• These terms will remain in effect for any outstanding matters

Data Retention: We may retain certain information as required by law or for legitimate business purposes even after account termination.`,
		},
	];

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>
				<button className="text-slate-400 hover:text-green-400 transition-colors duration-300 text-sm font-medium">
					Terms of Service
				</button>
			</DialogTrigger>

			<DialogContent className="max-w-4xl max-h-[80vh] bg-slate-900 border-slate-700">
				<DialogHeader className="border-b border-slate-700 pb-4">
					<div className="flex items-center gap-3">
						<div className="p-2 bg-green-500/10 rounded-lg">
							<Gavel className="w-6 h-6 text-green-400" />
						</div>
						<div>
							<DialogTitle className="text-2xl font-bold text-white">
								Terms of Service
							</DialogTitle>
							<p className="text-slate-400 text-sm mt-1">
								Your agreement with ClickBuyDeals • Effective
								March 1, 2025
							</p>
						</div>
					</div>
				</DialogHeader>

				<ScrollArea className="h-[60vh] pr-4">
					<div className="space-y-8 py-4">
						{sections.map((section, index) => (
							<div key={section.id} className="space-y-4">
								<div className="flex items-center gap-3">
									<div className="p-2 bg-slate-800 rounded-lg text-green-400">
										{section.icon}
									</div>
									<h3 className="text-xl font-semibold text-white">
										{section.title}
									</h3>
								</div>

								<div className="pl-12">
									<div className="text-slate-300 leading-relaxed whitespace-pre-line">
										{section.content}
									</div>
								</div>

								{index < sections.length - 1 && (
									<div className="border-b border-slate-800 mt-6"></div>
								)}
							</div>
						))}

						{/* Contact and Governing Law Section */}
						<div className="bg-slate-800/50 rounded-lg p-6 border border-slate-700">
							<div className="flex items-center gap-3 mb-4">
								<div className="p-2 bg-green-500/10 rounded-lg">
									<Scale className="w-5 h-5 text-green-400" />
								</div>
								<h3 className="text-xl font-semibold text-white">
									9. Governing Law and Contact Information
								</h3>
							</div>

							<div className="space-y-4 text-slate-300">
								<div>
									<h4 className="font-semibold text-white mb-2">
										Governing Law
									</h4>
									<p className="text-sm">
										These Terms of Service are governed by
										and construed in accordance with the
										laws of England and Wales. Any disputes
										arising from these terms shall be
										subject to the exclusive jurisdiction of
										the courts of England and Wales.
									</p>
								</div>

								<div>
									<h4 className="font-semibold text-white mb-2">
										Contact Information
									</h4>
									<p className="text-sm mb-2">
										For questions about these Terms of
										Service, please contact us:
									</p>
									<div className="text-sm">
										<div>
											📧 <EMAIL>
										</div>
										<div className="mt-1">
											📬 ClickBuy AI Limited (trading as
											ClickBuyDeals)
											<br />
											Unit 7, Sterling Business Park
											<br />
											Scunthorpe, DN15 8QP
											<br />
											United Kingdom
										</div>
									</div>
								</div>

								<div>
									<h4 className="font-semibold text-white mb-2">
										Changes to Terms
									</h4>
									<p className="text-sm">
										We may update these Terms of Service
										from time to time. We will notify you of
										any material changes via email or
										through our platform. Your continued use
										of our services after such modifications
										constitutes acceptance of the updated
										terms.
									</p>
								</div>
							</div>
						</div>

						{/* Acknowledgment */}
						<div className="bg-green-500/5 border border-green-500/20 rounded-lg p-6">
							<div className="text-center">
								<h3 className="text-lg font-semibold text-white mb-3">
									Acknowledgment
								</h3>
								<p className="text-slate-300 text-sm">
									By using ClickBuyDeals services, you
									acknowledge that you have read, understood,
									and agree to be bound by these Terms of
									Service. If you have any questions or
									concerns, please contact our customer
									support team before using our services.
								</p>
								<p className="text-green-400 text-sm font-medium mt-3">
									Last Updated: March 1, 2025
								</p>
							</div>
						</div>
					</div>
				</ScrollArea>

				<div className="border-t border-slate-700 pt-4 flex justify-end">
					<Button
						onClick={handleIAgree}
						className="bg-green-500 hover:bg-green-600 text-white font-medium px-6">
						I Agree
					</Button>
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default TermsOfServiceDialog;
