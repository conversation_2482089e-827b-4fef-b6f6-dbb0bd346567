"use client";

import React, { useEffect, useState } from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	Di<PERSON>Title,
	DialogTrigger,
} from "@/components/ui/dialog";
import {
	Drawer,
	DrawerContent,
	DrawerHeader,
	DrawerTitle,
	DrawerTrigger,
} from "@/components/ui/drawer";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { sections } from "@/constants/terms-services";
import {
	Shield,
	Mail,
	MapPin,
	AlertCircle,
} from "lucide-react";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useSearchParams } from "next/navigation";
import { useRouter } from "next/navigation";

const PrivacyPolicyDialog = () => {
	const [open, setOpen] = useState(false);
    const isMobile = useMediaQuery("(max-width: 768px)");
	const searchParams = useSearchParams();
	const router = useRouter()


useEffect(() => {
	if (searchParams?.get("showPrivacy") === "1") {
		setOpen(true);
	}
}, [searchParams]);

	const handleIUnderstand = () => {
		setOpen(false)

		router.push("/")
	}

	const renderContent = () => (
		<>
			<ScrollArea className="h-[60vh] pr-4">
				<div className="space-y-8 py-4">
					{sections.map((section, index) => (
						<div key={section.id} className="space-y-4">
							<div className="flex items-center gap-3">
								<div className="p-2 bg-slate-800 rounded-lg text-green-400">
									{React.createElement(section.icon, {
										className: "w-5 h-5",
									})}
								</div>
								<h3 className="text-xl font-semibold text-white">
									{section.title}
								</h3>
							</div>
							<div className="pl-12 text-slate-300 leading-relaxed whitespace-pre-line">
								{section.content}
							</div>
							{index < sections.length - 1 && (
								<div className="border-b border-slate-800 mt-6" />
							)}
						</div>
					))}

					{/* Contact & Additional Info — same as before */}
					<div className="bg-slate-800/50 rounded-lg p-6 border border-slate-700 mt-8">
						<div className="flex items-center gap-3 mb-4">
							<div className="p-2 bg-green-500/10 rounded-lg">
								<Mail className="w-5 h-5 text-green-400" />
							</div>
							<h3 className="text-xl font-semibold text-white">
								6. Contact Us
							</h3>
						</div>
						<p className="text-slate-300 mb-4">
							Contact our support team with questions:
						</p>
						<div className="space-y-3">
							<div className="flex items-center gap-3">
								<Mail className="w-4 h-4 text-green-400" />
								<a
									href="mailto:<EMAIL>"
									className="text-green-400 hover:text-green-300 transition-colors">
									<EMAIL>
								</a>
							</div>
							<div className="flex items-start gap-3">
								<MapPin className="w-4 h-4 text-green-400 mt-1" />
								<div className="text-slate-300 text-sm">
									<div className="font-medium">
										ClickBuy AI Limited
									</div>
									<div>Unit 7, Sterling Business Park</div>
									<div>Scunthorpe, DN15 8QP</div>
									<div>United Kingdom</div>
								</div>
							</div>
						</div>
						<div className="mt-4 p-3 bg-green-500/5 border border-green-500/20 rounded-lg">
							<div className="flex items-center gap-2 text-green-400 text-sm">
								<AlertCircle className="w-4 h-4" />
								<span className="font-medium">
									Response Time:
								</span>
							</div>
							<p className="text-slate-300 text-sm mt-1">
								We aim to respond within one business day.
							</p>
						</div>
					</div>
				</div>
			</ScrollArea>

			<div className="border-t border-slate-700 pt-4 flex justify-end">
				<Button
					onClick={handleIUnderstand}
					className="bg-green-500 hover:bg-green-600 text-white font-medium px-6">
					I Understand
				</Button>
			</div>
		</>
	);

	// Switch between Dialog (desktop) and Drawer (mobile)
	if (isMobile) {
		return (
			<Drawer open={open} onOpenChange={setOpen}>
				<DrawerTrigger asChild>
					<button className="text-slate-400 hover:text-green-400 transition-colors duration-300 text-sm font-medium">
						Privacy Policy
					</button>
				</DrawerTrigger>
				<DrawerContent className="bg-slate-900 border-slate-700 max-h-[90vh]">
					<DrawerHeader className="border-b border-slate-700 pb-4">
						<DrawerTitle className="text-white text-xl">
							Privacy Policy
						</DrawerTitle>
					</DrawerHeader>
					{renderContent()}
				</DrawerContent>
			</Drawer>
		);
	}

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>
				<button className="text-slate-400 hover:text-green-400 transition-colors duration-300 text-sm font-medium">
					Privacy Policy
				</button>
			</DialogTrigger>
			<DialogContent className="max-w-4xl max-h-[80vh] bg-slate-900 border-slate-700">
				<DialogHeader className="border-b border-slate-700 pb-4">
					<div className="flex items-center gap-3">
						<div className="p-2 bg-green-500/10 rounded-lg">
							<Shield className="w-6 h-6 text-green-400" />
						</div>
						<div>
							<DialogTitle className="text-2xl font-bold text-white">
								Privacy Policy
							</DialogTitle>
							<p className="text-slate-400 text-sm mt-1">
								How we protect and use your data • Effective
								March 1, 2025
							</p>
						</div>
					</div>
				</DialogHeader>
				{renderContent()}
			</DialogContent>
		</Dialog>
	);
};

export default PrivacyPolicyDialog;
