"use client";

import React, { useState } from "react";
import Link from "next/link";
import { Search, Users, TrendingUp, Package, X, Clock } from "lucide-react";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { CreateAccountButton } from "@/components/create-free-account-btn";
import type { FeatureCard } from "@/types";

const features: FeatureCard[] = [
	{
		icon: <Search className="w-14 h-14 text-green-500" />,
		title: "AI Deal Sourcing",
		description:
			"Automate product discovery and market research with 24/7 sourcing agents that never sleep, never miss opportunities, and continuously optimize your product portfolio.",
		buttonText: "Learn more",
		link: "/deals-finder",
	},
	{
		icon: <Users className="w-14 h-14 text-green-500" />,
		title: "Digital Workforce",
		description:
			"Suggest AI agents to automate tasks and scale operations in your business. Only Pay for agents once we build them and you use them no upfront costs.",
		buttonText: "Learn more",
		link: "/digital-workforce",
	},
	{
		icon: <TrendingUp className="w-14 h-14 text-green-500" />,
		title: "Cross-Selling Marketplace",
		description:
			"Sell your stock on our marketplace. No upfront costs, just a 10% flat commission fee. We succeed when you succeed, creating a true partnership for growth.",
		buttonText: "Learn more",
		link: "/marketplace",
	},
	{
		icon: <Package className="w-14 h-14 text-green-500" />,
		title: "Systemise Fulfilment",
		description:
			"Your trusted eCommerce fulfilment & logistics partner. Managing warehouse order fulfilment effortlessly across UK, EU & USA. Perfect for Amazon, Shopify, TikTok Shop and more.",
		buttonText: "Learn More",
		link: "#systemise-fulfilment",
	},
];

export const FeaturesSection: React.FC = () => {
	const [showComingSoonModal, setShowComingSoonModal] = useState(false);

	const handleSystemiseFulfilmentClick = () => {
		window.open("https://systemisefulfilment.co.uk", "_blank");
	};

	const handleFeatureClick = (link: string | undefined) => {
		if (link === "#systemise-fulfilment") {
			setShowComingSoonModal(true);
		}
	};
	return (
		<section className="py-24 bg-slate-900" id="features">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				{/* Section Header */}
				<div className="text-center mb-16">
					<div className="w-24 h-1 bg-gradient-to-r from-green-500 to-transparent mx-auto mb-8" />
					<h2 className="text-3xl lg:text-4xl font-bold mb-4 text-slate-50">
						What You Can Do with ClickBuy
					</h2>
					<p className="text-xl text-slate-400 max-w-3xl mx-auto">
						Start building your AI-powered business using our
						powerful automation tools.
					</p>
				</div>

				{/* Features Grid */}
				<div className="grid md:grid-cols-2 lg:grid-cols-4 gap-x-3 mb-16">
					{features.map((feature, index) => (
						<Card
							key={index}
							className="p-8 text-center group hover:shadow-2xl hover:-translate-y-2 transition-all duration-300 relative overflow-hidden bg-gradient-to-br from-slate-800 to-slate-700 border border-slate-700 rounded-2xl flex flex-col h-full">
							<div className="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-green-500 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

							<CardContent className="space-y-6 p-0 flex-grow">
								<div className="flex justify-center">
									{feature.icon}
								</div>
								<h3 className="text-2xl font-semibold text-slate-50">
									{feature.title}
								</h3>
								<p className="text-slate-400 leading-relaxed">
									{feature.description}
								</p>
							</CardContent>

							<CardFooter className="flex flex-col items-center p-0 pt-6 mt-auto">
								{feature.link?.startsWith("http") ? (
									// External link - opens in new tab
									<a
										href={feature.link}
										target="_blank"
										rel="noopener noreferrer"
										className="w-full">
										<Button className="w-full bg-green-500 text-slate-50 hover:bg-green-600 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30 transition-all duration-300">
											{feature.buttonText}
										</Button>
									</a>
								) : feature.link === "#systemise-fulfilment" ? (
									// Systemise Fulfilment - show modal
									<Button
										onClick={() =>
											handleFeatureClick(feature.link)
										}
										className="w-full bg-green-500 text-slate-50 hover:bg-green-600 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30 transition-all duration-300">
										{feature.buttonText}
									</Button>
								) : (
									<Link
										href={feature.link || "#"}
										className="w-full">
										<Button className="w-full bg-green-500 text-slate-50 hover:bg-green-600 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-green-500/30 transition-all duration-300 cursor-pointer">
											{feature.buttonText}
										</Button>
									</Link>
								)}
							</CardFooter>
						</Card>
					))}
				</div>

				{/* CTA Section */}
				<div className="text-center">
					<CreateAccountButton size="lg" className="text-lg px-8" />
				</div>

				{/* Coming Soon Modal */}
				{showComingSoonModal && (
					<div className="fixed inset-0 z-50 overflow-y-auto">
						<div
							className="fixed inset-0 bg-black/70 transition-opacity"
							onClick={() => setShowComingSoonModal(false)}
						/>

						<div className="flex min-h-full items-center justify-center p-4">
							<div className="relative w-full max-w-md transform rounded-2xl border shadow-2xl bg-gray-800 border-gray-700">
								<div className="flex items-center justify-between p-6 pb-4">
									<div className="flex items-center gap-3">
										<div className="w-10 h-10 rounded-full flex items-center justify-center bg-orange-500/20">
											<Clock
												size={20}
												className="text-orange-500"
											/>
										</div>
										<div>
											<h3 className="text-lg font-semibold text-white">
												Coming Soon
											</h3>
											<p className="text-sm text-gray-300">
												Systemise Fulfilment Details
											</p>
										</div>
									</div>

									<button
										onClick={() =>
											setShowComingSoonModal(false)
										}
										className="p-2 rounded-lg transition-colors hover:bg-gray-700 text-gray-400 hover:text-white">
										<X size={18} />
									</button>
								</div>

								<div className="px-6 pb-6">
									<div className="mb-6 p-4 rounded-lg border bg-orange-500/10 border-orange-500/30">
										<div className="flex items-start gap-3">
											<Clock
												size={16}
												className="mt-0.5 text-orange-500"
											/>
											<div className="text-sm">
												<p className="font-medium mb-1 text-white">
													Page Under Development
												</p>
												<p className="text-gray-300">
													We&apos;re working on creating a
													detailed page for Systemise
													Fulfilment. In the meantime,
													you can visit their website
													directly to learn more about
													their services.
												</p>
											</div>
										</div>
									</div>

									<div className="flex gap-3">
										<Button
											onClick={() =>
												setShowComingSoonModal(false)
											}
											variant="outline"
											className="flex-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 bg-gray-700 text-gray-300 hover:bg-gray-600 border-gray-600">
											Close
										</Button>

										<Button
											onClick={() => {
												setShowComingSoonModal(false);
												handleSystemiseFulfilmentClick();
											}}
											className="flex-1 px-4 py-3 rounded-lg font-medium transition-all duration-200 bg-gradient-to-r from-[#19D86C] to-[#12C2E9] text-[#111215] hover:from-[#12C2E9] hover:to-[#19D86C] hover:scale-105 active:scale-95">
											Visit Website
										</Button>
									</div>
								</div>
							</div>
						</div>
					</div>
				)}
			</div>
		</section>
	);
};
