"use client";

import React from "react";
import { X, Check } from "lucide-react";
import { CreateAccountButton } from "@/components/create-free-account-btn";

export const AEPDefinitionSection: React.FC = () => {
	return (
		<section className="py-24 bg-slate-900" id="about">
			<div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="w-24 h-1 bg-gradient-to-r from-green-500 to-transparent mx-auto mb-12" />

				<div className="text-center mb-16">
					<h2 className="text-3xl lg:text-4xl font-bold mb-8 text-slate-50">
						What is an AEP Platform?
					</h2>
					<p className="text-lg lg:text-xl text-slate-400 max-w-4xl mx-auto leading-relaxed">
						An AEP (agentic e-commerce platform) is an AI-powered
						closed-loop system that aims to fully automate
						e-commerce operations, eliminating human involvement
						while following predefined business rules.
					</p>
				</div>

				<div className="grid md:grid-cols-2 gap-4 mb-8">
					<div className="flex items-center gap-3 p-4 bg-slate-800 border border-slate-700 rounded-lg">
						<X className="w-5 h-5 text-red-500 flex-shrink-0" />
						<span className="text-slate-300">
							Not a software or a SaaS tool
						</span>
					</div>
					<div className="flex items-center gap-3 p-4 bg-slate-800 border border-slate-700 rounded-lg">
						<X className="w-5 h-5 text-red-500 flex-shrink-0" />
						<span className="text-slate-300">
							Not an AI agent-building platform
						</span>
					</div>
					<div className="flex items-center gap-3 p-4 bg-slate-800 border border-slate-700 rounded-lg">
						<X className="w-5 h-5 text-red-500 flex-shrink-0" />
						<span className="text-slate-300">
							Not a marketplace to sell your products on
						</span>
					</div>
					<div className="flex items-center gap-3 p-4 bg-slate-800 border border-slate-700 rounded-lg">
						<X className="w-5 h-5 text-red-500 flex-shrink-0" />
						<span className="text-slate-300">
							Not a mentor or consultancy to grow your business
						</span>
					</div>
				</div>

				<div className="flex items-center gap-3 p-6 bg-green-950/30 border border-green-500/30 rounded-lg mb-12">
					<Check className="w-6 h-6 text-green-500 flex-shrink-0" />
					<span className="text-slate-300 text-lg">
						We are a new kind of support for e-commerce businesses —
						an AI-first AEP platform.
					</span>
				</div>

				<div className="text-center">
					<CreateAccountButton size="lg" className="text-lg px-8" />
				</div>
			</div>
		</section>
	);
};
