/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { PropsWithChildren, useState, useEffect } from "react";
import { ReactNode } from "react";
import { toast } from "sonner";

// Query Client
const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			retry: 1,
			refetchOnWindowFocus: false,
		},
	},
});

export function QueryProvider({ children }: { children: ReactNode }) {
	return (
		<QueryClientProvider client={queryClient}>
			{children}
		</QueryClientProvider>
	);
}

export function StripeProviderWrapper({ children }: PropsWithChildren) {
	const [stripePromise, setStripePromise] = useState<Promise<any> | null>(
		null
	);

	useEffect(() => {
		const loadStripeInBackground = async () => {
			try {
				console.log("🔄 Loading Stripe...");

				const stripe = loadStripe(
					process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
				);
				setStripePromise(stripe);

				// Wait for it to resolve to show success/error
				const result = await stripe;

				if (result) {
					console.log("✅ Stripe loaded successfully");
					// toast.success("Payment system ready");
				} else {
					throw new Error("Stripe failed to initialize");
				}
			} catch (error: any) {
				console.error("❌ Stripe loading failed:", error);
				toast.error("Payment system unavailable");
			}
		};

		loadStripeInBackground();
	}, []);

	return <Elements stripe={stripePromise}>{children}</Elements>;
}

// Combined Provider
export function AppProviders({ children }: { children: ReactNode }) {
	return (
		<QueryProvider>
			{children}
		</QueryProvider>
	);
}

export function useStripeStatus() {
	const [isReady, setIsReady] = useState(false);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const checkStripe = async () => {
			try {
				const stripe = await loadStripe(
					process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
				);
				if (stripe) {
					setIsReady(true);
				} else {
					setError("Stripe not available");
				}
			} catch (err: any) {
				setError(err.message);
			}
		};

		checkStripe();
	}, []);

	return { isReady, error };
}
