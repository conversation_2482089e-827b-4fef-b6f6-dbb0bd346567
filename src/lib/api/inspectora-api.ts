import {
	CatalogRequest,
	CatalogResponse,
	FilterValues,
	InspectoraApiResponse,
	SearchRequest,
	SearchResponse,
} from "@/constants/inspectoral-filter";
import axios from "axios";
import { getAccessToken } from "..";

// API Configuration
const INSPECTORA_API_BASE_URL =
	"https://app.clickbuy.ai/extension-backend/api/v1";
const AMAZON_API_BASE_URL = "https://app.clickbuy.ai/amazon/api/v1";
const API_TOKEN = typeof window !== 'undefined' ? getAccessToken() : null;

// Axios instances
const inspectoraApi = axios.create({
	baseURL: INSPECTORA_API_BASE_URL,
	headers: {
		Accept: "application/json",
		Authorization: `Bearer ${API_TOKEN}`,
	},
	timeout: 10000,
});

const amazonApi = axios.create({
	baseURL: AMAZON_API_BASE_URL,
	headers: {
		Accept: "application/json",
		Authorization: `Bearer ${API_TOKEN}`,
	},
	timeout: 15000,
});


export const mapFiltersToSearchRequest = (
	filters: FilterValues
): SearchRequest => {
	console.log("🔄 Mapping filters:", filters);

	let domain = filters.marketplace_id.toUpperCase();
	if (domain === "UK") domain = "GB";

	const request: SearchRequest = {
		domain: domain || "GB",
		perPage: 100,
		page: 0,
	};

	// Brand filter
	if (filters.brand?.trim()) {
		request.brand = filters.brand.trim();
	}

	// New filter mappings
	if (filters.current_SALES_gte)
		request.current_SALES_gte = parseFloat(filters.current_SALES_gte);
	if (filters.current_SALES_lte)
		request.current_SALES_lte = parseFloat(filters.current_SALES_lte);
	if (filters.isLowest_SALES !== null)
		request.isLowest_SALES = filters.isLowest_SALES;

	// Buy Box filters
	if (filters.current_BUY_BOX_SHIPPING_gte) {
		// Send "-1" only if isOutOfStock is true, otherwise exclude "-1" values
		if (
			filters.isOutOfStock === true ||
			filters.current_BUY_BOX_SHIPPING_gte !== "-1"
		) {
			request.current_BUY_BOX_SHIPPING_gte = parseFloat(
				filters.current_BUY_BOX_SHIPPING_gte
			);
		}
	}
	if (filters.current_BUY_BOX_SHIPPING_lte) {
		// Send "-1" only if isOutOfStock is true, otherwise exclude "-1" values
		if (
			filters.isOutOfStock === true ||
			filters.current_BUY_BOX_SHIPPING_lte !== "-1"
		) {
			request.current_BUY_BOX_SHIPPING_lte = parseFloat(
				filters.current_BUY_BOX_SHIPPING_lte
			);
		}
	}
	if (filters.isOutOfStock !== null)
		request.isOutOfStock = filters.isOutOfStock;

	// eBay filters
	if (filters.current_EBAY_NEW_gte && filters.current_EBAY_NEW_gte !== "-1") {
		request.current_EBAY_NEW_gte = parseFloat(filters.current_EBAY_NEW_gte);
	}
	if (filters.current_EBAY_NEW_lte && filters.current_EBAY_NEW_lte !== "-1") {
		request.current_EBAY_NEW_lte = parseFloat(filters.current_EBAY_NEW_lte);
	}
	if (filters.isEbayOutOfStock !== null)
		request.isEbayOutOfStock = filters.isEbayOutOfStock;

	// Offer count filters
	if (filters.current_COUNT_NEW_gte)
		request.current_COUNT_NEW_gte = parseInt(filters.current_COUNT_NEW_gte);
	if (filters.current_COUNT_NEW_lte)
		request.current_COUNT_NEW_lte = parseInt(filters.current_COUNT_NEW_lte);

	// Seller IDs (comma-separated to array)
	if (filters.buyBoxSeller?.trim()) {
		request.buyBoxSeller = filters.buyBoxSeller
			.split(",")
			.map((s) => s.trim())
			.filter((s) => s);
	}

	// Buy Box Amazon stats
	if (filters.buyBoxStatsAmazon30_gte)
		request.buyBoxStatsAmazon30_gte = parseFloat(
			filters.buyBoxStatsAmazon30_gte
		);
	if (filters.buyBoxStatsAmazon30_lte)
		request.buyBoxStatsAmazon30_lte = parseFloat(
			filters.buyBoxStatsAmazon30_lte
		);
	if (filters.buyBoxStatsAmazon90_gte)
		request.buyBoxStatsAmazon90_gte = parseFloat(
			filters.buyBoxStatsAmazon90_gte
		);
	if (filters.buyBoxStatsAmazon90_lte)
		request.buyBoxStatsAmazon90_lte = parseFloat(
			filters.buyBoxStatsAmazon90_lte
		);

	// Buy Box Top Seller stats
	if (filters.buyBoxStatsTopSeller30_gte)
		request.buyBoxStatsTopSeller30_gte = parseFloat(
			filters.buyBoxStatsTopSeller30_gte
		);
	if (filters.buyBoxStatsTopSeller30_lte)
		request.buyBoxStatsTopSeller30_lte = parseFloat(
			filters.buyBoxStatsTopSeller30_lte
		);
	if (filters.buyBoxStatsTopSeller90_gte)
		request.buyBoxStatsTopSeller90_gte = parseFloat(
			filters.buyBoxStatsTopSeller90_gte
		);
	if (filters.buyBoxStatsTopSeller90_lte)
		request.buyBoxStatsTopSeller90_lte = parseFloat(
			filters.buyBoxStatsTopSeller90_lte
		);

	// Buy Box Seller Count stats
	if (filters.buyBoxStatsSellerCount30_gte)
		request.buyBoxStatsSellerCount30_gte = parseInt(
			filters.buyBoxStatsSellerCount30_gte
		);
	if (filters.buyBoxStatsSellerCount30_lte)
		request.buyBoxStatsSellerCount30_lte = parseInt(
			filters.buyBoxStatsSellerCount30_lte
		);
	if (filters.buyBoxStatsSellerCount90_gte)
		request.buyBoxStatsSellerCount90_gte = parseInt(
			filters.buyBoxStatsSellerCount90_gte
		);
	if (filters.buyBoxStatsSellerCount90_lte)
		request.buyBoxStatsSellerCount90_lte = parseInt(
			filters.buyBoxStatsSellerCount90_lte
		);

	// Buy Box Standard Deviation
	if (filters.buyBoxStandardDeviation30_gte)
		request.buyBoxStandardDeviation30_gte = parseFloat(
			filters.buyBoxStandardDeviation30_gte
		);
	if (filters.buyBoxStandardDeviation30_lte)
		request.buyBoxStandardDeviation30_lte = parseFloat(
			filters.buyBoxStandardDeviation30_lte
		);
	if (filters.buyBoxStandardDeviation90_gte)
		request.buyBoxStandardDeviation90_gte = parseFloat(
			filters.buyBoxStandardDeviation90_gte
		);
	if (filters.buyBoxStandardDeviation90_lte)
		request.buyBoxStandardDeviation90_lte = parseFloat(
			filters.buyBoxStandardDeviation90_lte
		);
	if (filters.buyBoxStandardDeviation365_gte)
		request.buyBoxStandardDeviation365_gte = parseFloat(
			filters.buyBoxStandardDeviation365_gte
		);
	if (filters.buyBoxStandardDeviation365_lte)
		request.buyBoxStandardDeviation365_lte = parseFloat(
			filters.buyBoxStandardDeviation365_lte
		);

	// Variations
	if (filters.hasParentASIN !== null)
		request.hasParentASIN = filters.hasParentASIN;

	// Monthly sold (bought in past month)
	if (filters.monthlySold_gte)
		request.monthlySold_gte = parseInt(filters.monthlySold_gte);
	if (filters.monthlySold_lte)
		request.monthlySold_lte = parseInt(filters.monthlySold_lte);

	// Rating
	if (filters.current_RATING_gte)
		request.current_RATING_gte = parseFloat(filters.current_RATING_gte);
	if (filters.current_RATING_lte)
		request.current_RATING_lte = parseFloat(filters.current_RATING_lte);

	// Review count
	if (filters.current_COUNT_REVIEWS_gte)
		request.current_COUNT_REVIEWS_gte = parseInt(
			filters.current_COUNT_REVIEWS_gte
		);
	if (filters.current_COUNT_REVIEWS_lte)
		request.current_COUNT_REVIEWS_lte = parseInt(
			filters.current_COUNT_REVIEWS_lte
		);

	// Availability Amazon (array)
	if (filters.availabilityAmazon && filters.availabilityAmazon.length > 0) {
		request.availabilityAmazon = filters.availabilityAmazon;
	}

	// Package dimensions
	if (filters.packageLength_gte)
		request.packageLength_gte = parseFloat(filters.packageLength_gte);
	if (filters.packageLength_lte)
		request.packageLength_lte = parseFloat(filters.packageLength_lte);
	if (filters.packageWidth_gte)
		request.packageWidth_gte = parseFloat(filters.packageWidth_gte);
	if (filters.packageWidth_lte)
		request.packageWidth_lte = parseFloat(filters.packageWidth_lte);
	if (filters.packageHeight_gte)
		request.packageHeight_gte = parseFloat(filters.packageHeight_gte);
	if (filters.packageHeight_lte)
		request.packageHeight_lte = parseFloat(filters.packageHeight_lte);

	// Package weight
	if (filters.packageWeight_gte)
		request.packageWeight_gte = parseFloat(filters.packageWeight_gte);
	if (filters.packageWeight_lte)
		request.packageWeight_lte = parseFloat(filters.packageWeight_lte);

	// HazMat and Heat Sensitive
	if (filters.isHazMat !== null) request.isHazMat = filters.isHazMat;
	if (filters.isHeatSensitive !== null)
		request.isHeatSensitive = filters.isHeatSensitive;

	console.log("🎯 Final request:", request);
	return request;
};

export const fetchInspectoraData = async (
	asin: string,
	country: string
): Promise<InspectoraApiResponse> => {
	try {
		const response = await inspectoraApi.get("/inspectora", {
			params: { asin, country },
		});
		return response.data;
	} catch (error) {
		if (axios.isAxiosError(error)) {
			if (error.response?.status === 404) {
				throw new Error(
					`Product with ASIN ${asin} not found in ${country.toUpperCase()}`
				);
			} else if (error.response?.status === 401) {
				throw new Error("Unauthorized: Please check your API token");
			} else if (error.response?.status === 422) {
				throw new Error(
					"Invalid parameters: Please check ASIN and country format"
				);
			} else if (error.code === "ECONNABORTED") {
				throw new Error("Request timeout: Please try again");
			}
		}
		throw new Error(
			"Failed to fetch product data. Please try again later."
		);
	}
};

export const searchProducts = async (
	filters: FilterValues
): Promise<SearchResponse> => {
	try {
		const searchRequest = mapFiltersToSearchRequest(filters);
		console.log("🔍 Search request:", searchRequest);

		const response = await inspectoraApi.post(
			"/inspectora/search",
			searchRequest
		);
		console.log("✅ Search response:", response.data);

		const asins = response.data;
		return {
			success: true,
			data: asins,
			status_code: response.status,
		};
	} catch (error) {
		console.error("❌ Search error:", error);

		if (axios.isAxiosError(error)) {
			if (error.response?.status === 401) {
				throw new Error("Unauthorized: Please check your API token");
			} else if (error.response?.status === 422) {
				throw new Error(
					"Invalid search parameters: Please check your filters"
				);
			} else if (error.response?.status === 429) {
				throw new Error("Rate limit exceeded: Please try again later");
			} else if (error.code === "ECONNABORTED") {
				throw new Error("Search timeout: Please try again");
			}
		}
		throw new Error("Product search failed. Please try again later.");
	}
};

export const getProductDetails = async (
	asins: string[]
): Promise<CatalogResponse> => {
	try {
		if (asins.length === 0) {
			throw new Error("No ASINs provided");
		}

		if (asins.length > 10) {
			console.warn(`⚠️ Too many ASINs (${asins.length}), limiting to 10`);
			asins = asins.slice(0, 10);
		}

		const catalogRequest: CatalogRequest = { asins };
		console.log(
			`📦 Catalog request for ${asins.length} ASINs:`,
			catalogRequest
		);

		const response = await amazonApi.post(
			"/amz-product/catalog-item",
			catalogRequest
		);
		console.log(
			`✅ Catalog response: ${response.data.data.successful_count}/${response.data.data.total_requested} products loaded`
		);
		return response.data;
	} catch (error) {
		console.error("❌ Catalog error:", error);

		if (axios.isAxiosError(error)) {
			if (error.response?.status === 401) {
				throw new Error("Unauthorized: Please check your API token");
			} else if (error.response?.status === 422) {
				const errorData = error.response.data;
				if (errorData?.detail?.[0]?.type === "too_long") {
					throw new Error(
						"Too many ASINs: Maximum 10 ASINs per request"
					);
				}
				throw new Error("Invalid catalog request: Please check ASINs");
			} else if (error.response?.status === 429) {
				throw new Error("Rate limit exceeded: Please try again later");
			} else if (error.code === "ECONNABORTED") {
				throw new Error("Catalog request timeout: Please try again");
			}
		}
		throw new Error(
			"Failed to fetch product details. Please try again later."
		);
	}
};

export const normalizeCountryCode = (country: string): string => {
	const countryMap: Record<string, string> = {
		gb: "uk",
		"united-kingdom": "uk",
		"united-states": "us",
		deutschland: "de",
		france: "fr",
		italia: "it",
		espana: "es",
		canada: "ca",
		japan: "jp",
	};
	return countryMap[country.toLowerCase()] || country.toLowerCase();
};

export const inspectoraKeys = {
	all: ["inspectora"] as const,
	productData: (asin: string, country: string) =>
		[...inspectoraKeys.all, "product", asin, country] as const,
	search: (filters: FilterValues) =>
		[...inspectoraKeys.all, "search", filters] as const,
	catalog: (asins: string[]) =>
		[...inspectoraKeys.all, "catalog", asins] as const,
};

export default inspectoraApi;
