import {
	LoginCredentials,
	RefreshTokenResponse,
	RegisterCredentials,
	SellerOnboardingData,
	SellerRegistrationData,
	TokenResponse,
	User,
} from "@/types/auth";
import { createClient, tokenManager } from "./client";

// Auth service base URL
const getAuthBaseUrl = (): string => {
	return "https://app.clickbuy.ai/auth";
};

const authApiClient = createClient(getAuthBaseUrl());

export const authApi = {
	// Authentication endpoints
	async login(credentials: LoginCredentials): Promise<TokenResponse> {
		try {
			// Convert to FormData for OAuth2PasswordRequestForm (backend expects this format)
			const formData = new FormData();
			formData.append("username", credentials.username);
			formData.append("password", credentials.password);

			const response = await authApiClient.post(
				"/api/v1/token",
				formData,
				{
					headers: {
						"Content-Type": "application/x-www-form-urlencoded",
					},
				}
			);

			// Backend returns: { access_token, refresh_token, token_type, amz_verify }
			return {
				access_token: response.data.access_token,
				refresh_token: response.data.refresh_token,
				token_type: response.data.token_type || "Bearer",
				amz_verify: response.data.amz_verify || false,
			};
		} catch (error) {
			console.error("Login failed:", error);
			throw error;
		}
	},

	async register(userData: RegisterCredentials): Promise<User> {
		const response = await authApiClient.post(
			"/api/v1/user-create",
			userData
		);
		return response.data;
	},

	async registerSeller(sellerData: SellerRegistrationData): Promise<User> {
		const response = await authApiClient.post(
			"/api/v1/seller-register",
			sellerData
		);
		return response.data;
	},

	async upgradeToSeller(sellerData: SellerOnboardingData): Promise<User> {
		const response = await authApiClient.post(
			"/api/v1/upgrade-to-seller",
			sellerData
		);
		return response.data;
	},

	// Profile endpoints
	async getProfile(): Promise<User> {
		const response = await authApiClient.get("/api/v1/me");
		return response.data;
	},

	async updateProfile(updates: Partial<User>): Promise<User> {
		const response = await authApiClient.put("/api/v1/users/me", updates);
		return response.data;
	},

	// Token management - using dedicated client to avoid interceptor conflicts
	async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
		// Use direct axios call to avoid interceptor conflicts
		const response = await fetch(`${getAuthBaseUrl()}/api/v1/refresh`, {
			method: "GET",
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${refreshToken}`,
			},
		});

		if (!response.ok) {
			throw new Error(`Refresh failed: ${response.status}`);
		}

		return response.json();
	},

	async validateToken(token: string): Promise<{
		message: string;
		success: boolean;
		token: string;
	}> {
		const response = await authApiClient.post("/api/v1/validate", {
			token: token,
		});
		return response.data;
	},

	// Health check
	async healthCheck(): Promise<{ status: string; service: string }> {
		const response = await authApiClient.get("/api/v1/healthz");
		return response.data;
	},
};

// Enhanced token management utilities
export const tokenUtils = {
	// Store tokens securely
	setTokens(accessToken: string, refreshToken?: string): void {
		tokenManager.setTokens(accessToken, refreshToken);

		// Dispatch event for state management
		if (typeof window !== "undefined") {
			window.dispatchEvent(
				new CustomEvent("auth:tokens-updated", {
					detail: { accessToken, refreshToken },
				})
			);
		}
	},

	// Get access token
	getAccessToken(): string | null {
		return tokenManager.getAccessToken();
	},

	// Get refresh token
	getRefreshToken(): string | null {
		return tokenManager.getRefreshToken();
	},

	// Clear all tokens
	clearTokens(): void {
		tokenManager.clearTokens();

		// Dispatch event for state management
		if (typeof window !== "undefined") {
			window.dispatchEvent(new CustomEvent("auth:tokens-cleared"));
		}
	},

	// Check if tokens exist
	hasTokens(): boolean {
		return tokenManager.hasValidTokens();
	},

	// Check if access token is expired (basic check)
	isAccessTokenExpired(): boolean {
		const token = this.getAccessToken();
		if (!token) return true;

		try {
			// Basic JWT payload extraction (not secure, just for expiry check)
			const payload = JSON.parse(atob(token.split(".")[1]));
			const currentTime = Math.floor(Date.now() / 1000);
			return payload.exp < currentTime;
		} catch {
			return true; // If we can't parse it, consider it expired
		}
	},

	// Auto-refresh token if needed
	async refreshIfNeeded(): Promise<boolean> {
		const refreshToken = this.getRefreshToken();

		if (!refreshToken) {
			console.log("No refresh token available");
			return false;
		}

		// Only refresh if access token is expired or missing
		if (!this.isAccessTokenExpired()) {
			return true; // Token is still valid
		}

		try {
			console.log("🔄 Refreshing expired token...");
			const response = await authApi.refreshToken(refreshToken);

			this.setTokens(response.access_token, response.refresh_token);
			console.log("✅ Token refresh successful");
			return true;
		} catch (error) {
			console.error("❌ Token refresh failed:", error);
			this.clearTokens();
			return false;
		}
	},

	// Initialize auth state from stored tokens
	async initializeAuth(): Promise<boolean> {
		if (!this.hasTokens()) {
			return false;
		}

		try {
			// Try to refresh if needed
			const isValid = await this.refreshIfNeeded();

			if (isValid) {
				// Don't fetch profile here - let React Query handle it
				// Just validate that tokens are valid
				return true;
			}

			return false;
		} catch (error) {
			console.error("Auth initialization failed:", error);
			this.clearTokens();
			return false;
		}
	},
};
