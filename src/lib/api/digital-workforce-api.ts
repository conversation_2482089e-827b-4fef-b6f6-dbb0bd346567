import { createClient } from "./client";
import { ApiError } from "@/types/auth";

const digitalWorkforceClient = createClient("https://app.clickbuy.ai/extension-backend/api/v1");

export interface CreateAgentRequest {
    agentName: string;
    specialization: "deal_sourcing" | "cross_selling" | "procurement" | "inventory" | "compliance_customer_service" | "finance";
    problemDescription: string;
    agentSteps: string;
    expectedOutput: string;
    escalationEmail: string;
}

export interface AgentResponse {
    id: string;
    agentName: string;
    specialization: string;
    problemDescription: string;
    agentSteps: string;
    expectedOutput: string;
    escalationEmail: string;
    userId: string;
    createdAt: string;
    updatedAt: string;
    status: string;
}

export interface GetAgentsParams {
    limit?: number;
    offset?: number;
}

export interface GetAgentsResponse {
    agents: AgentResponse[];
    total: number;
    limit: number;
    offset: number;
}

export class DigitalWorkforceAPI {
    static async createAgent(data: CreateAgentRequest): Promise<AgentResponse> {
        try {
            const response = await digitalWorkforceClient.post("/agent", data);
            return response.data;
        } catch (error) {
            if (error && typeof error === "object" && "detail" in error) {
                throw error as ApiError;
            }

            throw {
                detail: "Failed to create agent. Please try again.",
                status: 500,
            } as ApiError;
        }
    }

    static async getAgents(params: GetAgentsParams = {}): Promise<AgentResponse[]> {
        try {
            const { limit = 10, offset = 0 } = params;

            const response = await digitalWorkforceClient.get("/agent", {
                params: { limit, offset }
            });

            return response.data;
        } catch (error) {
            if (error && typeof error === "object" && "detail" in error) {
                throw error as ApiError;
            }

            throw {
                detail: "Failed to fetch agents. Please try again.",
                status: 500,
            } as ApiError;
        }
    }

    static async getAgent(agentId: string): Promise<AgentResponse> {
        try {
            if (!agentId || agentId.trim() === "") {
                throw {
                    detail: "Agent ID is required",
                    status: 400,
                } as ApiError;
            }

            const response = await digitalWorkforceClient.get(`/agent/${agentId}`);
            return response.data;
        } catch (error) {
            if (error && typeof error === "object" && "detail" in error) {
                throw error as ApiError;
            }

            throw {
                detail: "Failed to fetch agent. Please try again.",
                status: 500,
            } as ApiError;
        }
    }

    static validateAgentData(data: CreateAgentRequest): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        if (!data.agentName || data.agentName.trim().length < 2) {
            errors.push("Agent name must be at least 2 characters long");
        }

        const validSpecializations = [
			"deal_sourcing",
			"cross_selling",
			"procurement",
			"inventory",
			"compliance_customer_service",
			"finance",
		];
        if (!validSpecializations.includes(data.specialization)) {
            errors.push("Invalid specialization selected");
        }

        if (!data.problemDescription || data.problemDescription.trim().length < 10) {
            errors.push("Problem description must be at least 10 characters long");
        }

        if (!data.agentSteps || data.agentSteps.trim().length < 10) {
            errors.push("Agent steps must be at least 10 characters long");
        }

        if (!data.expectedOutput || data.expectedOutput.trim().length < 10) {
            errors.push("Expected output must be at least 10 characters long");
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!data.escalationEmail || !emailRegex.test(data.escalationEmail)) {
            errors.push("Valid email address is required");
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    static async getAgentStats(): Promise<{
        totalAgents: number;
        bySpecialization: Record<string, number>;
        byStatus: Record<string, number>;
    }> {
        try {
            const response = await digitalWorkforceClient.get("/agent/stats/summary");
            return response.data;
        } catch {
            return {
                totalAgents: 0,
                bySpecialization: {},
                byStatus: {}
            };
        }
    }
}

export const AgentUtils = {
    formatSpecialization(specialization: string): string {
    const map: Record<string, string> = {
        deal_sourcing: "Deal Sourcing",
        cross_selling: "Cross Selling",
        procurement: "Procurement",
        inventory: "Inventory",
        compliance_customer_service: "Compliance & Customer Service",
        finance: "Finance"
    };
    return map[specialization] || specialization;
},

    getSpecializationColor(specialization: string): string {
        const colors: Record<string, string> = {
            pricing: "bg-green-500",
            sourcing: "bg-blue-500",
            customer_services: "bg-purple-500"
        };
        return colors[specialization] || "bg-gray-500";
    },

    formatDate(dateString: string): string {
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString("en-US", {
                year: "numeric",
                month: "short",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit"
            });
        } catch {
            return "Invalid date";
        }
    },

    generateSummary(agent: AgentResponse): string {
        const specialization = this.formatSpecialization(agent.specialization);
        return `${specialization} agent that ${agent.problemDescription.slice(0, 100)}${
            agent.problemDescription.length > 100 ? "..." : ""
        }`;
    }
};