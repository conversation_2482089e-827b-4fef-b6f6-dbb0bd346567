/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from "axios";

const BASE_URL = "https://backend.clickbuydeals.com/api/v1/payment";
const TOKEN_BASE_URL = "https://backend.clickbuydeals.com/token/api/v1/token";

const getAuthHeaders = () => {
	const token = localStorage.getItem("auth_token");
	if (!token) {
		throw new Error("No access token found. Please log in again.");
	}
	return {
		headers: {
			Authorization: `Bearer ${token}`,
			"Content-Type": "application/json",
		},
	};
};

// Enhanced error handling function
const handleApiError = (error: any, context: string) => {
	console.error(`Stripe API Error (${context}):`, error);

	if (error.response) {
		const status = error.response.status;
		const message =
			error.response.data?.detail ||
			error.response.data?.message ||
			"Unknown server error";

		switch (status) {
			case 400:
				throw new Error(
					message ||
						"Invalid request. Please check your information and try again."
				);
			case 401:
				throw new Error("Authentication failed. Please log in again.");
			case 403:
				throw new Error(
					"Access denied. You don't have permission to perform this action."
				);
			case 404:
				throw new Error("Resource not found. Please try again.");
			case 409:
				throw new Error(
					"Conflict. This operation cannot be completed."
				);
			case 422:
				// Handle FastAPI validation errors
				if (Array.isArray(message)) {
					const errorMessages = message
						.map((d: any) => d.msg || d.message)
						.join(", ");
					throw new Error(`Validation error: ${errorMessages}`);
				}
				throw new Error(message || "Invalid request data");
			case 429:
				throw new Error(
					"Too many requests. Please wait a moment and try again."
				);
			case 500:
				throw new Error("Server error. Please try again later.");
			case 502:
			case 503:
			case 504:
				throw new Error(
					"Service temporarily unavailable. Please try again later."
				);
			default:
				throw new Error(message);
		}
	} else if (error.request) {
		throw new Error(
			"Network error. Please check your internet connection and try again."
		);
	} else {
		throw new Error(error.message || "An unexpected error occurred");
	}
};

// Type Definitions
export interface SetupIntentResponse {
	client_secret: string;
	setup_intent_id: string;
}

export interface PaymentMethod {
	id: string;
	stripe_payment_method_id: string;
	type: string;
	last4: string;
	brand: string;
	exp_month: number;
	exp_year: number;
	is_default: boolean;
	created_at: string;
	updated_at: string;
}

export interface PurchaseResponse {
	success: boolean;
	message: string;
	payment_id: string;
	stripe_payment_intent_id: string;
	amount_paid: number;
	currency: string;
	tokens_before: number;
	tokens_after: number;
	tokens_added: number;
	expires_at: string;
	plan_type: string;
}

export interface PaymentHistory {
	id: string;
	amount: number;
	currency: string;
	status: string;
	stripe_payment_intent_id: string;
	payment_method_id: string | null;
	description: string | null;
	created_at: string;
}

export interface ChargeRequest {
	amount: number;
	currency?: string;
	payment_method_id?: string;
	description?: string;
	metadata?: Record<string, string>;
}

// Setup Intent Functions
export const createSetupIntent = async (): Promise<SetupIntentResponse> => {
	try {
		const response = await axios.post(
			`${BASE_URL}/setup-intent`,
			{},
			getAuthHeaders()
		);

		if (!response.data?.client_secret || !response.data?.setup_intent_id) {
			throw new Error("Invalid setup intent response from server");
		}

		return response.data;
	} catch (error: any) {
		handleApiError(error, "createSetupIntent");
		throw error;
	}
};

export const confirmSetupIntent = async (
	setupIntentId: string
): Promise<void> => {
	try {
		if (!setupIntentId || setupIntentId.trim() === "") {
			throw new Error("Setup Intent ID is required");
		}

		const response = await axios.post(
			`${BASE_URL}/confirm-setup-intent`,
			{ setup_intent_id: setupIntentId.trim() },
			getAuthHeaders()
		);

		return response.data;
	} catch (error: any) {
		handleApiError(error, "confirmSetupIntent");
		throw error;
	}
};

// Payment Methods Functions
export const getPaymentMethods = async (): Promise<PaymentMethod[]> => {
	try {
		const response = await axios.get(
			`${BASE_URL}/payment-methods`,
			getAuthHeaders()
		);

		// Ensure we always return an array
		const paymentMethods = response.data || [];

		// Validate payment method structure
		return paymentMethods.filter(
			(method: any) =>
				method.stripe_payment_method_id &&
				method.id &&
				method.type &&
				method.brand &&
				method.last4
		);
	} catch (error: any) {
		handleApiError(error, "getPaymentMethods");
		throw error;
	}
};

export const setDefaultPaymentMethod = async (
	paymentMethodId: string
): Promise<void> => {
	try {
		if (!paymentMethodId || paymentMethodId.trim() === "") {
			throw new Error("Payment method ID is required");
		}

		await axios.patch(
			`${BASE_URL}/payment-methods/${paymentMethodId.trim()}/default`,
			{},
			getAuthHeaders()
		);
	} catch (error: any) {
		handleApiError(error, "setDefaultPaymentMethod");
		throw error;
	}
};

export const deletePaymentMethod = async (
	paymentMethodId: string
): Promise<void> => {
	try {
		if (!paymentMethodId || paymentMethodId.trim() === "") {
			throw new Error("Payment method ID is required");
		}

		await axios.delete(
			`${BASE_URL}/payment-methods/${paymentMethodId.trim()}`,
			getAuthHeaders()
		);
	} catch (error: any) {
		handleApiError(error, "deletePaymentMethod");
		throw error;
	}
};

// Payment Processing Functions
export const chargePaymentMethod = async (
	chargeData: ChargeRequest
): Promise<any> => {
	try {
		// Validate charge data
		if (!chargeData.amount || chargeData.amount <= 0) {
			throw new Error("Amount must be greater than 0");
		}

		const requestData = {
			amount: Math.round(chargeData.amount), // Ensure integer
			currency: chargeData.currency || "usd",
			payment_method_id: chargeData.payment_method_id,
			description: chargeData.description,
			metadata: chargeData.metadata || {},
		};

		const response = await axios.post(
			`${BASE_URL}/charge`,
			requestData,
			getAuthHeaders()
		);

		return response.data;
	} catch (error: any) {
		handleApiError(error, "chargePaymentMethod");
		throw error;
	}
};

export const getPaymentHistory = async (
	limit: number = 50,
	skip: number = 0
): Promise<PaymentHistory[]> => {
	try {
		const validLimit = Math.max(1, Math.min(limit, 100)); // Ensure limit is between 1 and 100
		const validSkip = Math.max(0, skip); // Ensure skip is not negative

		const response = await axios.get(
			`${BASE_URL}?limit=${validLimit}&skip=${validSkip}`,
			getAuthHeaders()
		);

		return response.data || [];
	} catch (error: any) {
		handleApiError(error, "getPaymentHistory");
		throw error;
	}
};

// Token Purchase Functions
export const purchaseTokens = async (
	plan_type: string,
	payment_method_id?: string
): Promise<PurchaseResponse> => {
	try {
		// Validate plan type
		const validPlanTypes = [
			"light",
			"basic",
			"standard",
			"ultimate",
			"custom",
		];
		if (!plan_type || !validPlanTypes.includes(plan_type.toLowerCase())) {
			throw new Error("Invalid plan type selected");
		}

		const requestData: any = {
			plan_type: plan_type.toLowerCase(), // Ensure lowercase
		};

		// Only include payment_method_id if it's provided and not empty
		if (payment_method_id && payment_method_id.trim() !== "") {
			requestData.payment_method_id = payment_method_id.trim();
		}

		console.log("Purchasing tokens with data:", requestData);

		const response = await axios.post(
			`${TOKEN_BASE_URL}/purchase`,
			requestData,
			getAuthHeaders()
		);

		console.log("Purchase response:", response.data);

		if (!response.data) {
			throw new Error("Invalid response from server");
		}

		return response.data;
	} catch (error: any) {
		console.error("Purchase error details:", {
			status: error.response?.status,
			data: error.response?.data,
			message: error.message,
		});

		// Handle specific 422 validation error
		if (error.response?.status === 422) {
			const details = error.response.data?.detail;
			if (Array.isArray(details)) {
				const errorMessages = details
					.map((d: any) => d.msg || d.message)
					.join(", ");
				throw new Error(`Validation error: ${errorMessages}`);
			} else {
				throw new Error(details || "Invalid request data");
			}
		}

		handleApiError(error, "purchaseTokens");
		throw error;
	}
};

export const chargeCustomPlan = async (
	amount: number,
	tokens: number,
	payment_method_id?: string
): Promise<PurchaseResponse> => {
	try {
		// Validate custom plan parameters
		if (!amount || amount <= 0) {
			throw new Error("Amount must be greater than 0");
		}

		if (!tokens || tokens < 10) {
			throw new Error("Minimum 10 tokens required for custom plan");
		}

		// The backend expects plan_type as an enum value, not a string
		// Based on your backend code, it seems to expect a specific format
		const requestData: any = {
			plan_type: "custom", // This should match the enum value exactly
			metadata: {
				tokens: tokens.toString(),
				custom_amount: Math.round(amount).toString(), // Ensure integer amount in cents
			},
		};

		// Only include payment_method_id if it's provided and not empty
		if (payment_method_id && payment_method_id.trim() !== "") {
			requestData.payment_method_id = payment_method_id.trim();
		}

		console.log("Charging custom plan with data:", requestData);

		const response = await axios.post(
			`${TOKEN_BASE_URL}/purchase`,
			requestData,
			getAuthHeaders()
		);

		console.log("Custom plan response:", response.data);

		if (!response.data) {
			throw new Error("Invalid response from server");
		}

		return response.data;
	} catch (error: any) {
		console.error("Custom plan error details:", {
			status: error.response?.status,
			data: error.response?.data,
			message: error.message,
		});

		// Handle specific 422 validation error
		if (error.response?.status === 422) {
			const details = error.response.data?.detail;
			if (Array.isArray(details)) {
				const errorMessages = details
					.map((d: any) => d.msg || d.message)
					.join(", ");
				throw new Error(`Validation error: ${errorMessages}`);
			} else {
				throw new Error(details || "Invalid request data");
			}
		}

		handleApiError(error, "chargeCustomPlan");
		throw error;
	}
};

// Utility Functions
export const validatePaymentMethodSelection = (
	paymentMethods: PaymentMethod[],
	selectedPaymentMethodId?: string
): boolean => {
	if (!paymentMethods || paymentMethods.length === 0) {
		return false;
	}

	if (!selectedPaymentMethodId) {
		return false;
	}

	return paymentMethods.some(
		(method) => method.stripe_payment_method_id === selectedPaymentMethodId
	);
};

export const getDefaultPaymentMethod = (
	paymentMethods: PaymentMethod[]
): PaymentMethod | null => {
	if (!paymentMethods || paymentMethods.length === 0) {
		return null;
	}

	return paymentMethods.find((method) => method.is_default) || null;
};

export const getPaymentMethodById = (
	paymentMethods: PaymentMethod[],
	paymentMethodId: string
): PaymentMethod | null => {
	if (!paymentMethods || paymentMethods.length === 0 || !paymentMethodId) {
		return null;
	}

	return (
		paymentMethods.find(
			(method) =>
				method.stripe_payment_method_id === paymentMethodId ||
				method.id === paymentMethodId
		) || null
	);
};

export const formatPaymentMethodDisplay = (method: PaymentMethod): string => {
	if (!method) return "Unknown payment method";

	const brand = method.brand.toUpperCase();
	const last4 = method.last4;
	const defaultLabel = method.is_default ? " (Default)" : "";

	return `${brand} ending in ${last4}${defaultLabel}`;
};

export const formatCurrency = (
	amountInCents: number,
	currency: string = "GBP"
): string => {
	const amount = amountInCents / 100;
	return new Intl.NumberFormat("en-GB", {
		style: "currency",
		currency: currency.toUpperCase(),
		minimumFractionDigits: 2,
	}).format(amount);
};

export const isPaymentMethodExpired = (method: PaymentMethod): boolean => {
	if (!method.exp_month || !method.exp_year) return false;

	const now = new Date();
	const currentYear = now.getFullYear();
	const currentMonth = now.getMonth() + 1; // JavaScript months are 0-indexed

	return (
		method.exp_year < currentYear ||
		(method.exp_year === currentYear && method.exp_month < currentMonth)
	);
};

export const isPaymentMethodExpiringSoon = (
	method: PaymentMethod,
	monthsAhead: number = 3
): boolean => {
	if (!method.exp_month || !method.exp_year) return false;

	const now = new Date();
	const warningDate = new Date(
		now.getFullYear(),
		now.getMonth() + monthsAhead,
		1
	);
	const expiryDate = new Date(method.exp_year, method.exp_month - 1, 1);

	return expiryDate <= warningDate;
};

// Health Check
export const checkPaymentServiceHealth = async (): Promise<boolean> => {
	try {
		const response = await axios.get(`${BASE_URL}/payments/health`, {
			headers: {
				"Content-Type": "application/json",
			},
			timeout: 5000, // 5 second timeout
		});

		return response.data?.status === "healthy";
	} catch (error) {
		console.warn("Payment service health check failed:", error);
		return false;
	}
};

// Export all functions for easy importing
export const stripeAPI = {
	// Setup Intent
	createSetupIntent,
	confirmSetupIntent,

	// Payment Methods
	getPaymentMethods,
	setDefaultPaymentMethod,
	deletePaymentMethod,

	// Payments
	chargePaymentMethod,
	getPaymentHistory,

	// Token Purchases
	purchaseTokens,
	chargeCustomPlan,

	// Utilities
	validatePaymentMethodSelection,
	getDefaultPaymentMethod,
	getPaymentMethodById,
	formatPaymentMethodDisplay,
	formatCurrency,
	isPaymentMethodExpired,
	isPaymentMethodExpiringSoon,

	// Health
	checkPaymentServiceHealth,
};
