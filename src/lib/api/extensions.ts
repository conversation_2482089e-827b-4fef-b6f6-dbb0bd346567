/* eslint-disable @typescript-eslint/no-explicit-any */
// src/lib/api/extensions.ts
import {
	ExtensionsDashboardData,
	ProductAnalysis,
	ExtensionSettings,
	AIWeights,
} from "@/types/extension";
import { extensionsDemoData } from "@/lib/data/extensionsDemo";

// Simulate API delay
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export const extensionsAPI = {
	// Get extensions dashboard data
	getDashboardData: async (): Promise<ExtensionsDashboardData> => {
		await delay(1000);
		return extensionsDemoData;
	},

	// Analyze product by ASIN
	analyzeProduct: async (asin: string): Promise<ProductAnalysis> => {
		await delay(2500); // Longer delay for AI analysis

		// Simulate token usage
		extensionsDemoData.tokenUsage.current += 50;
		extensionsDemoData.tokenUsage.usageHistory.unshift({
			date: new Date().toISOString().split("T")[0],
			used: 50,
			feature: "AI Analysis",
		});

		// Return mock analysis (in real app, this would be actual product data)
		const mockAnalysis: ProductAnalysis = {
			id: `analysis_${Date.now()}`,
			asin: asin,
			title: `Product Analysis for ${asin}`,
			brand: "Sample Brand",
			category: "Electronics",
			image: "/images/products/sample.jpg",
			currentPrice: Math.random() * 50 + 10,
			bsr: Math.floor(Math.random() * 100000) + 1000,
			rating: Math.random() * 2 + 3,
			reviewCount: Math.floor(Math.random() * 1000) + 50,
			profitability: {
				buyPrice: 0,
				sellPrice: 0,
				profit: 0,
				roi: 0,
				margin: 0,
			},
			competitorData: {
				totalSellers: Math.floor(Math.random() * 20) + 5,
				fbaSellers: 0,
				buyBoxWinner: "Amazon",
				priceRange: { min: 0, max: 0, avg: 0 },
			},
			trends: {
				priceHistory: [],
				salesRank: [],
				buyBoxHistory: [],
			},
			aiInsights: {
				profitabilityScore: Math.random() * 10,
				riskLevel: ["Low", "Medium", "High"][
					Math.floor(Math.random() * 3)
				] as any,
				recommendation: ["Buy", "Avoid", "Research More"][
					Math.floor(Math.random() * 3)
				] as any,
				reasons: ["Sample insight generated by AI"],
				marketTrend: "Stable",
				seasonality: "No strong seasonal pattern detected",
				competitionLevel: "Medium",
			},
			restrictions: {
				hazmat: false,
				oversized: false,
				gated: Math.random() > 0.7,
				ipClaims: false,
				variations: Math.random() > 0.5,
				meltable: false,
			},
			lastAnalyzed: new Date().toISOString(),
			analysisTime: 2500,
		};

		// Calculate profitability
		const sellPrice = mockAnalysis.currentPrice;
		const buyPrice = sellPrice * (0.6 + Math.random() * 0.3); // 60-90% of sell price
		const fbaFees = sellPrice * 0.15; // Approximate FBA fees
		const profit = sellPrice - buyPrice - fbaFees;
		const roi = (profit / buyPrice) * 100;
		const margin = (profit / sellPrice) * 100;

		mockAnalysis.profitability = {
			buyPrice: Number(buyPrice.toFixed(2)),
			sellPrice: Number(sellPrice.toFixed(2)),
			profit: Number(profit.toFixed(2)),
			roi: Number(roi.toFixed(1)),
			margin: Number(margin.toFixed(1)),
		};

		// Update competitor data
		mockAnalysis.competitorData.fbaSellers = Math.floor(
			mockAnalysis.competitorData.totalSellers * 0.7
		);
		mockAnalysis.competitorData.priceRange = {
			min: sellPrice * 0.9,
			max: sellPrice * 1.3,
			avg: sellPrice * 1.1,
		};

		// Add to recent analyses
		extensionsDemoData.recentAnalyses.unshift(mockAnalysis);
		if (extensionsDemoData.recentAnalyses.length > 10) {
			extensionsDemoData.recentAnalyses =
				extensionsDemoData.recentAnalyses.slice(0, 10);
		}

		return mockAnalysis;
	},

	// Bulk analyze products
	bulkAnalyze: async (
		asins: string[]
	): Promise<{
		success: ProductAnalysis[];
		failed: string[];
		tokensUsed: number;
	}> => {
		await delay(1000 * asins.length); // Simulate processing time

		const tokensUsed = asins.length * 30;
		extensionsDemoData.tokenUsage.current += tokensUsed;

		const success: ProductAnalysis[] = [];
		const failed: string[] = [];

		for (const asin of asins) {
			if (Math.random() > 0.1) {
				// 90% success rate
				const analysis = await extensionsAPI.analyzeProduct(asin);
				success.push(analysis);
			} else {
				failed.push(asin);
			}
		}

		return { success, failed, tokensUsed };
	},

	// Update extension settings
	updateSettings: async (
		settings: Partial<ExtensionSettings>
	): Promise<ExtensionSettings> => {
		await delay(500);

		Object.assign(extensionsDemoData.settings, settings);
		return extensionsDemoData.settings;
	},

	// Update AI weights
	updateAIWeights: async (weights: AIWeights): Promise<AIWeights> => {
		await delay(300);

		extensionsDemoData.settings.aiWeights = weights;
		return weights;
	},

	// Get product history
	getProductHistory: async (
		asin: string,
		days: number = 30
	): Promise<{
		priceHistory: { date: string; price: number; seller: string }[];
		salesRank: { date: string; rank: number }[];
		buyBoxHistory: {
			date: string;
			seller: string;
			price: number;
			percentage: number;
		}[];
	}> => {
		await delay(800);

		// Generate mock historical data
		const history = {
			priceHistory: [] as any[],
			salesRank: [] as any[],
			buyBoxHistory: [] as any[],
		};

		const basePrice = 20 + Math.random() * 30;
		const baseRank = 10000 + Math.random() * 50000;

		for (let i = days; i >= 0; i--) {
			const date = new Date();
			date.setDate(date.getDate() - i);
			const dateStr = date.toISOString().split("T")[0];

			history.priceHistory.push({
				date: dateStr,
				price: basePrice + (Math.random() - 0.5) * 5,
				seller: Math.random() > 0.5 ? "Amazon" : "ThirdParty",
			});

			history.salesRank.push({
				date: dateStr,
				rank: Math.floor(baseRank + (Math.random() - 0.5) * 20000),
			});

			history.buyBoxHistory.push({
				date: dateStr,
				seller: Math.random() > 0.3 ? "Amazon" : "ThirdParty",
				price: basePrice + (Math.random() - 0.5) * 3,
				percentage: Math.floor(Math.random() * 40) + 60,
			});
		}

		return history;
	},

	// Save product to watchlist
	saveToWatchlist: async (
		productId: string,
		watchlistId?: string
	): Promise<void> => {
		await delay(300);

		if (watchlistId) {
			const watchlist = extensionsDemoData.watchlist.find(
				(w) => w.id === watchlistId
			);
			if (watchlist && !watchlist.products.includes(productId)) {
				watchlist.products.push(productId);
			}
		} else {
			// Add to default watchlist or create new one
			const defaultWatchlist = extensionsDemoData.watchlist[0];
			if (
				defaultWatchlist &&
				!defaultWatchlist.products.includes(productId)
			) {
				defaultWatchlist.products.push(productId);
			}
		}
	},

	// Create new watchlist
	createWatchlist: async (name: string): Promise<string> => {
		await delay(200);

		const newWatchlist = {
			id: `watch_${Date.now()}`,
			name,
			products: [],
			alerts: true,
			createdDate: new Date().toISOString(),
		};

		extensionsDemoData.watchlist.push(newWatchlist);
		return newWatchlist.id;
	},

	// Get token usage details
	getTokenUsage: async (): Promise<ExtensionsDashboardData["tokenUsage"]> => {
		await delay(200);
		return extensionsDemoData.tokenUsage;
	},

	// Mark notifications as read
	markNotificationsRead: async (notificationIds: string[]): Promise<void> => {
		await delay(100);

		notificationIds.forEach((id) => {
			const notification = extensionsDemoData.notifications.find(
				(n) => n.id === id
			);
			if (notification) {
				notification.isRead = true;
			}
		});
	},

	// Export analysis data
	exportData: async (
		format: "csv" | "json" | "xlsx"
	): Promise<{ url: string; filename: string }> => {
		await delay(1500);

		return {
			url: `/api/export/analysis.${format}`,
			filename: `sway-analysis-${
				new Date().toISOString().split("T")[0]
			}.${format}`,
		};
	},

	// Get analytics for date range
	getAnalytics: async (): Promise<ExtensionsDashboardData["analytics"]> => {
		await delay(600);

		// In real implementation, this would filter analytics by date range
		return extensionsDemoData.analytics;
	},

	// Search products
	searchProducts: async (
		query: string,
		filters?: {
			category?: string;
			minPrice?: number;
			maxPrice?: number;
			minROI?: number;
			maxBSR?: number;
		}
	): Promise<ProductAnalysis[]> => {
		await delay(800);

		// Filter recent analyses based on query and filters
		let results = extensionsDemoData.recentAnalyses.filter(
			(product: ProductAnalysis) =>
				product.title.toLowerCase().includes(query.toLowerCase()) ||
				product.asin.toLowerCase().includes(query.toLowerCase()) ||
				product.brand.toLowerCase().includes(query.toLowerCase())
		);

		if (filters) {
			if (filters.category) {
				results = results.filter((p: ProductAnalysis) =>
					p.category.includes(filters.category!)
				);
			}
			if (filters.minPrice) {
				results = results.filter(
					(p: ProductAnalysis) => p.currentPrice >= filters.minPrice!
				);
			}
			if (filters.maxPrice) {
				results = results.filter(
					(p: ProductAnalysis) => p.currentPrice <= filters.maxPrice!
				);
			}
			if (filters.minROI) {
				results = results.filter(
					(p: ProductAnalysis) =>
						p.profitability.roi >= filters.minROI!
				);
			}
			if (filters.maxBSR) {
				results = results.filter(
					(p: ProductAnalysis) => p.bsr <= filters.maxBSR!
				);
			}
		}

		return results;
	},

	// Get competitor analysis
	getCompetitorAnalysis: async (): Promise<{
		competitors: {
			seller: string;
			price: number;
			rating: number;
			fulfillment: "FBA" | "FBM";
			buyBoxPercentage: number;
		}[];
		marketShare: {
			seller: string;
			percentage: number;
		}[];
		priceCompetitiveness: {
			rank: number;
			totalCompetitors: number;
			priceAdvantage: number;
		};
	}> => {
		await delay(1200);

		return {
			competitors: [
				{
					seller: "Amazon",
					price: 12.99,
					rating: 4.8,
					fulfillment: "FBA",
					buyBoxPercentage: 89,
				},
				{
					seller: "TechGear Official",
					price: 13.49,
					rating: 4.6,
					fulfillment: "FBA",
					buyBoxPercentage: 8,
				},
				{
					seller: "ElectroWorld",
					price: 14.99,
					rating: 4.2,
					fulfillment: "FBM",
					buyBoxPercentage: 2,
				},
				{
					seller: "GadgetStore",
					price: 15.49,
					rating: 4.4,
					fulfillment: "FBA",
					buyBoxPercentage: 1,
				},
			],
			marketShare: [
				{ seller: "Amazon", percentage: 89 },
				{ seller: "TechGear Official", percentage: 8 },
				{ seller: "Others", percentage: 3 },
			],
			priceCompetitiveness: {
				rank: 2,
				totalCompetitors: 23,
				priceAdvantage: -1.5, // 1.5% below average
			},
		};
	},

	// Refresh product data
	refreshProduct: async (asin: string): Promise<ProductAnalysis> => {
		await delay(1800);

		const existingProduct = extensionsDemoData.recentAnalyses.find(
			(p) => p.asin === asin
		);
		if (existingProduct) {
			// Update with fresh data
			existingProduct.lastAnalyzed = new Date().toISOString();
			existingProduct.currentPrice =
				existingProduct.currentPrice + (Math.random() - 0.5) * 2;
			existingProduct.bsr =
				existingProduct.bsr + Math.floor((Math.random() - 0.5) * 5000);

			// Use a few tokens for refresh
			extensionsDemoData.tokenUsage.current += 20;
			extensionsDemoData.tokenUsage.usageHistory.unshift({
				date: new Date().toISOString().split("T")[0],
				used: 20,
				feature: "Product Refresh",
			});

			return existingProduct;
		} else {
			// If not found, do a full analysis
			return await extensionsAPI.analyzeProduct(asin);
		}
	},

	// Get AI insights for multiple products
	getBulkAIInsights: async (
		asins: string[]
	): Promise<{
		[asin: string]: {
			score: number;
			recommendation: "Buy" | "Avoid" | "Research More";
			confidence: number;
			reasons: string[];
		};
	}> => {
		await delay(2000);

		const insights: any = {};
		asins.forEach((asin) => {
			const score = Math.random() * 10;
			insights[asin] = {
				score: Number(score.toFixed(1)),
				recommendation:
					score > 7 ? "Buy" : score > 4 ? "Research More" : "Avoid",
				confidence: Math.random() * 0.3 + 0.7, // 70-100% confidence
				reasons: [
					"AI analysis based on current market conditions",
					"Historical data suggests stable performance",
					"Competitive landscape analysis completed",
				],
			};
		});

		// Use tokens for AI insights
		extensionsDemoData.tokenUsage.current += asins.length * 40;

		return insights;
	},

	// Update extension status
	updateExtensionStatus: async (
		status: "Active" | "Inactive" | "Paused"
	): Promise<void> => {
		await delay(300);
		extensionsDemoData.profile.status = status;
	},

	// Get usage statistics
	getUsageStats: async (
		period: "7d" | "30d" | "90d" | "1y"
	): Promise<{
		scans: { date: string; count: number }[];
		profits: { date: string; amount: number }[];
		accuracy: { date: string; rate: number }[];
		tokens: { date: string; used: number }[];
	}> => {
		await delay(500);

		const days =
			period === "7d"
				? 7
				: period === "30d"
				? 30
				: period === "90d"
				? 90
				: 365;
		const stats = {
			scans: [] as any[],
			profits: [] as any[],
			accuracy: [] as any[],
			tokens: [] as any[],
		};

		for (let i = days; i >= 0; i--) {
			const date = new Date();
			date.setDate(date.getDate() - i);
			const dateStr = date.toISOString().split("T")[0];

			stats.scans.push({
				date: dateStr,
				count: Math.floor(Math.random() * 50) + 20,
			});

			stats.profits.push({
				date: dateStr,
				amount: Math.floor(Math.random() * 1000) + 500,
			});

			stats.accuracy.push({
				date: dateStr,
				rate: Math.random() * 10 + 85, // 85-95%
			});

			stats.tokens.push({
				date: dateStr,
				used: Math.floor(Math.random() * 200) + 100,
			});
		}

		return stats;
	},
};
