/* eslint-disable @typescript-eslint/no-explicit-any */
// lib/api/token.ts
import axios from "axios";

const BASE_URL = "https://backend.clickbuydeals.com/token/api/v1/token";

const getAuthHeaders = () => {
	const token = localStorage.getItem("auth_token");
	if (!token) {
		throw new Error("No access token found. Please log in again.");
	}
	return {
		headers: {
			Authorization: `Bearer ${token}`,
			"Content-Type": "application/json",
		},
	};
};

// API Response Types
export interface TokenBalance {
	user_id: string;
	current_tokens: number;
	expires_at: string | null;
	is_expired: boolean;
	plan_type: string;
	days_remaining: number;
}

export interface TokenHistoryItem {
	user_id: string;
	action: string;
	tokens_added: number;
	tokens_before: number;
	tokens_after: number;
	expires_at: string;
	stripe_payment_intent_id?: string;
	plan_type: string;
	created_at: string;
}

export interface UsageHistoryItem {
	user_id: string;
	service_name: string;
	tokens_used: number;
	tokens_remaining: number;
	request_details: Record<string, any>;
	created_at: string;
}

export interface TokenHistoryResponse {
	user_id: string;
	history: TokenHistoryItem[];
}

export interface UsageHistoryResponse {
	user_id: string;
	usage_history: UsageHistoryItem[];
}

export interface TokenUsageRequest {
	service_name: string;
	tokens_to_use: number;
	request_details?: Record<string, any>;
}

export interface TokenUsageResponse {
	success: boolean;
	tokens_remaining: number;
	message: string;
}

// Enhanced error handling function
const handleApiError = (error: any, context: string) => {
	console.error(`Token API Error (${context}):`, error);

	if (error.response) {
		// Server responded with error status
		const status = error.response.status;
		const message =
			error.response.data?.detail ||
			error.response.data?.message ||
			"Unknown server error";

		switch (status) {
			case 401:
				throw new Error("Authentication failed. Please log in again.");
			case 403:
				throw new Error(
					"Access denied. You don't have permission to perform this action."
				);
			case 404:
				throw new Error("Service not found. Please try again later.");
			case 429:
				throw new Error(
					"Rate limit exceeded. Please wait a moment and try again."
				);
			case 500:
				throw new Error("Server error. Please try again later.");
			default:
				throw new Error(message);
		}
	} else if (error.request) {
		// Network error
		throw new Error(
			"Network error. Please check your internet connection and try again."
		);
	} else {
		// Other error
		throw new Error(error.message || "An unexpected error occurred");
	}
};

// API Functions
export const tokenAPI = {
	// Get current token balance
	getBalance: async (): Promise<TokenBalance> => {
		try {
			const response = await axios.get(
				`${BASE_URL}/balance`,
				getAuthHeaders()
			);
			return response.data;
		} catch (error) {
			handleApiError(error, "getBalance");
			throw error; // This won't be reached due to handleApiError throwing, but TypeScript needs it
		}
	},

	// Get token purchase/renewal history
	getTokenHistory: async (
		limit: number = 50
	): Promise<TokenHistoryResponse> => {
		try {
			// Validate limit parameter
			const validLimit = Math.max(1, Math.min(limit, 100)); // Ensure limit is between 1 and 100

			const response = await axios.get(
				`${BASE_URL}/history/tokens?limit=${validLimit}`,
				getAuthHeaders()
			);
			return response.data;
		} catch (error) {
			handleApiError(error, "getTokenHistory");
			throw error;
		}
	},

	// Get token usage history
	getUsageHistory: async (
		limit: number = 100
	): Promise<UsageHistoryResponse> => {
		try {
			// Validate limit parameter
			const validLimit = Math.max(1, Math.min(limit, 200)); // Ensure limit is between 1 and 200

			const response = await axios.get(
				`${BASE_URL}/history/usage?limit=${validLimit}`,
				getAuthHeaders()
			);
			return response.data;
		} catch (error) {
			handleApiError(error, "getUsageHistory");
			throw error;
		}
	},

	// Use tokens for a service
	useTokens: async (
		request: TokenUsageRequest
	): Promise<TokenUsageResponse> => {
		try {
			// Validate request parameters
			if (!request.service_name || request.service_name.trim() === "") {
				throw new Error("Service name is required");
			}

			if (!request.tokens_to_use || request.tokens_to_use <= 0) {
				throw new Error("Tokens to use must be greater than 0");
			}

			const requestData = {
				service_name: request.service_name.trim(),
				tokens_to_use: Math.floor(request.tokens_to_use), // Ensure integer
				request_details: request.request_details || {},
			};

			const response = await axios.post(
				`${BASE_URL}/use`,
				requestData,
				getAuthHeaders()
			);
			return response.data;
		} catch (error) {
			handleApiError(error, "useTokens");
			throw error;
		}
	},

	// Health check for token service
	healthCheck: async (): Promise<{ status: string; service: string }> => {
		try {
			const response = await axios.get(`${BASE_URL}/health`, {
				headers: {
					"Content-Type": "application/json",
				},
				timeout: 5000, // 5 second timeout for health check
			});
			return response.data;
		} catch (error) {
			console.warn("Token service health check failed:", error);
			throw new Error("Token service is currently unavailable");
		}
	},
};

// Utility functions for token management
export const tokenUtils = {
	// Format token count with proper localization
	formatTokenCount: (tokens: number): string => {
		return new Intl.NumberFormat("en-US").format(tokens);
	},

	// Calculate estimated token value
	calculateTokenValue: (
		tokens: number,
		costPerToken: number = 0.002
	): number => {
		return tokens * costPerToken;
	},

	// Format token value as currency
	formatTokenValue: (
		tokens: number,
		costPerToken: number = 0.002
	): string => {
		const value = tokenUtils.calculateTokenValue(tokens, costPerToken);
		return new Intl.NumberFormat("en-US", {
			style: "currency",
			currency: "USD",
			minimumFractionDigits: 3,
		}).format(value);
	},

	// Check if tokens are low
	isTokensLow: (currentTokens: number, threshold: number = 150): boolean => {
		return currentTokens < threshold;
	},

	// Check if tokens are critically low
	isTokensCritical: (
		currentTokens: number,
		threshold: number = 50
	): boolean => {
		return currentTokens < threshold;
	},

	// Calculate days until expiration
	getDaysUntilExpiration: (expiresAt: string | null): number => {
		if (!expiresAt) return Infinity;

		const expirationDate = new Date(expiresAt);
		const now = new Date();
		const diffTime = expirationDate.getTime() - now.getTime();
		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

		return Math.max(0, diffDays);
	},

	// Check if tokens are expired
	isTokensExpired: (expiresAt: string | null): boolean => {
		if (!expiresAt) return false;
		return new Date(expiresAt) < new Date();
	},

	// Format expiration date
	formatExpirationDate: (expiresAt: string | null): string => {
		if (!expiresAt) return "Never expires";

		return new Date(expiresAt).toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
		});
	},

	// Get plan display name
	getPlanDisplayName: (planType: string): string => {
		const planNames: Record<string, string> = {
			free: "Free",
			light: "Light",
			basic: "Basic",
			standard: "Standard",
			ultimate: "Ultimate",
			custom: "Custom",
		};

		return (
			planNames[planType.toLowerCase()] ||
			planType.charAt(0).toUpperCase() + planType.slice(1)
		);
	},

	// Validate service name
	isValidServiceName: (serviceName: string): boolean => {
		const validServices = [
			"selectoria",
			"price_history",
			"competitor_scan",
			"product_analysis",
			"market_research",
		];

		return validServices.includes(serviceName.toLowerCase());
	},
};

// Token service status
export const getTokenServiceStatus = async (): Promise<boolean> => {
	try {
		await tokenAPI.healthCheck();
		return true;
	} catch {
		return false;
	}
};
