/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, {
	AxiosInstance,
	InternalAxiosRequestConfig,
	AxiosError,
} from "axios";
import { ApiError } from "@/types/auth";

let isAuthenticatedState = false;

export const updateAuthState = (isAuthenticated: boolean) => {
	isAuthenticatedState = isAuthenticated;
};

let isRefreshing = false;
let failedQueue: Array<{
	resolve: (value?: any) => void;
	reject: (error?: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
	failedQueue.forEach(({ resolve, reject }) => {
		if (error) {
			reject(error);
		} else {
			resolve(token);
		}
	});
	failedQueue = [];
};

// Token management utilities - centralized
const tokenManager = {
	getAccessToken(): string | null {
		if (typeof window === "undefined") return null;
		return localStorage.getItem("auth_token");
	},

	getRefreshToken(): string | null {
		if (typeof window === "undefined") return null;
		return localStorage.getItem("refresh_token");
	},

	setTokens(accessToken: string, refreshToken?: string): void {
		if (typeof window === "undefined") return;
		localStorage.setItem("auth_token", accessToken);
		if (refreshToken) {
			localStorage.setItem("refresh_token", refreshToken);
		}
	},

	clearTokens(): void {
		if (typeof window === "undefined") return;
		localStorage.removeItem("auth_token");
		localStorage.removeItem("refresh_token");
		localStorage.removeItem("guest_session_id");
	},

	hasValidTokens(): boolean {
		return !!(this.getAccessToken() && this.getRefreshToken());
	},
};

// Create a separate client for auth operations (no interceptors)
const createAuthAxiosClient = (baseURL?: string): AxiosInstance => {
	return axios.create({
		baseURL,
		timeout: 10000,
		headers: {
			"Content-Type": "application/json",
		},
	});
};

// Create a general API client factory
const createApiClient = (baseURL?: string): AxiosInstance => {
	const client = axios.create({
		baseURL,
		timeout: 10000,
		headers: {
			"Content-Type": "application/json",
		},
	});

	// Request interceptor - Add auth token or guest session
	client.interceptors.request.use(
		(config: InternalAxiosRequestConfig) => {
			const token = tokenManager.getAccessToken();
			const guestSession =
				typeof window !== "undefined"
					? localStorage.getItem("guest_session_id")
					: null;

			// Priority: Auth token > Guest session
			if (token) {
				config.headers = config.headers || {};
				config.headers["Authorization"] = `Bearer ${token}`;
				// Remove guest session if authenticated
				delete config.headers["X-Session-ID"];
			} else if (guestSession && !isAuthenticatedState) {
				// Only use guest session if not authenticated
				config.headers = config.headers || {};
				config.headers["X-Session-ID"] = guestSession;
			}

			return config;
		},
		(error) => {
			return Promise.reject(error);
		}
	);

	// Response interceptor - Handle errors globally
	client.interceptors.response.use(
		(response) => {
			return response;
		},
		async (error: AxiosError) => {
			const originalRequest =
				error.config as InternalAxiosRequestConfig & {
					_retry?: boolean;
				};

			// Handle 401 errors with token refresh
			if (error.response?.status === 401 && !originalRequest._retry) {
				// Prevent infinite retry loops
				originalRequest._retry = true;

				if (isRefreshing) {
					// Queue the request if refresh is already in progress
					return new Promise((resolve, reject) => {
						failedQueue.push({ resolve, reject });
					})
						.then((token) => {
							if (originalRequest.headers && token) {
								originalRequest.headers.Authorization = `Bearer ${token}`;
							}
							return client(originalRequest);
						})
						.catch((err) => {
							return Promise.reject(err);
						});
				}

				const refreshToken = tokenManager.getRefreshToken();

				if (refreshToken && tokenManager.hasValidTokens()) {
					isRefreshing = true;

					try {
						// Create completely fresh axios instance for refresh - NO interceptors
						const authClient = axios.create({
							baseURL: "https://app.clickbuy.ai/auth",
							timeout: 10000,
							headers: {
								"Content-Type": "application/json",
								Authorization: `Bearer ${refreshToken}`, // Backend expects Bearer token
							},
						});

						console.log("🔁 Refreshing token...");

						const response = await authClient.get(
							"/api/v1/refresh"
						);
						const { access_token, refresh_token: newRefreshToken } =
							response.data;

						// Update tokens
						tokenManager.setTokens(access_token, newRefreshToken);

						// Process queued requests
						processQueue(null, access_token);

						// Retry original request with new token
						if (originalRequest.headers) {
							originalRequest.headers.Authorization = `Bearer ${access_token}`;
						}

						return client(originalRequest);
					} catch (refreshError) {
						console.error("🚨 Token refresh failed:", refreshError);

						// Clear tokens and process queue with error
						processQueue(refreshError, null);
						tokenManager.clearTokens();
						updateAuthState(false);

						// Redirect to login only if not already on login page
						if (
							typeof window !== "undefined" &&
							!window.location.pathname.includes("/login") &&
							window.location.pathname !== "/"
						) {
							window.location.href = "/login";
						}

						return Promise.reject(refreshError);
					} finally {
						isRefreshing = false;
					}
				} else {
					// No refresh token available, clear everything
					tokenManager.clearTokens();
					updateAuthState(false);

					if (
						typeof window !== "undefined" &&
						!window.location.pathname.includes("/login") &&
						window.location.pathname !== "/"
					) {
						window.location.href = "/login";
					}
				}
			}

			// Handle 403 errors (insufficient permissions)
			if (error.response?.status === 403) {
				console.warn("⚠️ Insufficient permissions for this action");

				if (typeof window !== "undefined") {
					window.dispatchEvent(
						new CustomEvent("auth:insufficient-permissions", {
							detail: {
								...(typeof error.response.data === "object" &&
								error.response.data !== null
									? error.response.data
									: {}),
							},
						})
					);
				}
			}

			// Handle guest session errors (400 with session-related message)
			if (
				error.response?.status === 400 &&
				typeof error.response.data === "object" &&
				error.response.data !== null &&
				"detail" in error.response.data
			) {
				const data = error.response.data as { detail: string };
				if (
					typeof data.detail === "string" &&
					data.detail.toLowerCase().includes("session")
				) {
					if (typeof window !== "undefined") {
						localStorage.removeItem("guest_session_id");
					}
				}
			}

			// Transform error to our ApiError format
			let detail: string | undefined;
			if (
				error.response?.data &&
				typeof error.response.data === "object" &&
				"detail" in error.response.data
			) {
				detail = (error.response.data as { detail?: string }).detail;
			}

			const apiError: ApiError = {
				detail: detail || error.message || "An error occurred",
				status: error.response?.status,
			};

			return Promise.reject(apiError);
		}
	);

	return client;
};

// Export the factory function for custom base URLs
export const createClient = createApiClient;

// Export auth client factory (for auth operations without interceptors)
export const createAuthClient = createAuthAxiosClient;

// Export token manager for external use
export { tokenManager };

// Export default client without base URL (for backward compatibility)
const apiClient = createApiClient();
export default apiClient;
