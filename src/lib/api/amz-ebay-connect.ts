/* eslint-disable @typescript-eslint/no-explicit-any */
import { createClient } from "./client";

export const getAmazonAuthUrl = async (
	region: string = "uk",
	testMode = false
) => {
	try {
		const apiClient = createClient("https://app.clickbuy.ai/amazon");

		const response = await apiClient.get("/api/v1/region", {
			params: {
				region,
				test_mode: testMode,
			},
		});

		if (response.data && typeof response.data === "string") {
			const authUrl = response.data.replace(/^"|"$/g, "");
			return { auth_url: authUrl };
		}

		throw new Error("Amazon authorization URL not found in response");
	} catch (error: any) {
		if (error.detail) {
			throw new Error(`Amazon API error: ${error.detail}`);
		}

		throw new Error(
			`Failed to get Amazon authorization URL: ${
				error.message || "Unknown error"
			}`
		);
	}
};

export const getEbayAuthUrl = async () => {
	try {
		const apiClient = createClient("https://app.clickbuy.ai/amazon");

		const response = await apiClient.get("/api/v1/ebay/ebay/auth");

		if (
			response.data &&
			typeof response.data === "string" &&
			response.data.startsWith("https://auth.ebay.com")
		) {
			const authUrl = response.data.replace(/^"|"$/g, "");
			return { auth_url: authUrl };
		} else {
			throw new Error("Unexpected response format for eBay auth URL");
		}
	} catch (error: any) {
		if (error.detail) {
			throw new Error(`eBay API error: ${error.detail}`);
		}

		throw new Error(
			`Failed to get eBay authorization URL: ${
				error.message || "Unknown error"
			}`
		);
	}
};
